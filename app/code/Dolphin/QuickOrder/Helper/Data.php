<?php

namespace Dolphin\QuickOrder\Helper;

use <PERSON>gento\Customer\Model\SessionFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Data\Form\FormKey;
use Magento\Framework\Escaper;
use Magento\Framework\Module\Manager;
use Magento\Search\Model\QueryFactory;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class Data extends AbstractHelper
{
    const XML_PATH_ENABLE = 'quickorder/general/enable';

    const XML_PATH_DISPLAY_TITLE = 'quickorder/general/display_title';

    const XML_PATH_TITLE = 'quickorder/general/title';

    const XML_PATH_URL_KEY = 'quickorder/general/url_key';

    const XML_PATH_DISPLAY_LINK_IN = 'quickorder/general/display_link_in';

    const XML_PATH_CUSTOMER_REQUIRED = 'quickorder/general/customer_required';

    const XML_PATH_AUTOCOMPLETE_MINI_CHAR = 'quickorder/general/autocomplete_minimum_characters';

    const XML_PATH_LIMIT_SEARCH_RESULT = 'quickorder/general/limit_search_result';

    const XML_PATH_REQUISITION_LISTS = 'requisitionlist/general_option/enable_module';

    const XML_PATH_ENABLE_REQUISITION_LISTS = 'quickorder/general/enable_req';

    protected $_escaper;

    protected $_scopeConfig;

    protected $_storeManager;

    protected $_customerSessionFactory;

    protected $_moduleManager;

    protected $formKey;

    public function __construct(
        Context $context,
        Escaper $escaper,
        ScopeConfigInterface $scopeConfig,
        StoreManagerInterface $storeManager,
        Manager $moduleManager,
        FormKey $formKey,
        SessionFactory $customerSessionFactory
    ) {
        $this->_escaper = $escaper;
        $this->_scopeConfig = $scopeConfig;
        $this->_storeManager = $storeManager;
        $this->_moduleManager = $moduleManager;
        $this->formKey = $formKey;
        $this->_customerSessionFactory = $customerSessionFactory;
        parent::__construct($context);
    }

    public function getSuggestUrl()
    {
        return $this->_getUrl(
            'quickorder/ajax/suggest',
            ['_secure' => $this->_getRequest()->isSecure()]
        );
    }

    public function getEscapedQueryText()
    {
        return $this->_escaper->escapeHtml(
            $this->getPreparedQueryText($this->getQueryText())
        );
    }

    private function getPreparedQueryText($queryText)
    {
        return $queryText;
    }

    private function getQueryText()
    {
        $queryText = $this->_request->getParam($this->getQueryParamName());
        return ($queryText === null || is_array($queryText))
        ? ''
        : $this->string->cleanString(trim($queryText));
    }

    public function getQueryParamName()
    {
        return QueryFactory::QUERY_VAR_NAME;
    }

    public function getBaseUrl()
    {
        return $this->_storeManager->getStore()->getBaseUrl();
    }

    public function getAddtoListUrl()
    {
        $link = "quickorder/ajax/addquickorder";
        return $this->_storeManager->getStore()->getUrl($link);
    }

    public function setListUrl()
    {
        $link = 'requisitionlist/index/addlist';
        return $this->_storeManager->getStore()->getUrl($link);
    }

    public function getEditListUrl($id)
    {
        $link = $this->_storeManager->getStore()->getUrl("requisitionlist/index/editlist");
        $link .= "reqid/" . $id;
        return $link;
    }

    public function getFormKey()
    {
        return $this->formKey->getFormKey();
    }

    public function getMediaUrl()
    {
        return $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
    }

    public function isEnabled()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_ENABLE, ScopeInterface::SCOPE_STORE);
    }

    public function isRequisitionLists()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_REQUISITION_LISTS, ScopeInterface::SCOPE_STORE);
    }

    public function getDisplayTitle()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_DISPLAY_TITLE, ScopeInterface::SCOPE_STORE);
    }

    public function getTitle()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_TITLE, ScopeInterface::SCOPE_STORE);
    }

    public function getQsOrderUrl()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_URL_KEY, ScopeInterface::SCOPE_STORE);
    }

    public function getCustomerRequired()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_CUSTOMER_REQUIRED, ScopeInterface::SCOPE_STORE);
    }

    public function getQsRequisitionlistStatus()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_ENABLE_REQUISITION_LISTS, ScopeInterface::SCOPE_STORE);
    }

    public function getPageLitmit()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_LIMIT_SEARCH_RESULT, ScopeInterface::SCOPE_STORE);
    }

    public function getDisplayLink()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_DISPLAY_LINK_IN, ScopeInterface::SCOPE_STORE);
    }

    public function getMinSearchChar()
    {
        return $this->_scopeConfig->getValue(self::XML_PATH_AUTOCOMPLETE_MINI_CHAR, ScopeInterface::SCOPE_STORE);
    }

    public function isCustomerLoggedIn()
    {
        $flag = 0;
        $customerSession = $this->getCustomerSession();
        if ($customerSession->isLoggedIn()) {
            $flag = 1;
        }
        return $flag;
    }

    public function getCustomerSession()
    {
        return $this->_customerSessionFactory->create();
    }

    public function getModuleStatus()
    {
        $flag = 0;
        if ($this->_moduleManager->isOutputEnabled('Dolphin_Requisitionlist')) {
            $flag = 1;
        }
        return $flag;
    }

    public function getQuickOrderUrl()
    {
        $key = $this->getQsOrderUrl();
        return $this->_storeManager->getStore()->getUrl($key);
    }
}

<?php
$option_data = $block->getData('optionsdata');
if ($option_data->getPrdAddData()) {
    $links_data = json_decode($option_data->getPrdAddData(), true);
} else {
    $links_data = [];
}
?>
<?php /* @var $block \Magento\Downloadable\Block\Catalog\Product\Links */ ?>
<?php $_linksPurchasedSeparately = $block->getLinksPurchasedSeparately(); ?>
<?php if ($block->getProduct()->isSaleable() && $block->hasLinks()):?>
    <?php $_links = $block->getLinks(); ?>
    <?php $_linksLength = 0; ?>
    <?php $_isRequired = $block->getLinkSelectionRequired(); ?>
    <div class="field downloads<?php if ($_isRequired) { echo ' required'; } ?>
        <?php if (!$_linksPurchasedSeparately) { echo ' downloads-no-separately'; } ?>">
        <label class="label"><span><?= $block->escapeHtml($block->getLinksTitle()) ?></span></label>
        <div class="control" id="downloadable-links-list"
             data-mage-init='{"downloadable":{
                 "linkElement":"input:checkbox[value]",
                 "allElements":"#links_all",
                 "config":<?= $block->escapeHtmlAttr($block->getJsonConfig()) ?>}
             }'
             data-container-for="downloadable-links">
            <?php foreach ($_links as $_link): ?>
                <?php
                // checked option is selected or not
                $checked_val = "";
                if (!empty($links_data)) {
                    if (in_array($_link->getId(), $links_data)) {
                        $checked_val = "checked";
                    } else {
                        $checked_val = "";
                    }
                }
                ?>
                <?php $_linksLength++;?>
                <div class="field choice" data-role="link">
                    <?php if ($_linksPurchasedSeparately): ?>
                        <input type="checkbox"
                               <?php if ($_isRequired): ?>
                                data-validate="{'validate-one-checkbox-required-by-name':'downloadable-links-list'}"
                                <?php endif; ?>
                               name="links[]"
                               id="links_<?= $block->escapeHtmlAttr($_link->getId()) ?>"
                               value="<?= $block->escapeHtmlAttr($_link->getId()) ?>"
                               <?= /* @noEscape */ $checked_val ?> />
                    <?php endif; ?>
                    <label class="label" for="links_<?= $block->escapeHtmlAttr($_link->getId()) ?>">
                        <span><?= $block->escapeHtml($_link->getTitle()) ?></span>
                        <?php if ($_link->getSampleFile() || $_link->getSampleUrl()): ?>
                            <a class="sample link"
                               href="<?= $block->escapeUrl($block->getLinkSampleUrl($_link)) ?>"
                               <?= $block->getIsOpenInNewWindow() ? 'target="_blank"' : '' ?>>
                                <?= $block->escapeHtml(__('sample')) ?>
                            </a>
                        <?php endif; ?>
                        <?php if ($_linksPurchasedSeparately): ?>
                            <?= /* @noEscape */ $block->getLinkPrice($_link) ?>
                        <?php endif; ?>
                    </label>
                </div>
            <?php endforeach; ?>
        </div>
        <?php if ($_isRequired): ?>
            <span id="links-advice-container"></span>
        <?php endif;?>
    </div>
<?php endif; ?>

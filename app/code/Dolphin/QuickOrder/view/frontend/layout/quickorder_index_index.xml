<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
	xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<head>
		<css src="Dolphin_QuickOrder::css/quickorder.css" />
	</head>
    <body>
        <referenceContainer name="content">
            <block class="Dolphin\QuickOrder\Block\Index\Index"
            	   name="quicksearch.order"
            	   template="Dolphin_QuickOrder::quickorder.phtml"
            	   ifconfig="quickorder/general/enable"
            	   cacheable="false"/>
        </referenceContainer>
    </body>
</page>
<?php

namespace Dolphin\QuickOrder\Controller\Ajax;

use Dolphin\QuickOrder\Helper\Data;
use Dolphin\QuickOrder\Model\RequisitionListFactory as GetListNameData;
use Dolphin\QuickOrder\Model\RequisitionProductFactory as AddListData;
use Dolphin\QuickOrder\Model\ResourceModel\QuicksearchCustomer\CollectionFactory as QuicksearchCustomerFactory;
use Dolphin\QuickOrder\Model\ResourceModel\RequisitionProduct\CollectionFactory as ListCollection;
use Magento\Catalog\Model\ProductFactory;
use Magento\Framework\App\Action\Action;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\JsonFactory;

class AddQuickOrder extends \Magento\Framework\App\Action\Action
{
    protected $_resultFactory;
    protected $_qsCustomerModelFactory;
    protected $_requisitionProductFactory;
    protected $_addListData;
    protected $_request;
    protected $_resultJsonFactory;
    protected $_dataHelper;
    protected $_productloader;
    protected $_listnameData;

    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\App\Request\Http $request,
        \Magento\Framework\View\Result\PageFactory $resultFactory,
        QuicksearchCustomerFactory $qsCustomerModelFactory,
        ListCollection $requisitionProductFactory,
        AddListData $addListData,
        JsonFactory $resultJsonFactory,
        ProductFactory $productloader,
        GetListNameData $listnameData,
        Data $dataHelper
    ) {
        $this->_request = $request;
        $this->_resultFactory = $resultFactory;
        $this->_qsCustomerModelFactory = $qsCustomerModelFactory;
        $this->_requisitionProductFactory = $requisitionProductFactory;
        $this->_addListData = $addListData;
        $this->_resultJsonFactory = $resultJsonFactory;
        $this->_dataHelper = $dataHelper;
        $this->_productloader = $productloader;
        $this->_listnameData = $listnameData;
        parent::__construct($context);
    }

    public function execute()
    {
        $resultPage = $this->_resultFactory->create(ResultFactory::TYPE_JSON);
        $listId = $this->_request->getParam('listid');
        $custId = $this->_request->getParam('custid');
        $data = $this->_request->getParams();
        $backUrl = $this->_redirect->getRefererUrl();
        if ((!$listId) || (!$custId)) {
            $this->messageManager->addError(__('Invalid url.'));
        }

        // check customer is active or not
        $isCustomerLoggedIn = $this->_dataHelper->isCustomerLoggedIn();
        if ($isCustomerLoggedIn == 1) {
            try {
                $resultJson = $this->_resultJsonFactory->create();
                $quickOrderModel = $this->_qsCustomerModelFactory->create();
                $quickOrderModel->addFieldToFilter('customer_id', $custId);
                foreach ($quickOrderModel as $quickOrderKey => $quickOrderValue) {
                    $this->setListProduct($quickOrderValue, $listId);
                }
                // check quick order data
                if (count($quickOrderModel->getData())) {
                    $listNameData = $this->_listnameData->create()->load($listId);
                    $list_name = $listNameData->getReqName();
                    $editUrl = $this->_dataHelper->getEditListUrl($listId);
                    $msg = "Quick order products are added into ";
                    $msg .= "<a href='" . $editUrl . "' target='_blank' >" . $list_name . "</a>";
                    $this->messageManager->addSuccess(__($msg));
                } else {
                    $this->messageManager->addError(__('Please add product to quick order.'));
                }
                return;
            } catch (\Magento\Framework\DB\Adapter\TableNotFoundException $e) {
                $msg = "Table Not Found plese check requisition list module is enable or not.";
                $this->messageManager->addError(__($msg));
            } catch (\Exception $e) {
                $this->messageManager->addError(__('Something went wrong, please try again.'));
            }
        } else {
            $this->messageManager->addError(__('You must login or register to add items to requisition list.'));
        }
    }
    public function setListProduct($quickOrderValue, $listId)
    {
        $ReqProductData = $this->_requisitionProductFactory->create();
        $ReqProductData->addFieldToFilter('list_id', $listId);
        $ReqProductData->addFieldToFilter('prd_id', $quickOrderValue->getProductId());

        $prdCustomOp = $this->getConfigOptions($quickOrderValue);

        // check for group product
        $checkFilter = $this->getProductType($quickOrderValue->getProductId());
        if ($checkFilter) {
            $ReqProductData->addFieldToFilter('prd_add_data', $prdCustomOp);
        }

        // filter product custom options
        $customData = json_decode($quickOrderValue->getPrdCustomData(), true);
        $customData = $this->getCustomOption($quickOrderValue->getPrdCustomData());
        $ReqProductData->addFieldToFilter('prd_custom_data', $customData);

        if (count($ReqProductData->getData())) {
            // update data
            foreach ($ReqProductData as $ReqProductkey => $ReqProductvalue) {
                $addListData = $this->_addListData->create();
                $addListData->load($ReqProductvalue->getId());

                $updateQty = $ReqProductvalue->getPrdQty() + $quickOrderValue->getQty();
                $addListData->setPrdQty($updateQty);

                //update add data
                $prdAddData = [];
                $prdAddData = $this->updatePrdQty($ReqProductvalue, $quickOrderValue);
                $addListData->setPrdAddData($prdAddData);

                // update custom options
                $customData = json_decode($quickOrderValue->getPrdCustomData(), true);
                $customData = $this->getCustomOption($quickOrderValue->getPrdCustomData());
                $addListData->setPrdCustomData($customData);

                // save data
                $addListData->save();
            }
        } else {
            // add new data
            $addListData = $this->_addListData->create();
            $addListData->setListId($listId);
            $addListData->setPrdId($quickOrderValue->getProductId());
            $addListData->setPrdQty($quickOrderValue->getQty());
            $addListData->setPrdAddData($prdCustomOp);
            $customOptions = $this->getCustomOption($quickOrderValue->getPrdCustomData());
            $addListData->setPrdCustomData($customOptions);
            $addListData->save();
        }
    }

    public function getCustomOption($customOptions)
    {
        $jasonCustom = [];
        $custom_check = json_decode($customOptions, true);
        if (empty($custom_check)) {
            $jasonCustom = [];
            $jasonCustom = json_encode($custom_check);
        } else {
            $jasonCustom['options'] = json_decode($customOptions, true);
            $jasonCustom = json_encode($jasonCustom);
        }

        return $jasonCustom;
    }

    public function updatePrdQty($ReqProductvalue, $quickOrderValue)
    {
        $additional_data = [];

        $prdCustomOp = $this->getConfigOptions($quickOrderValue);

        $prdData = $this->_productloader->create()->load($ReqProductvalue->getPrdId());
        if ($prdData->getTypeId() == 'grouped') {
            $table_value = json_decode($ReqProductvalue->getPrdAddData(), true);
            $new_value = json_decode($prdCustomOp, true);
            if (count($table_value) >= count($new_value)) {
                $table_vale_check = $table_value;
                $table_vale_comp = $new_value;
            } else {
                $table_vale_check = $new_value;
                $table_vale_comp = $table_value;
            }
            foreach ($table_vale_check as $table_key => $table_value) {
                if (isset($table_vale_comp[$table_key])) {
                    $new_add_data[$table_key] = (int) $table_vale_comp[$table_key] + (int) $table_value;
                } else {
                    $new_add_data[$table_key] = (int) $table_value;
                }
            }
            $additional_data = json_encode($new_add_data);
        } else {
            $additional_data = $prdCustomOp;
        }
        return $additional_data;
    }

    public function getProductType($prdid)
    {
        $flag = 1;
        $prdData = $this->_productloader->create()->load($prdid);
        if ($prdData->getTypeId() == 'grouped') {
            $flag = 0;
        }
        return $flag;
    }

    public function getConfigOptions($options)
    {
        $prdId = $options->getProductId();
        $prdData = $this->_productloader->create()->load($prdId);
        if ($prdData->getTypeId() == 'configurable') {
            $options = json_decode($options->getPrdAddData(), true);
            $conffigOptions = json_encode($options[$prdId]);
        } else {
            $conffigOptions = $options->getPrdAddData();
        }
        return $conffigOptions;
    }
}

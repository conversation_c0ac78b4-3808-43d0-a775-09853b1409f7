<?php

namespace Dolphin\QuickOrder\Controller\Ajax;

use Dolphin\QuickOrder\Helper\Data;
use Dolphin\QuickOrder\Model\QuicksearchCustomerFactory;
use Dolphin\QuickOrder\Model\QuicksearchGuestFactory;
use Magento\Framework\App\Action\Action;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\JsonFactory;

class Delete extends \Magento\Framework\App\Action\Action
{
    protected $_resultFactory;
    protected $_qsCustomerModelFactory;
    protected $_qsGuestModelFactory;
    protected $_request;
    protected $_resultJsonFactory;
    protected $_dataHelper;

    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\App\Request\Http $request,
        \Magento\Framework\View\Result\PageFactory $resultFactory,
        QuicksearchCustomerFactory $qsCustomerModelFactory,
        QuicksearchGuestFactory $qsGuestModelFactory,
        JsonFactory $resultJsonFactory,
        Data $dataHelper
    ) {
        $this->_request = $request;
        $this->_resultFactory = $resultFactory;
        $this->_qsCustomerModelFactory = $qsCustomerModelFactory;
        $this->_qsGuestModelFactory = $qsGuestModelFactory;
        $this->_resultJsonFactory = $resultJsonFactory;
        $this->_dataHelper = $dataHelper;
        parent::__construct($context);
    }

    public function execute()
    {
        $resultPage = $this->_resultFactory->create(ResultFactory::TYPE_JSON);
        $id = $this->_request->getParam('id');
        $back_url = $this->_redirect->getRefererUrl();
        if (!$id) {
            $this->messageManager->addError(__('Invalid url.'));
            $resultRedirect->setUrl($back_url);
            return $resultRedirect;
        }
        try {
            $resultJson = $this->_resultJsonFactory->create();
            $isCustomerLoggedIn = $this->_dataHelper->isCustomerLoggedIn();

            if ($isCustomerLoggedIn == 1) {
                $customerModel = $this->_qsCustomerModelFactory->create()->load($id);
                if (count($customerModel->getData()) > 0) {
                    $productId = $customerModel->getProductId();
                    $customerModel->delete();
                }
            } else {
                $guestModel = $this->_qsGuestModelFactory->create()->load($id);
                if (count($guestModel->getData()) > 0) {
                    $productId = $guestModel->getProductId();
                    $guestModel->delete();
                }
            }
            $success_msg = 'Product deleted from list has been successfully.';
            $this->messageManager->addSuccess(__($success_msg));
            return;
        } catch (\Exception $e) {
            $error_msg = 'Something went wrong, please try again.';
            $this->messageManager->addError(__($error_msg));
            return;
        }
    }
}

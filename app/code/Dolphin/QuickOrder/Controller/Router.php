<?php

namespace Dolphin\QuickOrder\Controller;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Module\Manager;

class Router implements \Magento\Framework\App\RouterInterface
{
    /**
     * @var \Magento\Framework\App\ActionFactory
     */
    protected $actionFactory;

    /**
     * Response
     *
     * @var \Magento\Framework\App\ResponseInterface
     */
    protected $_response;

    /**
     * Event manager
     *
     * @var \Magento\Framework\Event\ManagerInterface
     */
    protected $_eventManager;

    /**
     * Response
     * @var \Magento\Framework\App\ResponseInterface
     */
    protected $response;

    /**
     * Http
     * @var \Magento\Framework\App\Request\Http
     */
    protected $_request;

    protected $quickOrderHelper;

    /**
     * @param \Magento\Framework\App\ActionFactory      $actionFactory
     * @param ScopeConfigInterface                      $scopeConfig
     * @param \Magento\Framework\App\ResponseInterface  $response
     * @param \Magento\Framework\Event\ManagerInterface $eventManager,
     * @param \Magento\Framework\App\Request\Http       $request
     */
    public function __construct(
        \Magento\Framework\App\ActionFactory $actionFactory,
        ScopeConfigInterface $scopeConfig,
        \Magento\Framework\App\ResponseInterface $response,
        \Magento\Framework\Event\ManagerInterface $eventManager,
        \Dolphin\QuickOrder\Helper\Data $quickOrderHelper,
        \Magento\Framework\App\Request\Http $request
    ) {
        $this->actionFactory = $actionFactory;
        $this->_scopeConfig = $scopeConfig;
        $this->_response = $response;
        $this->_eventManager = $eventManager;
        $this->quickOrderHelper = $quickOrderHelper;
        $this->_request = $request;
    }

    /**
     * @param RequestInterface $request
     * @return \Magento\Framework\App\ActionInterface|null
     */
    public function match(RequestInterface $request)
    {
        $identifier = $this->getPath($request);
        $brandParams = $this->matchBrandParams($identifier);
        if (strstr($identifier, '/') && !empty($brandParams)) {
            $brandId = $brandParams[0];
            $optionUrlKey = explode('/', $identifier);
            $urlKey = array_intersect($optionUrlKey, $brandParams);
            $optionKey = $optionUrlKey[1];
        } else {
            $optionKey = $identifier;
            $urlKey = $brandParams;
        }

        if (!empty($brandParams)) {
            if (in_array($identifier, $brandParams)) {
                $request->setModuleName('quickorder')->setControllerName('index')->setActionName('index');
                $request->setAlias(\Magento\Framework\Url::REWRITE_REQUEST_PATH_ALIAS, $identifier);
                $params = array_merge($request->getParams(), $brandParams);
                $request->setParams($params);
                return $this->actionFactory->create(\Magento\Framework\App\Action\Forward::class);
            }
        }
        return null;
    }

    /**
     * @param RequestInterface $request
     * @return string
     */
    private function getPath(RequestInterface $request)
    {
        $identifier = trim($request->getPathInfo(), '/');
        return $identifier;
    }

    /**
     * @param string $identifier
     * @return array
     */
    public function matchBrandParams($identifier)
    {
        $brandPageUrlKey = $this->quickOrderHelper->getQsOrderUrl();
        $identifier = [$brandPageUrlKey];
        return $identifier;
    }
}

<?php

namespace Dolphin\QuickOrder\Model\ResourceModel\QuicksearchCustomer;

use Do<PERSON>\QuickOrder\Model\QuicksearchCustomer as QuicksearchCustomerModel;
use Dolphin\QuickOrder\Model\ResourceModel\QuicksearchCustomer as QuicksearchCustomerResourceModel;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    protected function _construct()
    {
        $this->_init(
            QuicksearchCustomerModel::class,
            QuicksearchCustomerResourceModel::class
        );
    }
}

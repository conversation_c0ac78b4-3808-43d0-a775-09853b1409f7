<?php
/**
 * @var $block \Magezon\Builder\Block\Element
 */
?>
<?php
$elements          = $this->getElements();
$count             = count($elements);
$element           = $this->getElement();
$id                = $element->getId();
$activeTab         = $element->hasData('active_tab') ? (int)$element->getActiveTab() - 1 : 0;
$hideEmptyTab      = $element->getData('hide_empty_tab');
$tabAlign          = $element->getData('tab_align');
$tabPosition       = $element->getData('tab_position');
$spacing           = (int) $element->getData('spacing');
$gap               = (int) $element->getData('gap');
$noFillContentArea = $element->getData('no_fill_content_area');
$hoverActive       = $element->getData('hover_active');
$mobileAccordion   = $element->getData('mobile_accordion');
?>
<?php if ($count) { ?>
	<?php
	$tabs = [];
	foreach ($elements as $index => $_element) {
		$_html = $_element->toHtml();
		if (!$hideEmptyTab || ($hideEmptyTab && substr_count($_html, 'mgz-element-inner') > 1)) {
			$tabs[] = [
				'element' => $_element,
				'html'    => $_html
			];
		}
	}
?>
	<div class="mgz-tabs mgz-tabs-<?= $id ?> mgz-element-tab-align-<?= $tabAlign ?> mgz-element-tab-position-<?= $tabPosition ?> <?= $noFillContentArea ? 'mgz-tabs-no-fill-content' : '' ?> <?= $mobileAccordion ? 'tabs-mobile-accordion' : '' ?>" data-mage-init='{"Magezon_Builder/js/tabs":{"hover_active": <?= $hoverActive ? 'true' : 'false' ?>}}' data-spacing="<?= $spacing ?>" data-gap="<?= $gap ?>">
		<div class="mgz-tabs-nav">
			<?php foreach ($tabs as $index => $tab) { ?>
				<?php
				$_element     = $tab['element'];
				$id           = $_element->getId();
				$addIcon      = $_element->getData('add_icon');
				$iconPosition = $_element->getData('icon_position');
				$icon         = $_element->getData('icon');
				?>
				<div class="<?= $id ?> tab-<?= $id ?>-title mgz-tabs-tab-title<?= ($index===$activeTab) ? ' mgz-active' : '' ?>" data-id="tab-<?= $id ?>-title">
					<a href="#tab-<?= $id ?>" data-id="#tab-<?= $id ?>">
						<?php if ($addIcon && $icon && $iconPosition == 'left') { ?>
							<i class="<?= $icon ?>"></i>
						<?php } ?>
						<span><?= $_element->getTitle() ?></span>
						<?php if ($addIcon && $icon && $iconPosition == 'right') { ?>
							<i class="<?= $icon ?>"></i>
						<?php } ?>
						<span class="tabs-opener"></span>
					</a>
				</div>
			<?php } ?>
		</div>
		<div class="mgz-tabs-content">
			<?php foreach ($tabs as $index => $tab) { ?>
				<?php
				$_element = $tab['element'];
				$id       = $_element->getId();
				?>
				<div class="<?= $id ?> tab-<?= $id ?>-content mgz-tabs-tab-content<?= ($index===$activeTab) ? ' mgz-active' : '' ?>" id="tab-<?= $id ?>">
					<?= $tab['html'] ?>
				</div>
			<?php } ?>
		</div>
	</div>
<?php } ?>

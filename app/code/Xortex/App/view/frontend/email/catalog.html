<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Your Requested Catalog from %store_name" store_name=$store.getFrontendName()}} @-->
<!--@vars {
"var data.email":"Sender Email",
"var data.name":"Sender Name",
"var data.productId":"Product-ID",
"var data.productName":"Product",
"var data.productCatalogPdf":"Catalog"
} @-->

{{template config_path="design/email/header_template"}}

<table>
  <tr class="email-intro">
    <td>
      <p class="greeting">{{trans "Dear Ladies and Gentlemen,"}}</p>
      <p>
        {{trans 'You will find the Catalog for the Product %productName as Attachement.' productName=$data.productName}}<br/>
        {{trans 'Best regards'}}
      </p>
    </td>
  </tr>
</table>

{{template config_path="design/email/footer_template"}}

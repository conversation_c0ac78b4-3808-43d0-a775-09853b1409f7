<?php

namespace Xortex\App\Catalog\Helper;



class Price extends \Magento\Framework\App\Helper\AbstractHelper {

    /**
     * @var \Magento\Framework\Pricing\Helper\Data;
     */
    protected $pricingHelper;

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Framework\Pricing\Helper\Data $pricingHelper
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\Pricing\Helper\Data $pricingHelper
    ) {
        parent::__construct($context);
        $this->pricingHelper =  $pricingHelper;
    }

    /**
     * @param float $finalPrice
     * @param float $normalPrice
     * @return string
     */
    public function getSaveInfo($finalPrice, $normalPrice) {
        if($finalPrice && $normalPrice && $finalPrice < $normalPrice) {
            $saveAbsolut = $normalPrice - $finalPrice;
            $savePercent = 100 - (($finalPrice * 100) / $normalPrice);

            return '<span class="save-info">' .
                '<span class="save-label">' . __("You save:") . ' </span>' .
                $this->pricingHelper->currency($saveAbsolut) .
                '</span>';
        }
    }
}
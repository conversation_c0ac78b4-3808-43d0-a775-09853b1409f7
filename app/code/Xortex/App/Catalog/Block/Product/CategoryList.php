<?php

namespace Xortex\App\Catalog\Block\Product;

/**
 * Main contact form block
 */
class CategoryList extends \Magento\Catalog\Block\Product\View
{

    /**
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection|\Magento\Framework\Data\Tree\Node\Collection
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function getChildCategoryListCollection()
    {
        $category = $this->getSecondLevelCategory();
        if($category) {
            $categoryId = $category->getId();
            /** @var \Magento\Framework\Data\Tree\Node\Collection|\Magento\Catalog\Model\ResourceModel\Category\Collection $childCategoryCollection */
            $childCategoryCollection = $category->getCategories($categoryId, 1, false, true, false);
            $childCategoryCollection->addAttributeToFilter("showonproductdetailpage", 1);
            $childCategoryCollection->addAttributeToSelect('*')->setOrder(
                'position',
                \Magento\Framework\DB\Select::SQL_ASC
            );
            $childCategoryCollection->load();
            return $childCategoryCollection;
        }
        return null;
    }

    /**
     * @return \Magento\Framework\DataObject|mixed
     */
    public function getSecondLevelCategory() {
        static $secondLevelCategory = null;
        if(is_null($secondLevelCategory)) {
            if ($this->getProduct()->getCategoryCollection()->count() > 0) {
                foreach ($this->getProduct()->getCategoryCollection() as $category) {
                    if ($category->getLevel() == 2) {
                        $secondLevelCategory = $category;
                        return $secondLevelCategory;
                    }
                }
            }
            $secondLevelCategory = $this->getProduct()->getCategoryCollection()->getFirstItem();
        }
        return $secondLevelCategory;
    }

    /**
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection|\Magento\Framework\Data\Tree\Node\Collection
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getChildCategoryListCollectionFromCurrentCategory()
    {
        return $this->getChildCategoryListCollection();
    }

    public function isContentMode()
    {
        return true;
    }

    public function isMixedMode()
    {
        return true;
    }
}
<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class CatalogProductGetFinalPrice implements ObserverInterface
{
	/** @var \MageB2B\Staff\Helper\Data */
    protected $_staffHelper;
	
	public function __construct(
		\MageB2B\Staff\Helper\Data $staffHelper,
		\Magento\Checkout\Model\Session $checkoutSession
	)
	{
		$this->_staffHelper = $staffHelper;
		$this->_checkoutSession = $checkoutSession;
	}

	public function execute(Observer $observer)
    {	
		if ($this->_staffHelper->getCommissionType() == \MageB2B\Staff\Model\Config\Source\CommissionType::DISCOUNT_ON_PRODUCT) {
			$product = $observer->getProduct();			
			$qty = $observer->getQty();			
			$price = $product->getPrice();
			$commission = $this->_staffHelper->getStaffCommission($price);
			if ($commission) {
				$finalPrice = $price - $commission;
				$product->setFinalPrice($finalPrice); // set the product final price this will be used in finalprice calculation
				$product->setPrice($finalPrice);
			}
			
			// set commission in quote
			$commissionParts = $this->_staffHelper->getCustomerSession()->getStaffCommissionParts();
			if (!$commissionParts) {
				$commissionParts = array();
			}
			$quoteId = $this->_checkoutSession->getQuote()->getId();			
			$commissionParts[$quoteId][$product->getId()] = $commission * $qty;
			$this->_staffHelper->getCustomerSession()->setStaffCommissionParts($commissionParts);
		}
        return $this;
	}
}
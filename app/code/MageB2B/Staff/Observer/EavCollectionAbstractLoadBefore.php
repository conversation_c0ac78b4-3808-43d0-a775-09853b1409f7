<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class EavCollectionAbstractLoadBefore implements ObserverInterface
{
	/** @var \MageB2B\Staff\Model\ResourceModel\Staff\CollectionFactory */
    protected $staffCollectionFactory;
    
    /** @var Magento\Framework\Registry */
    protected $_coreRegistry;
    
    /**
     * EavCollectionAbstractLoadBefore constructor.
     * @param \MageB2B\Staff\Model\StaffFactory $staffFactory
     */
	public function __construct(
        \MageB2B\Staff\Model\ResourceModel\Staff\CollectionFactory $staffCollectionFactory,
		\Magento\Framework\Registry $coreRegistry
	)
	{
		$this->staffCollectionFactory = $staffCollectionFactory;
		$this->_coreRegistry = $coreRegistry;
	}

    /**
     * @param Observer $observer
     * @return $this
     */
	public function execute(Observer $observer)
    {
		$collection = $observer->getCollection();

		/**
		 * Filter collection by email if provided collection is customer collection
		 */
		if ($collection instanceof \Magento\Customer\Model\ResourceModel\Customer\Collection
				|| $collection instanceof \Magento\Customer\Model\ResourceModel\Grid\Collection) {
			/**
			 * skip filtering customer collection by staff when registry entry exists
			 */
			if ($this->_coreRegistry->registry('skip_staff_filter')) {
				return $this;
			}

			$staffEmails = $this->staffCollectionFactory->create()->emailOptionHash();
			if (count($staffEmails) > 0) {
				
				if ($collection instanceof \Magento\Customer\Model\ResourceModel\Customer\Collection) {
					$collection->addAttributeToFilter('email', array('nin' => $staffEmails));
				}
				
				if ($collection instanceof \Magento\Customer\Model\ResourceModel\Grid\Collection) {
					$collection->addFieldToFilter('email', array('nin' => $staffEmails));
				}
			}
		}
		return $this;
    }
}
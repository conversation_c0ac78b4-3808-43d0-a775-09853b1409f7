<!--@subject {{trans "Welcome, %firstname %lastname" firstname=$staff.firstname lastname=$staff.lastname}}! @-->
<!--@vars {
"var staff.firstname":"Staff Firstname",
"var staff.lastname":"Staff Lastname",
} @-->

<!--@styles
body,td { color:#2f2f2f; font:11px/1.35em Verdana, Arial, Helvetica, sans-serif; }
@-->
{{template config_path="design/email/header_template"}}

<p class="greeting">{{trans "%firstname %lastname," firstname=$staff.firstname lastname=$staff.lastname|raw}}</p>
<p>{{trans "Welcome to %store_name." store_name=$store.getFrontendName()|raw}}</p>
<p>
	{{trans "Use the following values when prompted to log in:"}}<br/>
	<strong>{{trans "E-mail"|raw}}</strong>: {{var staff.email}}<br/>
	<strong>{{trans "Password"|raw}}</strong>: {{var staff.password}}<br/>
</p>
	
<p>{{trans "When you log in to your account, you will be able to do the following:"}}</p>

<ul>
	<li>{{trans "login with allocated customer to you."}}</li>
	<li>{{trans "Check the status of orders of customer alocated to you."}}</li>
	<li>{{trans "View past orders of customer alocated to you."}}</li>
	<li>{{trans "Make changes to  account information of customer alocated to you."}}</li>        
</ul>

{{template config_path="design/email/footer_template"}}
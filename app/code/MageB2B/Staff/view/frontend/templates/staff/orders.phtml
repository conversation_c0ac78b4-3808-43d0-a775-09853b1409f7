<?php
/**
 * @package MageB2B_CustomerDocuments 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
 // $block MageB2B\CustomerDocuments\Block\Frontend
?>
<?php $_orders = $this->getCollection(); ?>
<?php if($_orders->getSize()): ?>
	<div class="table-wrapper">
		<table class="data table table-order-items history" id="my-orders-table">
			<caption class="table-caption"><?php /* @escapeNotVerified */ echo __('Orders') ?></caption>
			<thead>
				<tr>
					<th scope="col" class="col id"><?php echo __('Order #') ?></th>
					<th scope="col" class="col order_date"><?php echo __('Date') ?></th>
					<th scope="col" class="col ship_to"><?php echo __('Ship To') ?></th>
					<th scope="col" class="col order_total"><?php echo __('Order Total') ?></th>
				</tr>
			</thead>
			<tbody>
			<?php $_odd = ''; ?>
			<?php foreach ($_orders as $_order): ?>
				<tr>
					<td class="col id"><?php echo $_order->getRealOrderId() ?></td>
					<td class="col order_date"><span class="nobr"><?php echo $this->formatDate($_order->getCreatedAt()) ?></span></td>
					<td class="col ship_to"><?php echo $_order->getShippingAddress() ? $this->escapeHtml($_order->getShippingAddress()->getName()) : '&nbsp;' ?></td>
					<td class="col order_total"><?php echo $_order->formatPrice($_order->getGrandTotal()) ?></td>
				</tr>
			<?php endforeach; ?>
			</tbody>
		</table>
	</div>
    <?php if ($block->getPagerHtml()): ?>
        <div class="toolbar bottom"><?php echo $block->getPagerHtml(); ?></div>
    <?php endif ?>
<?php else: ?>
    <p><?php echo __('You have placed no orders.'); ?></p>
<?php endif ?>

<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" layout="2columns-left">
	<head>
        <css src="MageB2B_Staff::css/staff.css"/>
    </head>
    <body>
		<referenceContainer name="sidebar.main">
			<block class="MageB2B\Staff\Block\Staff" name="staff_menu" template="staff/account_menu.phtml" before="-" />
        </referenceContainer>
		
        <referenceContainer name="content">
			<block class="MageB2B\Staff\Block\Staff" name="staff_customers" as="staff_customers" template="staff/staff.phtml" cacheable="false">
                <block class="MageB2B\Staff\Block\Staff" name="staff_list" as="staff_list" template="staff/list.phtml" />
                <block class="MageB2B\Staff\Block\Staff" name="staff_check" as="staff_check" template="staff/check.phtml" />
            </block>
        </referenceContainer>
        <move element="page.main.title" destination="content.top" before="-"/>
    </body>
</page>
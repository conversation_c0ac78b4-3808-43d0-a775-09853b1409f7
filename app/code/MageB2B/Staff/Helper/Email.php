<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Staff\Helper;

class Email extends \Magento\Framework\App\Helper\AbstractHelper
{
	// const XML_PATH_REGISTER_EMAIL_TEMPLATE = 'staff/email/staff_new_template';
	/**
     * email identity
     */
    const XML_PATH_REGISTER_EMAIL_IDENTITY = 'staff/create_account/email_identity';
	
	/** @var \MageB2B\Staff\Helper\Common */
    protected $_commonHelper;
	
	/** @var \MageB2B\Staff\Helper\Config */
    protected $_configHelper;
	
	/** @var Logger */
    protected $_logger;
	
	/** @var \Magento\Framework\Mail\Template\TransportBuilder */
	protected $_transportBuilder;
	
	/** @var \Magento\Framework\Translate\Inline\StateInterface */
    protected $_inlineTranslation;

	/**
     * Initialize dependencies.
     */
    public function __construct(
		\MageB2B\Staff\Helper\Common $commonHelper,
		\Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
		\Magento\Framework\Translate\Inline\StateInterface $inlineTranslation
	) {
		$this->_commonHelper = $commonHelper;
		$this->_configHelper = $this->_commonHelper->getConfigHelper();
		$this->_logger = $this->_commonHelper->getLogger();
		$this->_transportBuilder = $transportBuilder;
		$this->_inlineTranslation = $inlineTranslation;
    }
	
	protected function _getFrom()
	{
		// magento will automatically use trans_email/ident_' . $sender . '/name and trans_email/ident_' . $sender . '/email
		// and will generate from array
		return $this->_configHelper->get(SELF::XML_PATH_REGISTER_EMAIL_IDENTITY);
	}
	
	protected function _getCcReceivers()
	{
		// get from config | based on extension
		$cc = [];
		return $cc;
	}
	
	protected function _getBccReceivers()
	{
		// get from config
		$bcc = [];

		// get bcc from post if exist
		$bcc[] = $this->_commonHelper->getRequest()->getPost('bcc');
		
		return $bcc;
	}
	
	public function send($to, $templateConfig, $templateVars) {
		$templateOptions = array('area' => \Magento\Framework\App\Area::AREA_FRONTEND, 'store' => $this->_commonHelper->getCurrentStore()->getId());		
		$templateVars['store'] = $this->_commonHelper->getCurrentStore();		
		$template = $this->_configHelper->get($templateConfig);
		
		// get cc receivers
		$ccReceivers = $this->_getCcReceivers();
			
		// get bcc receivers
		$bccReceivers = $this->_getBccReceivers();
		
		$from = $this->_getFrom();
		$this->_inlineTranslation->suspend();
		$to = array($to);
		$transport = $this->_transportBuilder->setTemplateIdentifier($template)
						->setTemplateOptions($templateOptions)
						->setTemplateVars($templateVars)
						->setFrom($from)
						->addTo($to)
						->addCc($ccReceivers)
						->addBcc($bccReceivers)
						->getTransport();
		try {
			$transport->sendMessage();
		} catch (\Exception $e) {
			$this->_logger->critical($e);
		}
		$this->_inlineTranslation->resume();
	}
}

<?xml version="1.0"?>
<!--
/**
 * @package MageB2B_CustomerId 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
	<event name="checkout_submit_all_after"> 
        <observer name="mageb2b_staff_checkout_submit_all_after" instance="MageB2B\Staff\Observer\CheckoutSubmitAllAfter" />
    </event>
	
	<event name="sales_order_save_before">
        <observer name="mageb2b_staff_sales_order_save_before" instance="MageB2B\Staff\Observer\SalesOrderSaveBefore" />
    </event>
	
	<event name="customer_delete_before">
        <observer name="mageb2b_staff_customer_delete_before" instance="MageB2B\Staff\Observer\CustomerDeleteBefore" />
    </event>
	
	<event name="catalog_product_get_final_price"> 
        <observer name="mageb2b_staff_catalog_product_get_final_price" instance="MageB2B\Staff\Observer\CatalogProductGetFinalPrice" />
    </event>
    <!-- to filter out customers which does exist in customer_staff table -->
    <event name="eav_collection_abstract_load_before">
        <observer name="mageb2b_staff_eav_collection_abstract_load_before"
                  instance="MageB2B\Staff\Observer\EavCollectionAbstractLoadBefore"/>
    </event>

    <!-- allow password reset for staff members -->
    <event name="controller_action_predispatch_customer_account_createPassword">
        <observer instance="MageB2B\Staff\Observer\Controller\ActionPredispatchCustomerAccountCreatePassword" name="mageb2b_staff_observer_controller_actionpredispatchcustomeraccountcreatepassword_controller_action_predispatch_customer_account_createPassword"/>
    </event>
    <event name="controller_action_predispatch_customer_account_resetpasswordpost">
        <observer instance="MageB2B\Staff\Observer\Controller\ActionPredispatchCustomerAccountCreatePassword" name="mageb2b_staff_observer_controller_actionpredispatchcustomeraccountcreatepassword_controller_action_predispatch_customer_account_createPassword"/>
    </event>

    <event name="core_collection_abstract_load_before">
        <observer name="mageb2b_staff_core_collection_abstract_load_before"
                  instance="MageB2B\Staff\Observer\EavCollectionAbstractLoadBefore"/>
    </event>
</config>

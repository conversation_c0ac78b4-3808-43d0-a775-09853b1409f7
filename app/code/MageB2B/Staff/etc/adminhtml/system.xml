<?xml version="1.0"?>
<!--
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="mageb2b" translate="label" sortOrder="1000">
            <label>MageB2B</label>
        </tab>
        <section id="staff" translate="label" type="text" sortOrder="1001" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Staff</label>
            <tab>mageb2b</tab>
			<resource>MageB2B_Staff::staff_config</resource>
            <group id="general" translate="label" type="text" sortOrder="1000" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>General settings</label>
				
				<!-- <field id="use_staffid_for_customer" translate="label comment" type="select" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Use staff_id for invisible Customers</label>
                    <comment>Staff creates invisible customers for staff users. If you want their ids the same as staff_id then say yes here. Note: this will overwrite existing customers if they have the same entity_id - only use this setting if you have for example the first 1000 customer_ids empty.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field> -->
				
				<field id="customer_have_multiplestaff" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Can customer have multiple staff</label>
                    <comment>If set to yes then one customer can be assigned to multiple staff.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
				
				<field id="staff_can_password_customer" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Can staff specify password for customer</label>
                    <comment>If set to yes then staff can specify password for customer while creating customers at front-end.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
				
				<field id="search_customers_by_staff" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable search customers by staff for front-end</label>
                    <comment>If set to yes then customers can be search by staff at front-end.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
				
				<field id="show_staffid_in_entitycol" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show staff id in entity_id column at front-end</label>
                    <comment>If set to yes then staff id will be displayed with entity_id. It is just to differentiate diplicate customers assigned to different staff.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
				
				<field id="add_footer_link_staff_signup" translate="label comment" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Add footer link for staff signup</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
				
				<field id="list_customers" translate="label comment" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>List customers in frontend</label>
                    <comment>If checked, a staff can see a complete list of his customers in frontend.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
				
				<field id="activate_check_add_staff" translate="label comment" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Activate check and staff in frontend</label>
                    <comment>If checked, you are able to check current customers in frontend.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                
                <field id="show_address" translate="label comment" type="select" sortOrder="91" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show address fields on customer registration</label>
                    <comment>When staff is creating a customer, should address fields be shown?</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                
                <field id="show_customer_group" translate="label comment" type="select" sortOrder="92" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show customer group field on customer registration</label>
                    <comment>When staff is creating a customer, should customer group selection be shown?</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                
                <field id="enable_creating_new_customer" translate="label comment" type="select" sortOrder="93" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable creating new customer</label>
                    <comment>If activated, you are able to create new customers at frontend.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
			
				
				<field id="staff_loginpost_redirect" translate="label comment" type="text" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Login post url handle</label>
					<comment><![CDATA[Staff will be redirected to this url after login at frontend. Example: 'salesstaff']]></comment>
                </field>
				
				<field id="show_vat_field" translate="label comment" type="select" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show vat field</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
				
				<field id="show_company_field" translate="label comment" type="select" sortOrder="140" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show company field</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                
                <field id="staff_can_delete_customer" translate="label comment" type="select" sortOrder="160" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Can staff delete his own customers?</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
			
			<group id="email" translate="label" type="text" sortOrder="1400" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Email settings</label>
				
				<field id="email_order_to" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Email order to</label>
                    <source_model>MageB2B\Staff\Model\Config\Source\OrderReceiver</source_model>
                </field>
				
				<field id="staff_order_template" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email template new order from staff</label>
					<source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
				
				<field id="staff_new_template" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email template new staff</label>
					<source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
				
				<field id="staff_set_password_customer_template" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email template password for customer created by staff</label>
					<source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
				
				<field id="staff_activated" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email template for staff activation</label>
					<source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
				
				<field id="staff_de_activated" translate="label comment" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email template for staff de-activation</label>
					<source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
            
            <group id="default_values" translate="label" type="text" sortOrder="1600" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Default values for customer attributes</label>
				<comment>Default values for customer attributes which are required and not exist for staff</comment>
				<frontend_model>MageB2B\Staff\Block\Adminhtml\System\Config\Form\Fieldset\RequiredAttributes</frontend_model>
            </group>
        </section>
    </system>
</config>

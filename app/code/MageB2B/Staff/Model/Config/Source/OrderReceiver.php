<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Model\Config\Source;

class OrderReceiver
{	
	const NONE = 0;
	const CUSTOMER_STAFF_RECEIVER = 1;
	const CUSTOMER_RECEIVER = 2;
	const CUSTOMER_RECEIVER_STAFF_BCC = 3;
	const CUSTOMER_RECEIVER_OR_STAFF = 4;
	const STAFF_ONLY = 5;

	protected $_orderReceivers;
    
    /**
     * Options getter
     * @return array
     */
    public function getAllOptions()
    {
		if (!$this->_orderReceivers)
        {
			$options = array();
			$options[] = array(
				'label'	=>	__('None'),
				'value'	=>	self::NONE,
			);
			
			$options[] = array(
				'label'	=>	__('Customer (Receiver) and Staff (Receiver)'),
				'value'	=>	self::CUSTOMER_STAFF_RECEIVER,
			);
			
			$options[] = array(
				'label'	=>	__('Customer (Receiver)'),
				'value'	=>	self::CUSTOMER_RECEIVER,
			);

            $options[] = array(
                'label'	=>	__('Customer OR Staff (Depeding on who made the transaction)'),
                'value'	=>	self::CUSTOMER_RECEIVER_OR_STAFF,
            );
            $options[] = array(
                'label'	=>	__('Staff Only'),
                'value'	=>	self::STAFF_ONLY,
            );
			
			$this->_orderReceivers = $options;
		}
		return $this->_orderReceivers;
    }

    /**
     * @return array
     */
    public function toOptionArray()
    {
		return $this->getAllOptions();
	}
}

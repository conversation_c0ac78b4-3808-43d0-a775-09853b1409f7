<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
 
namespace MageB2B\Staff\Controller\Index;

class Index extends \Magento\Customer\Controller\AbstractAccount
{
	/**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
    }
	
    /**
     * Index action
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
		if (!$this->_checkStaffAccess()) {
			$resultRedirect = $this->resultRedirectFactory->create();
			return $resultRedirect->setPath('/');
		}
		
		/** @var \Magento\Framework\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
		$resultPage->getConfig()->getTitle()->set(__('My staff account'));
		return $resultPage;
    }
	
	/**
     * check staff access
     */
    private function _checkStaffAccess()
    {
		$commonHelper = $this->_objectManager->create('MageB2B\Staff\Helper\Common');
		$customerSession = $commonHelper->getCustomerSession();
        if (!$customerSession->isLoggedIn() || is_null($customerSession->getStaffId())) {
            $this->messageManager->addErrorMessage(__('You are not allowed to view this page.'));
			
            return false;
        }
        return true;
    }
}
<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
 
namespace MageB2B\Staff\Controller\Index;

class DeleteCustomer extends \Magento\Customer\Controller\AbstractAccount
{
	/** @var \MageB2B\Staff\Helper\Data */
	private $staffHelper;
	
	/** @var \MageB2B\Staff\Helper\Common */
	private $commonHelper;
	
	/** @var \MageB2B\Staff\Model\CustomerRepository */
	private $staffCustomerRepository;
	
	/**
	 * @param \Magento\Framework\App\Action\Context $context
	 */
	public function __construct(
		\Magento\Framework\App\Action\Context $context,
		\MageB2B\Staff\Helper\Data $staffHelper,
		\MageB2B\Staff\Helper\Common $commonHelper,
		\MageB2B\Staff\Model\CustomerRepository $staffCustomerRepository
	) {
		parent::__construct($context);
		$this->staffHelper = $staffHelper;
		$this->commonHelper = $commonHelper;
		$this->staffCustomerRepository = $staffCustomerRepository;
	}
	
	public function execute()
	{
		try {
			$currentStaff = $this->staffHelper->getCurrentStaff();
			if (!$currentStaff) {
				throw new \Magento\Framework\Exception\LocalizedException(__('You do not have right to access this area.'));
			}
			
			$customerId = $this->getRequest()->getParam('customer_id');
			if (!$customerId) {
				throw new \Magento\Framework\Exception\LocalizedException(__('Customer ID does not exist.'));
			}
			
			$staffCustomer = $this->staffCustomerRepository->getCollection()
				->addFieldToFilter('customer_id', $customerId)
				->addFieldToFilter('staff_id', $currentStaff->getId())
				// ->addFieldToFilter('website_id', $currentStaff->getWebsiteId())
				->getFirstItem()
			;
			
			if (!$staffCustomer->getId()) {
				throw new \Magento\Framework\Exception\LocalizedException(__('This customer is un-assigned from your sales account.'));
			}
			$staffCustomer->delete();
			$this->messageManager->addSuccessMessage(__('Customer is removed.'));
		} catch (\Magento\Framework\Exception\LocalizedException $e) {
			$this->commonHelper->getLogger()->critical($e);
			$this->messageManager->addErrorMessage($e->getMessage());
		} catch (\Exception $e) {
			$this->commonHelper->getLogger()->critical($e);
			$this->messageManager->addErrorMessage($e->getMessage());
		}
		return $this->_redirect('*/*/customers');
	}
}
<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Sublogin\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class CustomerLogin implements ObserverInterface
{
	/**
     * @var \MageB2B\Sublogin\Helper\Data
     */
    protected $_subloginHelper;
	
	/**
     * @var \Magento\Framework\UrlFactory
     */
    protected $_urlFactory;

    /**
     * CustomerLogin constructor.
     * @param \MageB2B\Sublogin\Helper\Data $subloginHelper
     * @param \Magento\Framework\UrlFactory $urlFactory
     */
	public function __construct(
		\MageB2B\Sublogin\Helper\Data $subloginHelper,
		\Magento\Framework\UrlFactory $urlFactory
	)
	{
		$this->_subloginHelper = $subloginHelper;
		$this->_urlFactory = $urlFactory;
	}

	/**
     * If config for POST URL is set, sublogin will be redirect
	 * to this address after login
     * @param \Magento\Framework\Event\Observer $observer
     * @return \MageB2B\Sublogin\Observer\CustomerLogin
     */
	public function execute(Observer $observer)
    {
		if (!$this->_subloginHelper->getCurrentSublogin()) {
            return $this;
        }
		$urlFromConfig = $this->_subloginHelper->getConfigHelper()->get('sublogin/general/login_post_url');
		if (!$urlFromConfig) {
			return $this;
		}
		$uri = \Zend_Uri::check($urlFromConfig);
		if ($uri) {
			$this->_subloginHelper->getCustomerSession()->setBeforeAuthUrl($urlFromConfig);
			$this->_subloginHelper->getCustomerSession()->setAfterAuthUrl($urlFromConfig);
		} else {
			$urlFactory = $this->_urlFactory->create();
			$url = $urlFactory->getUrl($urlFromConfig);
			$this->_subloginHelper->getCustomerSession()->setBeforeAuthUrl($url);
			$this->_subloginHelper->getCustomerSession()->setAfterAuthUrl($url);
		}
    }
}
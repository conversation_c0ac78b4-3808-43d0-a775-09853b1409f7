<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Sublogin\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Checkout\Model\Session as CheckoutSession;

class SalesOrderPlaceAfter implements ObserverInterface
{
	/** @var \MageB2B\Sublogin\Helper\Data */
    protected $_subloginHelper;
	
	/** @var \MageB2B\Sublogin\Helper\Logger */
	protected $_logger;
	
	/** @var \Magento\Sales\Model\Order\Email\Sender\OrderSender */
	protected $_orderSender;

    /** @var \MageB2B\Sublogin\Helper\Config */
	protected $_configHelper;
	
	/** @var CheckoutSession */
	protected $checkoutSession;

    /**
     * SalesOrderPlaceAfter constructor.
     * @param \MageB2B\Sublogin\Helper\Data $subloginHelper
     * @param \MageB2B\Sublogin\Helper\Config $configHelper
     * @param \MageB2B\Sublogin\Helper\Logger $logger
     * @param \Magento\Sales\Model\Order\Email\Sender\OrderSender $orderSender
     * @param CheckoutSession $checkoutSession
     */
	public function __construct(
		\MageB2B\Sublogin\Helper\Data $subloginHelper,
		\MageB2B\Sublogin\Helper\Config $configHelper,
		\MageB2B\Sublogin\Helper\Logger $logger,
		\Magento\Sales\Model\Order\Email\Sender\OrderSender $orderSender,
		CheckoutSession $checkoutSession
	)
	{
		$this->_subloginHelper = $subloginHelper;
		$this->_configHelper = $configHelper;
		$this->_logger = $logger;
		$this->_orderSender = $orderSender;
		$this->checkoutSession = $checkoutSession;
	}

    /**
     * @param Observer $observer
     */
	public function execute(Observer $observer)
    {	
		$sublogin = $this->_subloginHelper->getCurrentSublogin();
		$customerSession = $this->_subloginHelper->getCustomerSession();
        if (!is_null($sublogin) && $sublogin->getId()) {
        	$order = $observer->getOrder();
            $order->setData('sublogin_id', $sublogin->getId());
			
			/**
			 * When we are saving main login email in order then email is not
			 * going to be send to sublogin email. So to send an email to sublogin
			 * we are saving sublogin email in customer_email_orig field
			 * This email we will be using in 
			 * \MageB2B\Sublogin\Observer\CheckoutSubmitAllAfter::execute to send out an email
			 */
			if ($this->_configHelper->get('sublogin/general/save_customer_email_in_order')) {
			    if ($customerSession->getLoggedinFromMainLogin()) {
                    $order->setCustomerEmailOrig($sublogin->getEmail());
                } else {
                    $order->setCustomerEmailOrig($order->getCustomerEmail());
                }

                $order->setCustomerEmail($customerSession->getData('orig_email'));
				$order->setCanSendNewEmailFlag(false);
			} else {
				/**
				 * In some cases, we do not have sublogin email saved by default in order when sublogin is placing an order
				 * So its a additional specification of sublogin email, firstname and lastname which needs to be set in order
				 */
				$order->setCustomerPrefix($sublogin->getPrefix());
				$order->setCustomerFirstname($sublogin->getFirstname());
				$order->setCustomerLastname($sublogin->getLastname());
				$order->setCustomerEmail($sublogin->getEmail());
			}
        }
    }
}
<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Sublogin\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class AddNewCustomerAddressToSublogin implements ObserverInterface
{
	/** @var \MageB2B\Sublogin\Helper\Common */
	protected $commonHelper;
	
	/** @var \MageB2B\Sublogin\Helper\Config */
    protected $configHelper;
	
	/** @var \MageB2B\Sublogin\Helper\Data */
    protected $subloginHelper;

    /**
     * AddNewCustomerAddressToSublogin constructor.
     * @param \MageB2B\Sublogin\Helper\Common $commonHelper
     * @param \MageB2B\Sublogin\Helper\Data $subloginHelper
     */
	public function __construct(
		\MageB2B\Sublogin\Helper\Common $commonHelper,
		\MageB2B\Sublogin\Helper\Data $subloginHelper
	)
	{
		$this->commonHelper = $commonHelper;
		$this->configHelper = $commonHelper->getConfigHelper();
		$this->subloginHelper = $subloginHelper;
	}

	public function execute(Observer $observer)
    {
    	$customerAddress = $observer->getCustomerAddress();
    	if ((int) $this->configHelper->get('sublogin/general/address_management') == \MageB2B\Sublogin\Model\Config\Source\Addressmanagement::OPTION_USE_CUSTOM_ADDRESSES &&
    			(int) $this->configHelper->get('sublogin/general/automatically_add_customer_address') == 1)
    	{
    		$allowed = false;
    		$request = $this->commonHelper->getRequest();
    		$requestHandle = $request->getModuleName().'_'.$request->getControllerName().'_'.$request->getActionName();
    		if ($requestHandle == 'admin_customer_save') {
    			$allowed = true;
    		} else {
    			// as its frontend, we need to check the logged in customer is not sublogin
    			if (!$this->subloginHelper->getCurrentSublogin()) {
    				$allowed = true;
    			}
    		}
    		
    		if ($allowed) {
    			if ($customerAddress->getOrigData('entity_id') == false) {
    				$sublogins = $this->subloginHelper->customerHasSublogin($customerAddress->getCustomer());
    				if ($sublogins) {
    					foreach ($sublogins as $sublogin) {
    						// as we are here only on customer address save so its always
    						// is_primary_billing = 0, is_primary_shipping = 0 and is_customer_address = 1
    						$additionalData = array(
    							'is_primary_billing'    =>  0,
    							'is_primary_shipping'   =>  0,
    							'is_customer_address'   =>  1,
    						);
    						$this->subloginHelper->saveAddressSubloginRelation($customerAddress->getId(), $sublogin->getId(), $additionalData);
    					}
    				}
    			}
    		}
    	}
    	return $this;
    }
}
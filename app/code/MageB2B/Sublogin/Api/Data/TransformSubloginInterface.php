<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Sublogin\Api\Data;

/**
 * TransformSubloginInterface
 * @api
 */
interface TransformSubloginInterface
{
    /**#@+
     * Constants for keys of data array. Identical to the name of the getter in snake case
     */
	const SUBLOGIN_ID = 'sublogin_id';

	/** @return int */
	public function getSubloginId();
	/** @return \MageB2B\Sublogin\Api\Data\TransformSubloginInterface */
	public function setSubloginId($subloginId);
}
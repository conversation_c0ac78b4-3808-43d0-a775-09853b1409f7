<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Sublogin\Api\Data;

/**
 * TransformCustomerInterface
 * @api
 */
interface TransformCustomerInterface
{
    /**#@+
     * Constants for keys of data array. Identical to the name of the getter in snake case
     */
	const CUSTOMER_ID = 'customer_id';
	const SUBLOGIN_CUSTOMER_ID = 'sublogin_customer_id';

    /** @return int */
    public function getCustomerId();
    /** @return \MageB2B\Sublogin\Api\Data\TransformCustomerInterface */
    public function setCustomerId($customerId);

	/** @return int */
	public function getSubloginCustomerId();
	/** @return \MageB2B\Sublogin\Api\Data\TransformCustomerInterface */
	public function setSubloginCustomerId($subloginCustomerId);
}
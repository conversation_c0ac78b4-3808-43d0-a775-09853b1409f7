<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Sublogin\Api\Data;

/**
 * Sublogin interface.
 * @api
 */
interface AddressInterface
{
    /**#@+
     * Constants for keys of data array. Identical to the name of the getter in snake case
     */
	const ID = 'id';
	const PREFIX = 'prefix';
	const FIRSTNAME = 'firstname';
	const MIDDLENAME = 'middlename';
	const LASTNAME = 'lastname';
	const SUFFIX = 'suffix';
	const COMPANY = 'company';
	const STREET = 'street';
	const CITY = 'city';
	const COUNTRY_ID = 'country_id';
	const REGION = 'region';
	const POSTCODE = 'postcode';
	const TELEPHONE = 'telephone';
	const DEFAULT_BILLING = 'default_billing';
	const DEFAULT_SHIPPING = 'default_shipping';
	
	/** @return int|null */
	public function getId();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setId($id);
	
	/** @return string|null */
	public function getPrefix();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setPrefix($prefix);
	
	/** @return string */
	public function getFirstname();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setFirstname($firstname);
	
	/** @return string|null */
	public function getMiddlename();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setMiddlename($middlename);
	
	/** @return string */
	public function getLastname();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setLastname($lastname);
	
	/** @return string|null */
	public function getSuffix();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setSuffix($suffix);
	
	/** @return string|null */
	public function getCompany();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setCompany($company);
	
	/** @return string */
	public function getStreet();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setStreet($street);
	
	/** @return string */
	public function getCity();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setCity($city);
	
	/** @return string */
	public function getCountryId();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setCountryId($countryId);
	
	/** @return string|null */
	public function getRegion();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setRegion($region);
	
	/** @return string */
	public function getPostcode();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setPostcode($postcode);
	
	/** @return string */
	public function getTelephone();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setTelephone($telephone);
	
	/** @return bool */
	public function getDefaultBilling();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setDefaultBilling($defaultBilling);
	
	
	/** @return bool */
	public function getDefaultShipping();
	/** @return \MageB2B\Sublogin\Api\Data\AddressInterface */
	public function setDefaultShipping($defaultShipping);
}
<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Sublogin\Controller\Adminhtml\Sublogin;

use Magento\Backend\App\Action;

class Issubloginemailunique extends Action
{
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    protected $jsonFactory;

    /** @var \MageB2B\Sublogin\Model\SubloginRepository */
    protected $subloginRepository;

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param ForwardFactory $resultForwardFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $jsonFactory,
    	\MageB2B\Sublogin\Model\SubloginRepository $subloginRepository
    ) {
        parent::__construct($context);
        $this->jsonFactory = $jsonFactory;
        $this->subloginRepository = $subloginRepository;
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $post = $this->getRequest()->getPostValue();
        $email = $post['email'];

        $sublogin = $this->subloginRepository->getByField('email', $email);
        
        if ($sublogin && $sublogin->getId()) {
            $return['isUnique'] = 0;
        } else {
            $return['isUnique'] = 1;
        }

        /** @var $json \Magento\Framework\Controller\Result\Json */
        $json = $this->jsonFactory->create();
        $json->setData($return);
        return $json;
    }

    /**
     * Check the permission to run it
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('MageB2B_Sublogin::manage');
    }
}
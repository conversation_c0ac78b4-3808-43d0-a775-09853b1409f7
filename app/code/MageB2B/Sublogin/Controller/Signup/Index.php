<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Sublogin\Controller\Signup;

use MageB2B\Sublogin\Model\SubloginRepository;
use Magento\Customer\Model\ResourceModel\CustomerRepository;
use Magento\Framework\App\Action\Context;
use MageB2B\Sublogin\Helper\Data as DataHelper;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\DataObject;
use Magento\Framework\Controller\Result\JsonFactory;

class Index extends \Magento\Framework\App\Action\Action
{
    /** @var DataHelper */
    private $dataHelper;

    /** @var SubloginRepository */
    private $subloginRepository;

    /** @var \MageB2B\Sublogin\Helper\Config */
    private $configHelper;

    /** @var Validator */
    protected $formKeyValidator;

    /** @var PageFactory */
    private $resultPageFactory;

    /** @var JsonFactory */
    private $jsonFactory;

    /** @var CustomerRepository */
    private $customerRepository;

    /**
     * Index constructor.
     * @param Context $context
     * @param DataHelper $dataHelper
     * @param SubloginRepository $subloginRepository
     * @param Validator $formKeyValidator
     * @param PageFactory $resultPageFactory
     * @param JsonFactory $jsonFactory
     * @param CustomerRepository $customerRepository
     */
    public function __construct(
        Context $context,
        DataHelper $dataHelper,
        SubloginRepository $subloginRepository,
        Validator $formKeyValidator,
        PageFactory $resultPageFactory,
        JsonFactory $jsonFactory,
        CustomerRepository $customerRepository
    )
    {
        $this->dataHelper = $dataHelper;
        $this->subloginRepository = $subloginRepository;
        $this->configHelper = $this->dataHelper->getConfigHelper();
        $this->formKeyValidator = $formKeyValidator;
        $this->resultPageFactory = $resultPageFactory;
        $this->jsonFactory = $jsonFactory;
        $this->customerRepository = $customerRepository;
        parent::__construct($context);
    }

    public function execute()
    {
        $session = $this->dataHelper->getCustomerSession();
        // only these parameters are valid for input

        $validParams = array('firstname', 'lastname', 'email', 'entity_id');
        $paramsObject = new DataObject($validParams);
        $this->dataHelper->getEventManager()->dispatch('mageb2b_sublogin_signup_validate_params', array('params' => $paramsObject));
        $request = $this->getRequest();
        $postParams = $request->getParams();

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        $return = [];
        $url = $this->_url->getUrl('*/*/', ['_secure' => true]);
        $return['url'] = $url;
        $return['error'] = false;

        if ($request->isPost())
        {
            if (!$this->formKeyValidator->validate($this->getRequest())) {
                $return['error'] = true;
                $this->messageManager->addErrorMessage(__('Form key can not be validated.'));
                return $this->sendJson($return);
            }

            $postParams['entity_id'] = null;
            if (!isset($postParams['mainlogin_email'])) {
                $return['error'] = true;
                $this->messageManager->addErrorMessage(__('Mainlogin email is required.'));
                return $this->sendJson($return);
            } else {
                /** validate mainlogin email exist */
                try {
                    $mainloginCustomer = $this->customerRepository->get($postParams['mainlogin_email'], $this->dataHelper->getCurrentStore()->getWebsiteId());
                    $postParams['entity_id'] = $mainloginCustomer->getId();
                } catch (\Exception $e) {
                    $return['error'] = true;
                    $this->messageManager->addErrorMessage(__('Mainlogin email "%1" is wrong.', $postParams['mainlogin_email']));
                    return $this->sendJson($return);
                }
            }

            if ($postParams['email'] !== $postParams['cemail']) {
                $return['error'] = true;
                $this->messageManager->addErrorMessage(__('Email and email confirmation does not match.'));
                return $this->sendJson($return);
            }

            try {
                $postObject = new DataObject($postParams);
                $this->dataHelper->getEventManager()->dispatch('mageb2b_sublogin_signup_post_check', array('post' => $postObject));

                if ($postObject->getData('wrong_entity_id') == 1) {
                    throw new \Exception(__('Wrong Customer ID specified.'));
                }

                $subloginModel = $this->subloginRepository->getModel();
                foreach ($postParams as $key => $value) {
                    if (in_array($key, $validParams)) {
                        $subloginModel->setData($key, $value);
                    }
                }
                $subloginModel->setPassword($postParams['password']);
                $subloginModel->setSendBackendmails(1);
                if ($this->configHelper->get('sublogin/general/activate_on_signup')) {
                    $subloginModel->setActive(1);
                } else {
                    $subloginModel->setActive(0);
                }

                $subloginModel->setCreateSublogins(0);

                // overwrite from observer if customer_id (entity_id) is set from outside
                if ($postObject->getData('entity_id')) {
                    $subloginModel->setEntityId($postObject->getData('entity_id'));
                }
                $subloginModel->setStoreId($this->dataHelper->getCurrentStore()->getId());

                $this->subloginRepository->save($subloginModel);

                // @TODO: improve message based on activate or not (admin will set account to active ect.)
                $this->messageManager->addSuccessMessage(__('You successfully signed up for an account. You will receive an email with your account data.'));

            } catch(\Exception $e) {
                $return['error'] = true;
                $this->messageManager->addExceptionMessage($e, $e->getMessage());
            }

            return $this->sendJson($return);
        }

        /** @var \Magento\Framework\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $resultPage->getConfig()->getTitle()->set(__('Signup for a sublogin account.'));
        return $resultPage;
    }

    /**
     * @param $data
     * @return mixed
     */
    private function sendJson($data)
    {
        $json = $this->jsonFactory->create();
        $json->setData($data);
        return $json;
    }
}
<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Sublogin\Model\Config\Source;

use MageB2B\Sublogin\Helper\Config as ConfigHelper;


class Formfields
{
    protected $_formFields;
    protected $_adminAllowedFields;
    protected $_frontendAllowedFields;

    /** @var ConfigHelper $configHelper */
    protected $configHelper;

    /** @var SubloginHelper $subloginHelper */
    protected $subloginHelper;

    /** @var \Magento\Framework\Event\Manager */
    protected $eventManager;

    public function __construct(
        ConfigHelper $configHelper,
        \Magento\Framework\Event\Manager $eventManager
    ) {
        $this->configHelper = $configHelper;
        $this->eventManager  = $eventManager;
    }

    /**
     * Options getter
     * @return array
     */

    public function getAllOptions($isConfig = 0)
    {
        if (!isset($this->_formFields[$isConfig]))
        {
            $options = [];

            $options[] = array(
                'label'	=>	__('None'),
                'value'	=>	'',
            );

            $options[] = array(
                'label'	=>	__('Addresses'),
                'value'	=>	'address_ids',
            );

            $options[] = array(
                'label'	=>	__('Send Mail'),
                'value'	=>	'send_backendmails',
            );

            $options[] = array(
                'label'	=>	__('Optional email'),
                'value'	=>	'optional_email',
            );

            $options[] = array(
                'label'	=>	__('Can create sublogins'),
                'value'	=>	'create_sublogins',
            );

            $options[] = array(
                'label'	=>	__('Prefix'),
                'value'	=>	'prefix',
            );

            // in config we only want to use expire interval

            if ($isConfig == 1) {
                $options[] = array(
                    'label'	=>	__('Expire Interval'),
                    'value'	=>	'expire_interval',
                );
            } else {
                $options[] = array(
                    'label' => __('Date to Expire'),
                    'value' => 'expire_date'
                );
            }

            $options[] = array(
                'label'	=>	__('Is subscribed'),
                'value'	=>	'is_subscribed',
            );

            $options[] = array(
                'label'	=>	__('Active'),
                'value'	=>	'active',
            );


            $optionsObject = new \Magento\Framework\DataObject();
            $optionsObject->setData($options);
            $this->eventManager->dispatch('sublogin_formfields_config', [
                'fields_object' => $optionsObject,
                'fields'        => $options,
                'is_config'     => $isConfig
            ]);

            $this->_formFields[$isConfig]= $optionsObject->getData();
        }
        return $this->_formFields[$isConfig];
    }

    /**
     * return default values in an array
     *
     * @return array
     */
    public function getDefaultValues()
    {
        $defaultValues =  array(
            'send_backendmails'   		  => 0,
            'create_sublogins'	    	  => 0,
            'prefix'			    	  => '',
            'expire_interval'	   	      => '',
            'optional_email' 	   	      => '',
            'is_subscribed'		    	  => 0,
            'active'			          => 1,
        );

        $defaultValuesObj = new \Magento\Framework\DataObject();
        $defaultValuesObj->setData($defaultValues);
        $this->eventManager->dispatch('sublogin_formfields_config_default_values', [
            'default_values_obj' => $defaultValuesObj,
            'default_values'      => $defaultValues,
        ]);

        $defaultValues = $defaultValuesObj->getData();

        foreach ($defaultValues as $key => $v) {
            if ($this->configHelper->getDefaultValues($key) !== false) {
                if ($key == 'expire_interval') {
                    $expireInterValConfig = $this->configHelper->getDefaultValues($key);
                    $today = date('d-m-Y');
                    $expireInterval = date('d-m-Y', strtotime($today. ' + ' . $expireInterValConfig. ' days'));
                    $defaultValues[$key] = $expireInterval;
                }

                $defaultValues[$key] = $this->configHelper->getDefaultValues($key);
            }
        }

        $defaultValuesObj = new \Magento\Framework\DataObject();
        $defaultValuesObj->setData($defaultValues);
        $this->eventManager->dispatch('sublogin_formfields_config_default_values_after_set', [
            'default_values_obj' => $defaultValuesObj,
            'default_values'      => $defaultValues,
        ]);

        $defaultValues = $defaultValuesObj->getData();
        return $defaultValues;
    }

    /**
     * @return array
     */
    public function toOptionArray()
    {
        return $this->getAllOptions();
    }

    /**
     * @param $field
     * @param string $area
     * @return bool
     */
    public function isFieldAllowed($field, $area = 'admin')
    {
        if ($area == 'admin') {
            return $this->isFieldAllowedForAdmin($field);
        } else { // frontend
            return $this->isFieldAllowedForFront($field);
        }
    }

    /**
     * @param $field
     * @return bool
     */
    public function isFieldAllowedForAdmin($field)
    {
        if (in_array($field, $this->getAdminAllowedFields()))
            return true;

        return false;
    }

    /**
     * @return array
     */
    protected function getAdminAllowedFields()
    {
        if (!$this->_adminAllowedFields)
        {
            $_adminAllowedFields = $this->configHelper->get('sublogin/form_fields/admin');
            if (strpos($_adminAllowedFields, ',')) {
                $_adminAllowedFields = explode(',', $_adminAllowedFields);
            } else {
                $_adminAllowedFields = array($_adminAllowedFields);
            }

            /**
             * check for setting: sublogin/general/address_management
             * if its not \MageB2B\Sublogin\Model\Config\Source\Addressmanagement::OPTION_USE_CUSTOM_ADDRESSES
             * then remove address_ids field from allowed
             */
            if ($this->configHelper->get('sublogin/general/address_management') != \MageB2B\Sublogin\Model\Config\Source\Addressmanagement::OPTION_USE_CUSTOM_ADDRESSES) {
                $arrayIndex = array_search('address_ids', $_adminAllowedFields);
                if ($arrayIndex >= 0) {
                    unset($_adminAllowedFields[$arrayIndex]);
                }
            }

            $this->_adminAllowedFields = $_adminAllowedFields;
        }
        return $this->_adminAllowedFields;
    }

    /**
     * @param $field
     * @return bool
     */
    public function isFieldAllowedForFront($field)
    {
        if (in_array($field, $this->getFrontendAllowedFields())) {
            return true;
        }

        return false;
    }

    /**
     * @return array
     */
    protected function getFrontendAllowedFields()
    {
        if (!$this->_frontendAllowedFields)
        {
            $_frontendAllowedFields = $this->configHelper->get('sublogin/form_fields/frontend');
            if (strpos($_frontendAllowedFields, ',')) {
                $_frontendAllowedFields = explode(',', $_frontendAllowedFields);
            }
            else {
                $_frontendAllowedFields = array($_frontendAllowedFields);
            }

            $this->_frontendAllowedFields = $_frontendAllowedFields;
        }
        return $this->_frontendAllowedFields;
    }

    public function getRightSortOrder($fields, $index)
    {
        $sortOrders = array_keys($fields);
        $sortOrder = $index;
        $found = 0;

        if (!in_array($sortOrder, $sortOrders)) {
            return $sortOrder;
        } else {
            $end = $index + 100;
            $index++;
            for ($i=$index;$i<=$end;$i++) {
                if (!in_array($i, $sortOrders)) {
                    $sortOrder = $i;
                    $found = 1;
                    break;
                }
            }
        }

        if (!$found) {
            $sortOrder = time();
        }

        return $sortOrder;
    }
}

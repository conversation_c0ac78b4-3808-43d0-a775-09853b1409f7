<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Sublogin\Model\Config\Source;

class Addressmanagement
{
    protected $_options;

    const OPTION_NOT_USE_CUSTOMER_ADDRESSES= 0;
    const OPTION_USE_ALL_CUSTOMER_ADDRESSES = 1;
    const OPTION_USE_CUSTOM_ADDRESSES = 2;

    /**
     * Options getter
     * @return array
     */
    public function getAllOptions()
    {
        if (!$this->_options) {
            $options = [];

            $options[] = [
                'label'	=>	__('Disallow using customer addresses'),
                'value'	=>	self::OPTION_NOT_USE_CUSTOMER_ADDRESSES,
            ];

            $options[] = [
                'label'	=>	__('Allow to use all customer addresses'),
                'value'	=>	self::OPTION_USE_ALL_CUSTOMER_ADDRESSES,
            ];

            $options[] = [
                'label'	=>	__('Use custom address management'),
                'value'	=>	self::OPTION_USE_CUSTOM_ADDRESSES,
            ];

            $this->_options = $options;
        }
        return $this->_options;
    }

    /**
     * @return array
     */
    public function toOptionArray()
    {
        return $this->getAllOptions();
    }
}
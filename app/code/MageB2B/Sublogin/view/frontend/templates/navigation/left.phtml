<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 * @var $block \MageB2B\Sublogin\Block\Navigation
 */
?>
<?php if (!$block->canAccessSubloginMenu()) {
    return;
}?>

    <?php
        $isMySubloginsLinkActive = false;
        if (in_array($this->getRequest()->getFullActionName(), array('sublogin_sublogin_index', 'sublogin_sublogin_edit', 'sublogin_sublogin_add'))) {
            $isMySubloginsLinkActive = true;
        }
    ?>
    <li class="nav item <?php echo $isMySubloginsLinkActive ? 'current' : ''?>">
        <a href="<?php echo $block->getUrl('sublogin/sublogin/index') ?>">
            <?php echo __('My Sublogins') ?>
        </a>
    </li>

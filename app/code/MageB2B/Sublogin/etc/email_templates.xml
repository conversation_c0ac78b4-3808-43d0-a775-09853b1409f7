<?xml version="1.0"?>
<!--
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Email:etc/email_templates.xsd">
    <template id="sublogin_email_new" label="Sublogin - new account" file="sublogin_email_new.html" type="html" module="MageB2B_Sublogin" area="frontend"/>
	<template id="sublogin_email_confirmation" label="Sublogin - new account (confirmation)" file="sublogin_confirmation.html" type="html" module="MageB2B_Sublogin" area="frontend"/>
	<template id="sublogin_email_reset_password" label="Sublogin - password reset" file="sublogin_email_reset_password.html" type="html" module="MageB2B_Sublogin" area="frontend"/>
	<template id="sublogin_email_expire_refresh" label="Sublogin - refresh expired account" file="sublogin_email_expire_refresh.html" type="html" module="MageB2B_Sublogin" area="frontend"/>

	<template id="sublogin_email_password_reset_confirmation" label="Sublogin Email Password Reset Confirmation" file="sublogin_password_reset_confirmation.html" type="html" module="MageB2B_Sublogin" area="frontend"/>
</config>

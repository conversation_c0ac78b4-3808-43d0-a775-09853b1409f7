<?xml version="1.0"?>
<!--
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
    	<add id="MageB2B_Sublogin::sublogin" title="Sublogin" module="MageB2B_Sublogin" sortOrder="80" resource="MageB2B_Sublogin::sublogin"/>
		<add id="MageB2B_Sublogin::manage" title="All Sublogins" module="MageB2B_Sublogin" sortOrder="10" parent="MageB2B_Sublogin::sublogin" action="sublogin/sublogin/index" resource="MageB2B_Sublogin::manage"/>
	</menu>
</config>
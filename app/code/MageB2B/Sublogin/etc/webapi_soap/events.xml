<?xml version="1.0"?>
<!--
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="customer_load_after">
        <observer name="mageb2b_sublogin_customer_load_after" instance="MageB2B\Sublogin\Observer\CustomerLoadAfter" />
    </event>
</config>

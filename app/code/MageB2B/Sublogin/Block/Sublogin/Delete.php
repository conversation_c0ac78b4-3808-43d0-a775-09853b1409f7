<?php
/**
 * @package MageB2B_Sublogin 1.0.2
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Sublogin\Block\Sublogin;

class Delete extends \Magento\Framework\View\Element\Template
{
	/** @var \MageB2B\Sublogin\Helper\Data */
    protected $subloginHelper;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \MageB2B\Sublogin\Helper\Data $subloginHelper,
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \MageB2B\Sublogin\Helper\Data $subloginHelper,
        array $data = []
    ) {
    	$this->subloginHelper= $subloginHelper;
        parent::__construct($context, $data);
    }

    /**
     * @return string
     */
    protected function _toHtml()
    {
    	$html = '';
    	$currentSublogin = $this->subloginHelper->getCurrentSublogin();
    	if ($this->subloginHelper->getCustomerSession()->isLoggedIn() && !(is_null($currentSublogin))) {
    		$url = $this->getUrl('sublogin/sublogin/deleteown', array('id' => $currentSublogin->getId()));
    		$confirmationMessage = __('Are you sure?');
    		$html = '<div class="box-info"><div class="box-head"><h2>' .
      		__('Delete my account') .
      		'</h2></div><div class="col2-set">'. __('If you wish to delete your account, please click <a onclick="return confirm(\'%1\')" href="%2">here</a>.', $confirmationMessage, $url) .
      		'</div></div>';
    	}
    	return $html;
    }
}

"Sublogins","Sublogins"
"My Sublogins","My Sublogins"
"Add","Add"
"Customer Id","Customer Id"
"Email ""%s"" already exists","Email ""%s"" already exists"
"Your password reset link has expired.","Your password reset link has expired."
"Your account is deactivated. You have received an email to reactivate the account.","Your account is deactivated. You have received an email to reactivate the account."
"Store Id","Store Id"
"eMail","eMail"
"Send Mail","Send Mail"
"Create new sublogin","Create new sublogin"
"Firstname","Firstname"
"Lastname","Lastname"
"Days to Expire","Days to Expire"
"Date to Expire","Date to Expire"
"Customer Email","Customer Email"
"Sublogin Customer Id","Sublogin Customer Id"
"Sublogin Email","Sublogin Email"
"Edit Customer","Edit Customer"
"Mail","Mail",
"Expire Date","Expire Date"
"Expire interval","Expire interval"
"Restriction My Sublogin area","Restriction My Sublogin area"
"If activated, a sublogin can not see the customer area my sublogins.","If activated, a sublogin can not see the customer area my sublogins."
"Restrict order view for sublogins","Restrict order view for sublogins"
"If activated, a sublogin can only see his own orders in customer area.","If activated, a sublogin can only see his own orders in customer area."
"If an account will be reactivated, type in here how long (in days) the accounts will be active again","If an account will be reactivated, type in here how long (in days) the accounts will be active again"
"Email settings","Email settings"
"Send form (email)","Send form (email)"
"Send from (name)","Send from (name)"
"BCC Receiver for emails","BCC Receiver for emails"
"Edit sublogin inside grid","Edit sublogin inside grid"
"If you have over 100 sublogins per customer you should disable this option else not everything might be saved (too much data).","If you have over 100 sublogins per customer you should disable this option else not everything might be saved (too much data)."
"If you need more than one bcc receiver, just split them with ";"","If you need more than one bcc receiver, just split them with ";""
"Type in the email address which the email should come from.","Type in the email address which the email should come from."
"Type in the name (e.g. of your company) which the email should come from.","Type in the name (e.g. of your company) which the email should come from."
"No sublogins created for your account yet. Just click on create new sublogin to create a new one.","No sublogins created for your account yet. Just click on create new sublogin to create a new one."
"Subscribe for Newsletter","Subscribe for Newsletter"
"Can create sublogins","Can create sublogins"
"Is subscribed","Is subscribed"
"Sublogins Configuration","Sublogins Configuration"
"Sublogins","Sublogins"
"Can create sublogins","Can create sublogins"
"If allowed, customer can create sublogins in frontend area","If allowed, customer can create sublogins in frontend area"
"Max. number of sublogins","Max. number of sublogins"
"Configure the max. amount of sublogins the customer can create. 0 is unlimited.","Configure the max. amount of sublogins the customer can create. 0 is unlimited."
"Manage Sublogins","Manage Sublogins"
"Edit sublogin %s for customer %s","Edit sublogin %s for customer %s"
"Add new sublogin for customer %s","Add new sublogin for customer %s"
"Edit Sublogin","Edit Sublogin"
"Delete Sublogin","Delete Sublogin"
"Only type in password if you need to change. Otherwise the password will not be set.","Only type in password if you need to change. Otherwise the password will not be set."
"Import Sublogins","Import Sublogins"
"Add Sublogin","Add Sublogin"
"Prefix","Prefix"
"Addresses","Addresses"
"Sublogin","Sublogin"
"Order Approval Required","Order Approval Required"
"Send order email also to main account","Send order email also to main account"
"If activated, the order email made by a sublogin will be also sent to the main account holder.","If activated, the order email made by a sublogin will be also sent to the main account holder."
"Order is approved.","Order is approved"
"OrderID not found","OrderID not found"
"Order is declined.","Order is declined."
"Order needs approval","Order needs approval"
"Approve Order","Approve Order"
"Decline Order","Decline Order"
"Form fields","Form fields"
"Delete not approved orders","Delete not approved orders"
"If activated, main login can delete not approved orders of their sublogin.","If activated, Main login can delete not approved orders of their sublogin."
"Order Approval Cart Page Notice","Order Approval Cart Page Notice"
"Save sublogin address","Save sublogin address"
"Enable mainaccount login","Enable mainaccount login"
"If activated, a mainaccount can directly login to a sublogin without having their password.","If activated, a mainaccount can directly login to a sublogin without having their password."
"Budget settings","Budget settings"
"Should mainaccount to be able to set budgets for their sublogins in frontend?","Should mainaccount to be able to set budgets for their sublogins in frontend?"
"If you need more than one bcc receiver, just split them with "";""","If you need more than one bcc receiver, just split them with "";"""
"Email template new sublogin","Email template new sublogin"
"Email template new password","Email template new password"
"Email template expired account refreshing","Email template expired account refreshing"
"Email template Main login order alert","Email template Main login order alert"
"Email template Order Require Approval","Email template Order Require Approval"
"Email template Order Declined","Email template Order Declined"
"Separate multiple entries by comma. Example: acl_identifier1,acl_identifier2","Separate multiple entries by comma. Example: acl_identifier1,acl_identifier2"
"If set to no then sublogin address will not be saved in mainlogin's address book. If set to yes then default Magento process will be done.","If set to no then sublogin address will not be saved in mainlogin's address book. If set to yes then default Magento process will be done."
"General settings","General settings"
"If set to yes then sublogin orders needs to be approved by its main account otherwise orders will be approved automatically.","If set to yes then sublogin orders needs to be approved by its main account otherwise orders will be approved automatically."
"Selected fields will be displayed in sublogin area at frontend.","Selected fields will be displayed in sublogin area at frontend."
"Selected fields will be displayed in sublogin area at admin.","Selected fields will be displayed in sublogin area at admin."
"Format: 2015-05-31 (Year-Month-Date)","Format: 2015-05-31 (Year-Month-Date)"
"Days to Expire + Date to Expire","Days to Expire + Date to Expire"
"Default values for form fields","Default values for form fields"
"Leave blank if don't want to show notice.","Leave blank if don't want to show notice."
"If an account will be reactivated, type in here how long (in days) the accounts will be active again. Specify 0 if you don't want to deactivate account. But 0 will work only when expire_date is not specified while creating/updating sublogin. Otherwise specified date will be considered as expired date.","If an account will be reactivated, type in here how long (in days) the accounts will be active again. Specify 0 if you don't want to deactivate account. But 0 will work only when expire_date is not specified while creating/updating sublogin. Otherwise specified date will be considered as expired date."
"Per Order Limit","Per Order Limit"
"Manage Budget","Manage Budget"
"Manage ACL","Manage ACL"
"Sublogin Budget","Sublogin Budget"
"Add budget","Add budget"
"Add new budget","Add new budget"
"Budget Type","Budget Type"
"Same configuration already exist for this sublogin: Year: "%s", Month: "%s" and Day: "%s"","Same configuration already exist for this sublogin: Year: "%s", Month: "%s" and Day: "%s""
"You are not allowed to place order which has total greater than %s","You are not allowed to place order which has total greater than %s"
"You can not proceed as your purchase limit %s for this %s is reached.","You can not proceed as your purchase limit %s for this %s is reached."
"You can continue with this order if your cart total is maximum upto %s","You can continue with this order if your cart total is maximum upto %s"
"Use shared card","Use shared card"
"Main account budget","Main account budget"
"Disable","Disable"
"Customer optional email templates","Customer optional email templates"
"Select for which email templates the optional customer email address is valid.","Select for which email templates the optional customer email address is valid."
"Sublogin optional email templates","Sublogin optional email templates"
"Select for which email templates the optional sublogin email address is valid.","Select for which email templates the optional sublogin email address is valid."
"Save customer email in order table","Save customer email in order table"
"Instead of inserting the sublogin email in sales order table, you can select yes to use the customer email address from main account to be saved.","Instead of inserting the sublogin email in sales order table, you can select yes to use the customer email address from main account to be saved."
"If activated a sublogins cart is shared with customer incl. connected sublogins","If activated a sublogins cart is shared with customer incl. connected sublogins"
"Apply main account budget limit when no sublogin budget found","Apply main account budget limit when no sublogin budget found"
"Apply main account budget limit and skip sublogin budget","Apply main account budget limit and skip sublogin budget"
"Customer","Customer"
"Transfer existing customers to sublogin","Transfer existing customers to sublogin"
"Sublogin and Parent customer should be different","Sublogin and Parent customer should be different"
"Sublogin Customer","Sublogin Customer"
"Parent Customer","Parent Customer"
"Activate","Activate"
"Deactivate","Deactivate"
"Optional email","Optional email"
"Please select order(s)","Please select order(s)"
"Changed order(s) to approved.","Changed order(s) to approved."
"Changed order(s) to declined.","Changed order(s) to declined."
"Order approval state","Order approval state"
"Order approval status","Order approval status"
"Order approved state","Order approved state"
"Order approved status","Order approved status"
"Order not approved state","Order not approved state"
"Order not approved status","Order not approved status"
"Are you sure you want to delete this sublogin?","Are you sure you want to delete this sublogin?"
"Actions","Actions"
"Select item(s)","Select item(s)"
"Please select","Please select"
"Please select one action.","Please select one action."
"Please select at least one item to update.","Please select at least one item to update."
"%d items successfully updated.","%d items successfully updated."
"Massactions in frontend","Massactions in frontend"
"You have no assigned sublogins.","You have no assigned sublogins."
"Optional Email","Optional Email"
"If specified, then email alerts of this customer will be sent to the specified email address.","If specified, then email alerts of this customer will be sent to the specified email address."
"Order approval amount check","Order approval amount check"
"Amount","Amount"
"Year","Year"
"Month","Month"
"Day","Day"
"Yearly","Yearly"
"Monthly","Monthly"
"Daily","Daily"
"Per Order","Per Order"
"Attention: At current state only main customers can create sublogins in frontend. Restriction for sublogins is currently activated through the system configuration.","Attention: At current state only main customers can create sublogins in frontend. Restriction for sublogins is currently activated through the system configuration."
"Giving sublogins the right to create sublogins themselves will not take effect. Please change the system configuration first.","Giving sublogins the right to create sublogins themselves will not take effect. Please change the system configuration first."
"You don't have access to this page.","You don't have access to this page."
"Custom ACL","Custom ACL"
"Cart lock","Cart lock"
"Checkout lock","Checkout lock"
"My account lock","My account lock"
"Custom Identifier","Custom Identifier"
"Login Post URL for sublogins","Login Post URL for sublogins"
"Restrict address book only to assigned mainlogin entries.","Restrict address book only to assigned mainlogin entries."
"Default Value Can Create Sublogins","Default Value Can Create Sublogins"
"Order approval settings","Order approval settings"
"Type in your URL part for the sublogin to be redirect to.","Type in your URL part for the sublogin to be redirect to."
"Create a new sublogin","Create a new sublogin"
"Filter by type","Filter by type"
"Filter","Filter"
"Show all types","Show all types"
"Default value for can create sublogins attribute","Standardwert für das Attribut 'Kann Unterkonten erstellen'"
"Internal name / code of your checkout module","Interner Name / Code Ihres Checkout Moduls"
"Only touch in case you are using another checkout module.","Nur ändern wenn Sie ein anderes Checkout Modul implementiert haben."
"Order needs no approval","Order needs no approval"
"Is not subscribed","Not subscribed to newsletter"
"Send backendmails","Send backendmails"
"Send no backendmails","Send no backendmails"
"Create sublogins","Create sublogins"
"Can not create sublogins","Can not create sublogins"
"If activated, the restriction will take effect that just address book entries can be used which are assigned to the sublogins.","If activated, the restriction will take effect that just address book entries can be used which are assigned to the sublogins."
"Selected fields will be displayed at frontend.","Selected fields will be displayed at frontend."
"Selected fields will be displayed at admin.","Selected fields will be displayed at admin."
"Selected address fields which will be displayed at admin.","Selected address fields which will be displayed at admin."
"Sublogin Addresses","Sublogin Addresses"
"Delete my account","Delete my account"
"Are you sure?","Are you sure?"
"If you wish to delete your account, please click <a onclick=""return confirm('%1')"" href=""%2"">here</a>.","If you wish to delete your account, please click <a onclick=""return confirm('%1')"" href=""%2"">here</a>."
"You do not have any sublogin.","You do not have any sublogin."
"You can now see the account of %1.","You can now see the account of %1."
"You just logged out from the account of %1.","You just logged out from the account of %1."
"You are not allowed to access this page.","You are not allowed to access this page."
"Reports","Reports"
"Sublogin Orders Report","Sublogin Orders Report"
"Sublogin Products Report","Sublogin Products Report"
"All Sublogins","All Sublogins"
"No budget found","No budget found"
"Address management for sublogins","Address management for sublogins"
"Behavior for address management","Behavior for address management"
"Use custom address mangement","Use custom address mangement"
"Disallow using customer addresses","Disallow using customer addresses"
"Allow to use all customer addresses","Allow to use all customer addresses"
"Show customer address under my account for sublogin","Show customer address under my account for sublogin"
"Enable add new address option for sublogins in checkout","Enable add new address option for sublogins in checkout"
"Decide if sublogins should have the ability to add new address during the checkout process.","Decide if sublogins should have the ability to add new address during the checkout process."
"Require email confirmation of new created sublogin","Require email confirmation of new created sublogin"
"Sublogin need to confirm account before access is granted.","Sublogin need to confirm account before access is granted."
"Match email domain on registration and transfer to sublogin","Match email domain on registration and transfer to sublogin"
"If used, customer will be transferred as a sublogin once it got a match with an existing email domain customer.","If used, customer will be transferred as a sublogin once it got a match with an existing email domain customer."
"If sublogin is able to delete own account in frontend","If sublogin is able to delete own account in frontend"
"Can Edit Order In Approval State","Can Edit Order In Approval State"
"If set to yes then customer can edit sublogin order which is still in approval state.","If set to yes then customer can edit sublogin order which is still in approval state."
"Auto approve edited order","Auto approve edited order"
"If set to yes, then order with status ""Approval"" will be ""Approved"" once main login finshed editing an order","If set to yes, then order with status ""Approval"" will be ""Approved"" once main login finshed editing an order"
"Allowed budget types","Allowed budget types"
"Selected budget types will be used when creating budget","Selected budget types will be used when creating budget"
"Default Year begin from","Default Year begin from"
"Will apply to budget types: year and yearly. Format: MM-DD (MM=Month, DD=Date). Example: 01-01","Will apply to budget types: year and yearly. Format: MM-DD (MM=Month, DD=Date). Example: 01-01"
"Email templates for store owner","Email templates for store owner"
"Store owner will get an email alert for the selected email templates","Store owner will get an email alert for the selected email templates"
"Mainlogin Edited Order Template","Mainlogin Edited Order Template"
"Deleted Order Email Template","Deleted Order Email Template"
"Comment settings","Comment settings"
"Enable comments for","Enable comments for"
"Editing an order","Editing an order"
"Approving an order","Approving an order"
"Declining an order","Declining an order"
"Customer Email","Customer Email"
"Sublogin Information","Sublogin Information"
"Automatically add new customer address","Automatically add new customer address"
"Customer Addresses","Customer Addresses"
"Prefix","Prefix"
"Store","Store"
"Add order details to selected email templates","Add order details to selected email templates"
"Admin address fields","Admin address fields"
"Expire Interval","Expire Interval"
"If a new customer address has been created, then we are adding it the restricted address book of sublogin","If a new customer address has been created, then we are adding it the restricted address book of sublogin"
"No Login Needed","No Login Needed"
"This sublogin has no saved addresses.","This sublogin has no saved addresses."
"Order approval","Order approval"
"Token not found.","Token not found."
"Order was already approved or declined by email.","Order was already approved or declined by email."
"Order ID not found.","Order ID not found."
"Order model not found.","Order model not found."
"Token does not match.","Token does not match."
"Order %1 is approved.","Order %1 is approved."
"Order %1 is declined.","Order %1 is declined."
"Confirm Email","Confirm Email"
"Email and Email Confirmation does not match.","Email and Email Confirmation does not match."
"Wrong Customer ID specified.","Wrong Customer ID specified."
"Signup for a sublogin account.","Signup for a sublogin account."
"You successfully signed up for an account. You will receive an email with your account data.","You successfully signed up for an account. You will receive an email with your account data."
"Content settings","Content settings"
"Static block for sublogin grid at frontend","Static block for sublogin grid at frontend"
"Specify identifier of a static block, content of which you want to display just before the sublogin grid at frontend","Specify identifier of a static block, content of which you want to display just before the sublogin grid at frontend"
"Activate sublogin account on signup by default","Activate sublogin account on signup by default"
"Selected fields will be displayed in sublogin create form at frontend","Selected fields will be displayed in sublogin create form at frontend"
"Selected fields will be displayed in sublogin create form at admin","Selected fields will be displayed in sublogin create form at admin"
"Return to primary account","Return to primary account"
"You just logged out from the account of %1.","You just logged out from the account of %1."
"Your account refresh link has expired - try to login to get another email.","Your account refresh link has expired - try to login to get another email."
"This account doesn't exist anymore and can therefore not be refreshed.","This account doesn't exist anymore and can therefore not be refreshed."
"Successfully activated this account.","Successfully activated this account."
"Your account confirmation link has expired. We just resend confirmation email so you can confirm your account.","Your account confirmation link has expired. We just resend confirmation email so you can confirm your account."
"This account doesn't exist anymore and can therefore not be confirmed.","This account doesn't exist anymore and can therefore not be confirmed."
"The sublogin has been deleted.","The sublogin has been deleted."
"We can't find a sublogin to delete.","We can't find a sublogin to delete."
"Customer successfully transformed to sublogin.","Customer successfully transformed to sublogin."
"You successfully signed up for an account. You will receive an email with your account data.","You successfully signed up for an account. You will receive an email with your account data."
"Email and email confirmation does not match.","Email and email confirmation does not match."
"Mainlogin email "%1" is wrong.","Mainlogin email "%1" is wrong."
"Mainlogin email is required.","Mainlogin email is required."
"Form key can not be validated.","Form key can not be validated."

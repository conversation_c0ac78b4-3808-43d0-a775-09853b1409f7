<?php
/**
 * @package MageB2B_Sublogin 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Sublogin\Plugins;

use Magento\Customer\Model\ResourceModel\AddressRepository;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\Search\FilterGroupBuilder;

class AddressRepositoryPlugin
{
    /** @var \MageB2B\Sublogin\Helper\Data */
    private $subloginHelper;

    /**
     * @var \Magento\Framework\Api\FilterBuilder
     */
    protected $filterBuilder;

    /**
     * @var FilterGroupBuilder
     */
    protected $_filterGroupBuilder;

    /**
     * @var \Magento\Multishipping\Model\Checkout\Type\Multishipping
     */
    protected $_multishipping;

    public function __construct(
        \MageB2B\Sublogin\Helper\Data $subloginHelper,
        \Magento\Framework\Api\FilterBuilder $filterBuilder,
        FilterGroupBuilder $_filterGroupBuilder,
        \Magento\Multishipping\Model\Checkout\Type\Multishipping $_multishipping
    )
    {
        $this->subloginHelper = $subloginHelper;
        $this->filterBuilder = $filterBuilder;
        $this->_filterGroupBuilder = $_filterGroupBuilder;
        $this->_multishipping = $_multishipping;
    }

    /**
     * @param AddressRepository $addressRepository
     * @param SearchCriteriaInterface $searchCriteria
     * @return array
     */
    public function beforeGetList(AddressRepository $addressRepository, SearchCriteriaInterface $searchCriteria)
    {
        $sublogin = $this->subloginHelper->getCurrentSublogin();
        if ($sublogin &&
            $this->subloginHelper->getConfigHelper()->get('sublogin/general/address_management') != \MageB2B\Sublogin\Model\Config\Source\Addressmanagement::OPTION_USE_ALL_CUSTOMER_ADDRESSES
        ) {
            $searchCriteria = $this->_applyRestrictionForSublogin($searchCriteria);
        } else if ($sublogin &&
            $this->subloginHelper->getConfigHelper()->get('sublogin/general/address_management') == \MageB2B\Sublogin\Model\Config\Source\Addressmanagement::OPTION_USE_ALL_CUSTOMER_ADDRESSES
        ) {
            $searchCriteria = $this->_applyRestrictionForSubloginUseAllCustomerAddress($searchCriteria);
        } else {
            // $this->_noRestriction($customer);
        }

        return [$searchCriteria];
    }

    /**
     * Here we are applying conditions so logged in sublogin addresses and
     * its parent customer addresses will be returned in collection
     * @param $searchCriteria SearchCriteriaInterface
     */
    protected function _applyRestrictionForSubloginUseAllCustomerAddress($searchCriteria)
    {
        $customerId = $this->_multishipping->getCustomer()->getId();
        if ($customerId) {
            /**
             * At this point, filter for parent_id is already set in $searchCriteria
             */

            /** @var $sublogin \MageB2B\Sublogin\Model\Sublogin */
            $sublogin = $this->subloginHelper->getCurrentSublogin();
            // get sublogin addresses
            $subloginAddressesIds = $sublogin->getOrigSubloginAddressesIds();
            if (!is_array($subloginAddressesIds) && strpos($subloginAddressesIds, ',') === true) {
                $subloginAddressesIds = explode(',', $subloginAddressesIds);
            }

            // get all address ids of sublogin by customer
            $allSubloginAddressIds = $this->subloginHelper->getAllSubloginAddressIdsByCustomer($customerId);
            if (count($allSubloginAddressIds) > 0) {
                $notAllowedAddressIds = array_diff($allSubloginAddressIds, $subloginAddressesIds);
                if (count($notAllowedAddressIds)) {
                    /** set "nin" filter for entity_id */
                    $filter =  $this->filterBuilder->setField('entity_id')
                        ->setValue(implode(',', $notAllowedAddressIds))
                        ->setConditionType('nin')
                        ->create();

                    $filterGroups = $searchCriteria->getFilterGroups();
                    $newFilterGroup = $this->_filterGroupBuilder->setFilters([$filter])->create();
                    $filterGroups[] = $newFilterGroup;
                    $searchCriteria->setFilterGroups($filterGroups);
                }
            }
        } else {
            /** unset parent_id filter and add additional filter for parent_id to set its value to -1 */
            $filter =  $this->filterBuilder->setField('parent_id')
                ->setValue(-1)
                ->setConditionType('eq')
                ->create();

            $filterGroups = $searchCriteria->getFilterGroups();
            unset($filterGroups[0]); // its to unset filter applied using parent_id having some value of parent_id

            $newFilterGroup = $this->_filterGroupBuilder->setFilters([$filter])->create();
            $filterGroups[] = $newFilterGroup;
            $searchCriteria->setFilterGroups($filterGroups);
        }

        return $searchCriteria;
    }

    /**
     * @param $searchCriteria SearchCriteriaInterface
     * @return $searchCriteria SearchCriteriaInterface
     */
    protected function _applyRestrictionForSublogin($searchCriteria)
    {
        /** @var $sublogin \MageB2B\Sublogin\Model\Sublogin */
        $sublogin = $this->subloginHelper->getCurrentSublogin();
        if ($sublogin) {
            // get sublogin addresses
            $subloginAddressesIds = $sublogin->getOrigSubloginAddressesIds();
            if (!is_array($subloginAddressesIds) && strpos($subloginAddressesIds, ',') === true) {
                $subloginAddressesIds[] = explode(',', $subloginAddressesIds);
            }

            /**
             * On "checkout" page, show addresses in dropdown based on config setting: sublogin/general/address_management
             * if OPTION_USE_CUSTOM_ADDRESSES, then show sublogin addresses + assigned customer addresses
             * if OPTION_USE_ALL_CUSTOMER_ADDRESSES, then show sublogin addresses + all customer addresses
             * if OPTION_NOT_USE_CUSTOMER_ADDRESSES, then only show sublogin addresses
             */
            if ($this->subloginHelper->isCheckoutArea() && $this->subloginHelper->getConfigHelper()->get('sublogin/general/address_management') == \MageB2B\Sublogin\Model\Config\Source\Addressmanagement::OPTION_USE_CUSTOM_ADDRESSES) {
                if ($sublogin->getOrigCustomerAddressIds() != null) {
                    $subloginAddressesIds = array_merge($subloginAddressesIds, $sublogin->getOrigCustomerAddressIds());
                }
            } else {
                if ($this->subloginHelper->isCustomerAddressBookArea() &&
                    $this->subloginHelper->getConfigHelper()->get('sublogin/general/show_customeraddress_under_myaccount') &&
                    ($this->subloginHelper->getConfigHelper()->get('sublogin/general/address_management') == \MageB2B\Sublogin\Model\Config\Source\Addressmanagement::OPTION_USE_CUSTOM_ADDRESSES )
                ) {
                    if ($sublogin->getCustomerAddressIds() != null) {
                        $subloginAddressesIds = array_merge($subloginAddressesIds, $sublogin->getCustomerAddressIds());
                    }
                }
            }

            $filter =  $this->filterBuilder->setField('entity_id')
                ->setValue($subloginAddressesIds)
                ->setConditionType('in')
                ->create();

            $filterGroups = $searchCriteria->getFilterGroups();
            unset($filterGroups[0]); // its to unset filter applied using parent_id having some value of parent_id

            $newFilterGroup = $this->_filterGroupBuilder->setFilters([$filter])->create();
            $filterGroups[] = $newFilterGroup;
            $searchCriteria->setFilterGroups($filterGroups);

            /**
             * We need to skip setting default addresses to null on checkout pages
             * As its setting customer default addresses to null and so
             * magento is setting sublogin addresses as customer default addresses
             */
            $defaultBillingSublogin = $sublogin->getDefaultBilling();
            $defaultShippingSublogin = $sublogin->getDefaultShipping();
            if (!$this->subloginHelper->isCheckoutArea()) {
                if ($defaultBillingSublogin) {
                    $this->subloginHelper->getCustomerSession()->getCustomer()->setDefaultBilling($defaultBillingSublogin);
                } else {
                    /** without this code, customer's default billing will be set as default billing for sublogin */
                    $this->subloginHelper->getCustomerSession()->getCustomer()->setDefaultBilling(null);
                }
                if ($defaultShippingSublogin) {
                    $this->subloginHelper->getCustomerSession()->getCustomer()->setDefaultShipping($defaultShippingSublogin);
                } else {
                    /** without this code, customer's default shipping will be set as default shipping for sublogin */
                    $this->subloginHelper->getCustomerSession()->getCustomer()->setDefaultShipping(null);
                }
            }
        } else {
            $customerId = $this->_multishipping->getCustomer()->getId();

            $subloginAddressIds = $this->subloginHelper->getAllSubloginAddressIdsByCustomer($customerId);
            if (!empty($subloginAddressIds)) {
                $filter =  $this->filterBuilder->setField('entity_id')
                    ->setValue($subloginAddressIds)
                    ->setConditionType('nin')
                    ->create();

                $filterGroups = $searchCriteria->getFilterGroups();
                unset($filterGroups[0]); // its to unset filter applied using parent_id having some value of parent_id

                $newFilterGroup = $this->_filterGroupBuilder->setFilters([$filter])->create();
                $filterGroups[] = $newFilterGroup;
                $searchCriteria->setFilterGroups($filterGroups);
            }
        }
        return $searchCriteria;
    }
}
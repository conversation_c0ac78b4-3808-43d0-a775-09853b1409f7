<?php
/**
 * Copyright (c) 2008-2018 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse <<EMAIL>>
 */
namespace Payolution\Payments\Model\Config\Source;

use Magento\Framework\App\Config\ScopeConfigInterface;

/**
 * Class SpecificCountries
 * @package Payolution\Payments\Model\Config\Source
 */
class SpecificCountries implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * @var ScopeConfigInterface
     */
    protected $_scopeConfig;

    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $_request;

    /**
     * Options array
     *
     * @var array
     */
    protected $_options;

    /**
     * Countries
     *
     * @var \Magento\Directory\Model\ResourceModel\Country\Collection
     */
    protected $_countryCollection;

    /**
     * SpecificCountries constructor.
     *
     * @param \Magento\Directory\Model\ResourceModel\Country\Collection $countryCollection
     * @param \Magento\Framework\View\Element\Context $context
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        \Magento\Directory\Model\ResourceModel\Country\Collection $countryCollection,
        \Magento\Framework\View\Element\Context $context,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->_countryCollection = $countryCollection;
        $this->_scopeConfig = $scopeConfig;
        $this->_request = $context->getRequest();
    }

    /**
     * Return options array
     *
     * @param boolean $isMultiselect
     * @param string|array $foregroundCountries
     * @return array
     */
    public function toOptionArray($isMultiselect = false, $foregroundCountries = '')
    {
        if (!$this->_options) {

            $allowedCountries = $this->_scopeConfig->getValue(
                'general/country/allow',
                \Magento\Framework\App\Config\ScopeConfigInterface::SCOPE_TYPE_DEFAULT
            );
            // check if we have a range of allowed countries for the current website
            if ($website = $this->_request->getParam('website', 0)) {
                $allowedCountries = $this->_scopeConfig->getValue(
                    'general/country/allow',
                    \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE,
                    $website
                );
            }
            if ($allowedCountries) {
                $this->_countryCollection->addCountryCodeFilter(explode(',', $allowedCountries));
            }

            $this->_options = $this->_countryCollection->loadData()->setForegroundCountries(
                $foregroundCountries
            )->toOptionArray(
                false
            );
        }

        $options = $this->_options;
        if (!$isMultiselect) {
            array_unshift($options, ['value' => '', 'label' => __('--Please Select--')]);
        }

        return $options;
    }
}

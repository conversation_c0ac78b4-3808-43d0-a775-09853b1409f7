<?xml version="1.0"?>
<!--
* Copyright (c) 2008-2016 dotSource GmbH.
* All rights reserved.
* http://www.dotsource.de
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Payolution_Payments::css/payolution.css" media='screen' rel='stylesheet' type='text/css'/>
    </head>
    <body>
        <referenceBlock name="product.info.addtocart">
            <block
                class="Payolution\Payments\Block\Catalog\Product\View\Instalment"
                name="payolution.installment.plan"
                template="Payolution_Payments::catalog/product/view/instalment.phtml"/>
        </referenceBlock>
    </body>
    <referenceContainer name="after.body.start">
        <block class="Payolution\Payments\Block\Translate" name="payolution_translate" template="Payolution_Payments::translate.phtml"/>
    </referenceContainer>
</page>
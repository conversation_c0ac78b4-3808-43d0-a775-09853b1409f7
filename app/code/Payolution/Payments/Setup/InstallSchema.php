<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * <PERSON>
 */

namespace Payolution\Payments\Setup;


use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class InstallSchema implements InstallSchemaInterface
{
    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;
        $installer->startSetup();

        $installer->getConnection()->addColumn(
            $installer->getTable('sales_order'),
            'payolution_unique_id',
            Table::TYPE_TEXT,
            array(),
            ['nullable' => true],
            'Payolution Unique ID'
        );

        $installer->getConnection()->addColumn(
            $installer->getTable('sales_order_grid'),
            'payolution_unique_id',
            Table::TYPE_TEXT,
            array(),
            ['nullable' => true],
            'Payolution Unique ID'
        );

        $setup->endSetup();
    }
}
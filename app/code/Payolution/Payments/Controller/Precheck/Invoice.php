<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Controller\Precheck;

use Magento\Framework\Controller\ResultFactory;
use Payolution\Payments\Helper\Data;
use Payolution\Payments\Helper\PreCheck as PrecheckHelper;

/**
 * Class Invoice
 * PreCheck Action for Invoice
 * @package Payolution\Payments\Controller\Precheck
 */
class Invoice extends \Payolution\Payments\Controller\Action
{
    /**
     * @var string
     */
    protected $_preCheckApiType = 'Payolution\Payments\Model\Api\PreCheck\Invoice';

    /**
     * Do invoice precheck
     *
     * @return \Magento\Framework\Controller\Result\Json
     */
    public function execute()
    {
        try {
            $precheckFromSession = $this->_getPrecheckResultFromSession(PrecheckHelper::PRECHECK_INVOICE);
            if (false !== $precheckFromSession) {
                $responseContent = [
                    'Status' => self::PRECHECK_SUCCESS,
                ];
            } else {
                /** @var \Payolution\Payments\Model\Api\PreCheck\Invoice $preCheckApi */
                $preCheckApi = $this->_apiFactory->create(
                    $this->_preCheckApiType,
                    [
                        'formData' => [
                            'billing'   => $this->getRequest()->getParam('billing'),
                            'dob'       => (string)$this->getRequest()->getParam('dob'),
                            'email'     => (string)$this->getRequest()->getParam('email'),
                            'telephone' => (string)$this->getRequest()->getParam('telephone'),
                            'ssn'       => (string)$this->getRequest()->getParam('ssn'),
                            'company'   => (string)$this->getRequest()->getParam('company'),
                            'vat_id'    => (string)$this->getRequest()->getParam('vat_id'),
                            'reg_number' => (string)$this->getRequest()->getParam('reg_number'),
                            'organization' => (string)$this->getRequest()->getParam('organization'),
                            'company_name' => (string)$this->getRequest()->getParam('company_name'),
                            'company_owner_given' => (string)$this->getRequest()->getParam('company_owner_given'),
                            'company_owner_family' => (string)$this->getRequest()->getParam('company_owner_family')
                        ],
                        'customer' => $this->_customerSession->getCustomer(),
                        'quote'    => $this->_getQuote(),
                    ]
                );
                $preCheckApi->setFraudProtectionSessionId($this->_getFraudProtectionSessionId())
                    ->run();

                $responseContent = [
                    'Status' => $preCheckApi->isSuccess()
                        ? self::PRECHECK_SUCCESS
                        : self::PRECHECK_ERROR,
                ];

                $this->_savePrecheckResultInSession(
                    PrecheckHelper::PRECHECK_INVOICE,
                    [
                        'unique_id'     => $preCheckApi->getResponseUniqueId(),
                        'amount'        => $preCheckApi->getPrecheckAmount(),
                    ]
                );
            }
        } catch (\InvalidArgumentException $e) {
            $this->_sendPayolutionFailedEmail($e->getMessage(), Data::INVOICE_PAYMENT_CODE);
            $responseContent = [
                'success' => false,
                'error_message' => __('Invalid parameters for processing payolution request'),
            ];
        } catch (\Exception $e) {
            $this->_sendPayolutionFailedEmail($e->getMessage(), Data::INVOICE_PAYMENT_CODE);
            $responseContent = [
                'success' => false,
                'error_message' => __('Unable to process payolution request. Check request data!'),
            ];
            $this->_logger->critical($e);
        }

        /** @var \Magento\Framework\Controller\Result\Json $resultJson */
        $resultJson = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $resultJson->setData($responseContent);
        return $resultJson;
    }
}

<?php

/**
 * Copyright (c) 2008-2016 dotSource GmbH
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * <PERSON> J<PERSON>ck
 */

namespace Payolution\Payments\Test\Integration\Model;

use Payolution\Payments\Test\Integration\Model\PayolutionInstalmentTest;

/**
 * Class PayolutionInstalmentReversalTest
 * @package Payolution\Payments\Test\Integration\Model
 */
class PayolutionInstalmentReversalTest extends PayolutionInstalmentTest
{
    /**
     * @var string
     */
    protected $_cancelApiType   = 'Payolution\Payments\Model\Api\Cancel';

    /**
     * Test perform one Order without Capture and cancel the Payment PreAuth
     */
    public function testReversal()
    {
        $this->performPreAuth();
        foreach ($this->_preAuthIds[$this->_methodName] as $preAuthId) {
            $this->_preAuthOrder->setPayolutionUniqueId($preAuthId);
            /** @var \Payolution\Payments\Model\Api\Cancel $cancelApi */
            $cancelApi = $this->_apiFactory->create(
                $this->_cancelApiType,
                [
                    'order' => $this->_preAuthOrder,
                ]
            )->setPwdLogin(self::TEST_PWD)->run();

            $this->assertNotEmpty($cancelApi->isSuccess(), 'Instalment reversal request did not return "ACK".');
        }
    }
}

<?php
/**
 * @package Magedelight_Cgr for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Cgr\Ui\DataProvider\Restrict;

use Magedelight\Cgr\Model\ResourceModel\Restrict\Grid\CollectionFactory as GridCollectionFactory;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Magento\Ui\DataProvider\AddFieldToCollectionInterface;
use Magento\Ui\DataProvider\AddFilterToCollectionInterface;

class DataProvider extends AbstractDataProvider
{
    /**
     * Product collection
     *
     * @var Collection
     */
    public $objectManager;

    /**
     * @var AbstractCollection
     */
    public $collection;

    /**
     * @var AddFieldToCollectionInterface[]
     */
    public $addFieldStrategies;

    /**
     * @var AddFilterToCollectionInterface[]
     */
    public $addFilterStrategies;

    /**
     * DataProvider constructor.
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param GridCollectionFactory $collectionFactory
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        GridCollectionFactory $collectionFactory,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->collection = $collectionFactory->create();
    }

    /**
     * Get Data
     *
     * @return array
     */
    public function getData()
    {
        if (!$this->getCollection()->isLoaded()) {
            $this->getCollection()->load();
        }

        $items = $this->getCollection()->getData();

        return [
            'totalRecords' => $this->getCollection()->getSize(),
            'items' => array_values($items),

        ];
    }

    /**
     * Get Search Criteria.
     *
     * @return AbstractCollection|null
     */
    public function getSearchCriteria()
    {
        return $this->getCollection();
    }

    /**
     * For filter grid according to category
     *
     * @param \Magento\Framework\Api\Filter $filter
     */
    public function addFilter(\Magento\Framework\Api\Filter $filter)
    {
        if ($filter->getField() == \Magedelight\Cgr\Api\Data\RestrictInterface::STORE_ID) {
            $this->addStoreFilter(['in' => $filter->getValue()]);
        } elseif (isset($this->addFilterStrategies[$filter->getField()])) {
            $this->addFilterStrategies[$filter->getField()]
                ->addFilter(
                    $this->getCollection(),
                    $filter->getField(),
                    [$filter->getConditionType() => $filter->getValue()]
                );
        } else {
            parent::addFilter($filter);
        }
    }

    /**
     * Add Store Filter.
     *
     * @param array $storeIds
     * @return $this
     */
    public function addStoreFilter(array $storeIds)
    {
        $this->collection->getSelect()->join(
            ['stores' => $this->collection->getTable('magedelight_group_restriction_store')],
            'main_table.entity_id = stores.entity_id',
            []
        );

        $this->collection->getSelect()->where('stores.store_id IN (?)', $storeIds);

        return $this;
    }
}

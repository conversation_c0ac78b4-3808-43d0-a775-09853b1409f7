<?php
/**
 * @package Magedelight_Cgr for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Cgr\Plugin\Catalog\Model;

use Magedelight\Cgr\Helper\ProductHelper;
use Zend_Db_Select_Exception;

class Selection
{
    /**
     * @var ProductHelper
     */
    private $productHelper;

    /**
     * Selection constructor.
     * @param ProductHelper $productHelper
     */
    public function __construct(
        ProductHelper $productHelper
    ) {
        $this->productHelper = $productHelper;
    }

    /**
     * After Get Selections Collection.
     *
     * @param mixed $subject
     * @param mixed $collection
     * @return mixed
     * @throws Zend_Db_Select_Exception
     */
    public function afterGetSelectionsCollection($subject, $collection)
    {
        if ($this->productHelper->isGroupRestricted(ProductHelper::FORBID_SCOPE_PRODUCT)) {
            $this->productHelper->filterGroupProducts($collection->getSelect());
        }
        return $collection;
    }
}

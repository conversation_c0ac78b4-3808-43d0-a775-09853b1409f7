<?php
/**
 * @package Magedelight_Cgr for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Cgr\Plugin\Catalog\Model\Category;

use Magedelight\Cgr\Helper\ProductHelper;

class ElasticLayerPlugin
{
    /**
     * @var ProductHelper
     */
    private $productHelper;

    /**
     * ElasticLayerPlugin constructor.
     * @param ProductHelper $productHelper
     */
    public function __construct(
        ProductHelper $productHelper
    ) {
        $this->productHelper = $productHelper;
    }

    /**
     * Before Query.
     *
     * @param mixed $subject
     * @param mixed $query
     * @return array
     */
    public function beforeQuery($subject, $query)
    {

        if ($this->productHelper->isGroupRestricted(ProductHelper::FORBID_SCOPE_PRODUCT)) {
            $products = array_values($this->productHelper->filterProducts());
            if (!$products || count($products) < 1) {
                return [$query];
            }
            // Exclude the entity_id filter from the Elastic collection
            $query['body']['query']['bool']['mustNot'] = ['ids' => [ 'values' => $products]];
        }
        return [$query];
    }
}

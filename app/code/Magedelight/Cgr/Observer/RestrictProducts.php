<?php
/**
 * @package Magedelight_Cgr for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Cgr\Observer;

use Magedelight\Cgr\Logger\Logger;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magedelight\Cgr\Helper\ProductHelper;

class RestrictProducts implements ObserverInterface
{
    /**
     * @var ProductHelper
     */
    private $productHelper;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * RestrictProducts constructor.
     * @param ProductHelper $productHelper
     * @param Logger $logger
     */
    public function __construct(
        ProductHelper $productHelper,
        Logger $logger
    ) {
        $this->productHelper = $productHelper;
        $this->logger = $logger;
    }

    /**
     * Restrict Products.
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            $collection = $observer->getEvent()->getCollection();
            if ($this->productHelper->isGroupRestricted(ProductHelper::FORBID_SCOPE_PRODUCT)) {
                $this->productHelper->filterGroupProducts($collection->getSelect());
            }
        } catch (\Exception $exception) {
            $this->logger->error(__($exception->getMessage()));
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * @package Magedelight_Cgr for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magedelight_Base::root">
                    <resource id="Magedelight_Base::md_modules">
                        <resource id="Magedelight_Cgr::root" title="Magedelight Group Restriction" sortOrder="30">
                            <resource id="Magedelight_Cgr::restrict" title="Manage Group Restriction" sortOrder="10" />
                            <!-- REST API RESTRICTION START -->
                            <resource id="Magedelight_Cgr::restrict_save" title="Save Restrict" sortOrder="20"/>
                            <resource id="Magedelight_Cgr::restrict_delete" title="Delete Restrict" sortOrder="30"/>
                            <resource id="Magedelight_Cgr::restrict_update" title="Update Restrict" sortOrder="40"/>
                            <resource id="Magedelight_Cgr::restrict_view" title="View Restrict" sortOrder="50"/>
                            <!-- REST API RESTRICTION END -->
                        </resource>
                    </resource>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Magedelight_Base::config_root">
                                <resource id="Magedelight_Cgr::config_root"
                                          title="Magedelight Group Restriction Configuration" sortOrder="90" />
                            </resource>
                        </resource>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>

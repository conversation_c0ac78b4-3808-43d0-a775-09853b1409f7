require(['jquery', 'mage/translate'], function($, $t){
    $( document ).ready(function() {

        //wait until the last element (.payment-method) being rendered
        var existCondition = setInterval(function() {
            if ($(".checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] input," +
                ".checkout-index-index .fieldset.address .field[name='billingAddress.fax'] input").length ) {
                clearInterval(existCondition);
                // createAccordion();
                // smoothScrollToInput();
            }
        }, 100);

        function createAccordion(){
            let elements = $(".checkout-index-index .fieldset.address .field[name='shippingAddress.fax']," +
                ".checkout-index-index .fieldset.address .field[name='billingAddress.fax']," +
                ".checkout-index-index .fieldset.address .field[name='shippingAddress.company']," +
                ".checkout-index-index .fieldset.address .field[name='billingAddress.company']");
            elements.find('.label').append(' <span>'+$t('hinzufügen')+'</span>').on('click',function(){
                $(this).siblings('.control').toggle();
            });
        };

        function smoothScrollToInput(){
            var isScrolling = false;
            $(".checkout-index-index input").on('focusin', function () {
                if (!isScrolling) {
                    isScrolling = true;
                    $('html, body').animate({
                        scrollTop: $(this).offset().top - 50
                    }, 800,function(){isScrolling = false;});
                }
            });
        }

    });
});
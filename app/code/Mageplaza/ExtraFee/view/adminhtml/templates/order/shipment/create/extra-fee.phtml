<?php
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_ExtraFee
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */

/** @var \Mageplaza\ExtraFee\Block\Sales\Order\View\ExtraFee $block */
$paymentExtraFee  = $block->getExtraFeeInfo('1');
$shipmentExtraFee = $block->getExtraFeeInfo('2');
$extraFee         = $block->getExtraFeeInfo('3');
?>
<?php if (count($paymentExtraFee)) : ?>
<div class="admin__page-section-item order-payment-extra-fee" id="mp_extra_fee_payment" style="margin-top: 20px">
    <div class="admin__page-section-item-title">
        <span class="title"><?= $block->escapeHtml(__('Extra Fee Information')) ?></span>
    </div>
    <div class="admin__page-section-item-content">
        <?php foreach ($paymentExtraFee as $fee) : ?>
            <div class="order-payment-method-title">
                <?= /* @noEscape */ $block->getFeeTitle($fee) ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<?php if (count($shipmentExtraFee)) : ?>
<div id="mp_extra_fee_shipment" style="margin-top: 20px">
    <div class="admin__page-section-item-title">
        <span class="title"><?= $block->escapeHtml(__('Extra Fee Information')) ?></span>
    </div>
    <div class="admin__page-section-item-content">
        <?php foreach ($shipmentExtraFee as $fee) : ?>
            <div class="order-payment-method-title">
                <?= /* @noEscape */ $block->getFeeTitle($fee) ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<?php if (count($extraFee)) : ?>
<section class="admin__page-section order-view-extra-fee" id="mp_extra_fee">
    <div class="admin__page-section-title">
        <span class="title"><?= $block->escapeHtml(__('Extra Fee')) ?></span>
    </div>
    <div class="admin__page-section-content">
        <div class="admin__page-section-item">
            <div class="admin__page-section-item-content">
                <?php foreach ($extraFee as $fee) : ?>
                    <div class="order-payment-method-title">
                        <?= /* @noEscape */ $block->getFeeTitle($fee) ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<script>
    require([
        'jquery'
    ], function ($) {
        $('#ship_items_container').before($('#mp_extra_fee'));
        $('.admin__page-section-item.order-shipping-address').eq(1).append($('#mp_extra_fee_shipment'));
        $('.admin__page-section-item.order-payment-method').append($('#mp_extra_fee_payment'));
    })
</script>

# 1. Rat<PERSON>e

When using the Magento 2 UI Components to create admin grids and forms, I always felt like I was dying a bit inside. From my point of view it's an awful system for a number of reasons that I don't want to go into more details about now.


Alternative store fronts that do not use UI components (PWA Studio, Hyvä Themes) are great for frontend developers, but (un?)fortunately I do mostly backend work.

The UI interfaces I create are mainly for store owners and admins.


I desire a way to do my job (which includes building grids and forms) that doesn't feel like I have to fight the framework. Instead, I want to feel empowered and get work done quickly and efficiently.


After years of bitching about Magento, I was very impressed by the work <PERSON> did with the Hyvä frontend theme. He inspired me to stop complaining and also take matters into my own hands, and finally build the tools I desire. Hence, Hyva_Admin.
<?php
/** @var \Magento\Framework\Escaper $escaper */
/** @var Hyva\Admin\ViewModel\HyvaGridInterface $grid */
/** @var Hyva\Admin\ViewModel\HyvaGrid\GridExportInterface[] $exports */

if( $exports ) :
?>
<div class="exports inline-flex mx-2">
    <button @click.prevent="openExport = !openExport"
            class="inline-flex items-center px-2 py-2 font-semibold text-gray-500 bg-white
                                    rounded-lg hover:text-blue-500 focus:outline-none focus:shadow-outline md:px-4"
    >
        <svg xmlns="http://www.w3.org/2000/svg"
             class="w-6 h-6 md:hidden md:visible"
             viewBox="0 0 24 24"
             stroke-width="2"
             stroke="currentColor"
             fill="none"
             stroke-linecap="round"
             stroke-linejoin="round"
        >
            <rect x="0" y="0" width="24" height="24" stroke="none"></rect>
            <path
                    d="M5.5 5h13a1 1 0 0 1 0.5 1.5L14 12L14 19L10 16L10 12L5 6.5a1 1 0 0 1 0.5 -1.5"/>
        </svg>
        <span class="hidden md:block md:visible">
                                <?= $escaper->escapeHtml(__('Export')) ?>
                            </span>
        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-1" width="24" height="24"
             viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
             stroke-linecap="round" stroke-linejoin="round">
            <rect x="0" y="0" width="24" height="24" stroke="none"></rect>
            <polyline points="6 9 12 15 18 9"/>
        </svg>
    </button>

    <div x-show="openExport"
         @click.away="openExport = false"
         class="absolute top-0 right-0 z-40 block p-4 mt-16 -mr-1 overflow-y-auto bg-white
                                rounded-lg shadow-lg"
         style="max-height: 75vh;">
        <?php foreach($exports as $export) : ?>
            <a href="<?= $escaper->escapeHtml($grid->getNavigation()->buildUrl('hyva_admin/export/download', ['_current' => true, 'gridName' => $grid->getGridName(), 'exportType' => $export->getId()])) ?>" class="btn btn-primary inline-flex mx-2 cursor-pointer">
                <span><?= $escaper->escapeHtml($export->getLabel()) ?></span>
            </a>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

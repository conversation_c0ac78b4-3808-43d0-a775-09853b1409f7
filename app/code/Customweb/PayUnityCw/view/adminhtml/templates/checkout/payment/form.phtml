<?php
/**
 * You are allowed to use this API in your web application.
 *
 * Copyright (C) 2018 by customweb GmbH
 *
 * This program is licenced under the customweb software licence. With the
 * purchase or the installation of the software in your application you
 * accept the licence agreement. The allowed usage is outlined in the
 * customweb software licence which can be found under
 * http://www.sellxed.com/en/software-license-agreement
 *
 * Any modification or distribution is strictly forbidden. The license
 * grants you the installation in one application. For multiuse you will need
 * to purchase further licences at http://www.sellxed.com/shop.
 *
 * See the customweb software licence agreement for more details.
 *
 *
 * @category	Customweb
 * @package		Customweb_PayUnityCw
 * 
 */

/* @var $block \Customweb\PayUnityCw\Block\Adminhtml\Checkout\Payment\Form */
?>

<div id="payunitycw-payment-form">
	<?php echo $block->getFormRenderer()->renderForm($block->getForm()) ?>
</div>

<script type="text/javascript">
require(['jquery', 'Magento_Ui/js/modal/confirm', 'Customweb_PayUnityCw/js/form', 'Customweb_PayUnityCw/js/alias'], function($, confirm, Form, Alias){
	$('#payunitycw-payment-submit-button').click(function(){
		var formId = $('form[name="payment_form"]').attr('id');
		window[formId+"-validation"] = true;
		$('.payunitycw_form').submit();
	});

	function getForm(url) {
        return $('<form>', {
            action: url,
            method: 'POST'
        }).append($('<input>', {
            name: 'form_key',
            value: window.FORM_KEY,
            type: 'hidden'
        }));
    }

	$('#payunitycw-payment-cancel-button').click(function(){
		confirm({
			content: '<?php echo __('Are you sure you want to cancel this order?') ?>',
			actions: {
				confirm: function(){
					getForm('<?php echo $block->getCancelUrl() ?>').appendTo('body').submit();
				}
			}
		});
		return false;
	});

	var alias = new Alias('#payunitycw-payment-form', {
		transactionId: '<?php echo $block->getTransaction()->getId() ?>'
	}, function(response){
		$('#payunitycw-payment-form').html(response.html);
	}, '<?php echo $block->getUrl('payunitycw/checkout/updateAlias') ?>');
	alias.attachListeners();
});
</script>
.payunitycw-payment-information-block {
	margin: 0 0 3rem;
}

.payunitycw-transaction-payment-information-block {
	margin: 3rem 0;
	background-color: #f1f1f1;
	padding: 1rem;
}

.payunitycw-transaction-information .tooltip,
.payunitycw-payment-information-block .tooltip {
	float: right;
}

@media (min-width: 768px) {
	.payunitycw-transaction-information,
	.payunitycw-transaction-captures {
		float: left;
		width: calc( (100%) * .5 - 30px );
	}
	
	.payunitycw-transaction-history,
	.payunitycw-transaction-refunds {
		float: right;
		width: calc( (100%) * .5 - 30px );
	}
}

.payunitycw_form .admin__field .note,
.payunitycw_form .admin__field div.mage-error {
	clear: both;
	padding: 5px 0;
	margin-left: calc( (100%) * .33333333 + 30px );	
}
.payunitycw_form .admin__field .note {
	font-size: 12px;
	color: #303030;
}
.payunitycw_form .admin__field div.mage-error,
.payunitycw_form td.value div.mage-error {
	font-size: 12px;
	color: #e02b27;
}

.payunitycw_form .html-field {
	line-height: 3.2rem;
}

.payunitycw_form .multi-control:after {
	content: "";
	display: table;
	clear: both;
}
.payunitycw_form .multi-control > div > div {
	width: auto;
	float: left;
	margin-right: 5px;
	margin-top: 0;
}


/*** CHECKOUT ***/

.payunitycw_form .card-number .multi-control > div > div {
	width: 100%;
	float: none;
	margin: 0;
}

.payunitycw_form .card-brand-image-color.brand-is-deselected {
	display:none;
}

.payunitycw_form .card-brand-image-grey.brand-is-selected {
	display:none;
}

.payunitycw_form .card-brand-image-selection {
	margin-top: 5px;
}

.payunitycw_form .card-brand-image-selection img {
	width:auto;
	height: 20px;
	margin-right: 3px;
	vertical-align: middle;
}

.payunitycw_form .invalid-card-number,
.payunitycw_form .invalid-cvc-number {
	border-color: #ed8380;
	background-image: url(../images/invalid.png) !important;
	background-position: right 5px center;
	background-repeat: no-repeat !important;
}

.payunitycw_form .valid-card-number,
.payunitycw_form .valid-cvc-number {
	background-image: url(../images/valid.png) !important;
	background-position: right 5px center;
	background-repeat: no-repeat !important;
}

.payunitycw_form .alias select {
	width: 100%;
}

.payunitycw_form .alias select option:empty {
	display: block;
}
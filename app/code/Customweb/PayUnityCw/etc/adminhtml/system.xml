<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<section id="payunitycw" type="text" sortOrder="800" showInDefault="1" showInWebsite="1" showInStore="1">
			<label>PayUnityCw</label>
			<tab>sales</tab>
			<resource>Customweb_PayUnityCw::config_payunitycw</resource>
			<group id="information" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="0" translate="label">
				<label><![CDATA[Information]]></label>
				<field id="version" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="0" translate="label" type="label">
					<label><![CDATA[Version]]></label>
				</field>
				<field id="more" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" type="label">
					<frontend_model>Customweb\PayUnityCw\Block\Backend\System\Config\Information\More</frontend_model>
				</field>
			</group>
			<group id="shop" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
				<label><![CDATA[Shop]]></label>
				<field id="alias_management" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Customer Alias Management]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[You can allow the customers to manage/delete their aliases in the their account.]]></comment>
				</field>
				<field id="await_notification" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="10" translate="label" type="select">
					<label><![CDATA[Await Notification]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[In some cases it may take a few seconds until the transaction is successfully authorized. When this option is enabled, the notification is awaited.]]></comment>
				</field>
				
			</group>
			<group id="general" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label">
				<label><![CDATA[General]]></label>
				<field id="operation_mode" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="0" translate="label comment" type="select">
					<label><![CDATA[Operation Mode]]></label>
					<comment><![CDATA[You can switch between the different environments, by
							selecting the corresponding operation mode.
						]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\OperationMode</source_model>
				</field>
				<field id="test_mode" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label comment" type="select">
					<label><![CDATA[Test Mode]]></label>
					<comment><![CDATA[You can switch between the different test modes.
						]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\TestMode</source_model>
				</field>
				<field id="access_token" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label comment" type="text">
					<label><![CDATA[Authorization Bearer Token (Access Token)]]></label>
					<comment><![CDATA[The Authorization Bearer Token is provided by
							PayUnity. Access token can be taken from
							the backend UI under Administration > Account data > Merchant /
							Channel Info only if you have specific administration rights.
						]]></comment>
				</field>
				<field id="user_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label comment" type="text">
					<label><![CDATA[User ID (Deprecated)]]></label>
					<comment><![CDATA[The user ID is provided by
							PayUnity. This authentication method is
							deprecated, please use the 'Authorization Bearer Token' instead.
						]]></comment>
				</field>
				<field id="user_password" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label comment" type="text">
					<label><![CDATA[User Password (Deprecated)]]></label>
					<comment><![CDATA[The password is provided by
							PayUnity. This authentication method is
							deprecated, please use the 'Authorization Bearer Token' instead.
						]]></comment>
				</field>
				<field id="transaction_id_schema" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label comment" type="text">
					<label><![CDATA[Merchant Transaction ID Schema]]></label>
					<comment><![CDATA[Here you can configure a schema for the
							merchantTransactionId parameter. The tag {id} will be replaced by a
							guaranteed unique id, which may be created using a combination of
							order and transaction ids. The tag {tid} will be replaced by the
							transaction id, and the tag {oid} will be replaced by the order id.
							This field should stay unique, we advise using {id}. The text will
							be cut at 255 characters.
						]]></comment>
				</field>
				<field id="descriptor_schema" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="60" translate="label comment" type="text">
					<label><![CDATA[Descriptor Schema]]></label>
					<comment><![CDATA[Here you can configure a schema for the
							descriptor
							parameter. The tag {id} will be replaced by a
							guaranteed unique id,
							which may be created using a combination of
							order and transaction
							ids. The tag {tid} will be replaced by the
							transaction id, and the
							tag {oid} will be replaced by the order id.
							The text will be cut at
							127 characters.
						]]></comment>
				</field>
				<field id="global_entity_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[You receive an Entity ID when you create your
							PayUnity account. The entity
							ID specified
							here will be taken, when no specific Entity ID is defined
							in the
							payment method settings.
						]]></comment>
				</field>
				<field id="custom_parameters" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80" translate="label comment" type="textarea">
					<label><![CDATA[Custom Parameters]]></label>
					<comment><![CDATA[You can define custom parameters that are sent to the
							PSP with every request. Syntax: "customParameters[RISK_MID]=123123",
							one parameter per line.
						]]></comment>
				</field>
				<field id="webhook_secret_key" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label comment" type="text">
					<label><![CDATA[Webhook Secret Key]]></label>
					<comment><![CDATA[You can specify the secret key during the setup of the
							webhook.
						]]></comment>
				</field>
			</group>
			<group id="logging" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label">
				<label><![CDATA[Logging]]></label>
				<field id="level" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="10" translate="label" type="select">
					<label><![CDATA[Level]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\LoggingLevel</source_model>
					<comment><![CDATA[Messages of this or a higher level will be logged.]]></comment>
				</field>
			</group>
		</section>
		<section id="payment">
			<group id="payunitycw_generic" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
				<label>PayUnity Generic</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Generic\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Generic\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="payment_methods" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Payment Methods]]></label>
					<comment><![CDATA[Define the payment methods, the customer can choose
									from. List them separated by a space.
								]]></comment>
				</field>
				<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Generic\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Generic\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="220" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_americanexpress" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label">
				<label>PayUnity American Express</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Americanexpress\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Americanexpress\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Americanexpress\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Americanexpress\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_cartebleue" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label">
				<label>PayUnity Carte Bleue</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Cartebleue\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Cartebleue\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Cartebleue\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Cartebleue\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_dankort" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label">
				<label>PayUnity Dankort</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Dankort\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Dankort\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Dankort\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Dankort\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_diners" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label">
				<label>PayUnity Diners Club</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Diners\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Diners\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Diners\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Diners\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_directdebitssepa" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="60" translate="label">
				<label>PayUnity Sepa Direct Debits</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Directdebitssepa\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Directdebitssepa\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="debtor_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Deptor ID]]></label>
					<comment><![CDATA[The debtor ID for processing SEPA mandates.
								]]></comment>
				</field>
				<field id="merchant_name" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="text">
					<label><![CDATA[Merchant Name]]></label>
					<comment><![CDATA[The merchant name, displayed to customer on SEPA
									mandates.
								]]></comment>
				</field>
				<field id="display_customer_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="select">
					<label><![CDATA[Display Customer ID]]></label>
					<comment><![CDATA[If a customer id is provided in the context, and this
									feature is activated, the customer id will be displayed in the
									SEPA mandate text.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\DisplayCustomerId</source_model>
				</field>
				<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Directdebitssepa\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="220" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="230" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Directdebitssepa\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="240" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_discovercard" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70" translate="label">
				<label>PayUnity Discover Card</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Discovercard\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Discovercard\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Discovercard\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Discovercard\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_eps" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80" translate="label">
				<label>PayUnity EPS</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Eps\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Eps\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Eps\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_giropay" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label">
				<label>PayUnity giropay</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Giropay\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Giropay\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Giropay\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_ideal" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
				<label>PayUnity iDEAL</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Ideal\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Ideal\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Ideal\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_jcb" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="110" translate="label">
				<label>PayUnity JCB</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Jcb\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Jcb\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Jcb\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Jcb\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_klarnainvoice" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="120" translate="label">
				<label>PayUnity Klarna Invoice</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Klarnainvoice\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Klarnainvoice\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="klarna_merchant_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Klarna: Merchant ID]]></label>
					<comment><![CDATA[Please specify the merchant id (EID) of your Klarna
									account.
								]]></comment>
				</field>
				<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Klarnainvoice\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_maestro" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="130" translate="label">
				<label>PayUnity Maestro</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Maestro\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Maestro\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Maestro\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Maestro\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_mastercard" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="140" translate="label">
				<label>PayUnity MasterCard</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Mastercard\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Mastercard\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Mastercard\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Mastercard\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_openinvoice" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label">
				<label>PayUnity Open Invoice</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Openinvoice\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Openinvoice\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="processor" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="select">
					<label><![CDATA[Processor]]></label>
					<comment><![CDATA[Please select the processor by which the invoice
									should be processed.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Processor</source_model>
				</field>
				<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Openinvoice\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_payolutionelv" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label">
				<label>PayUnity Payolution ELV</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Payolutionelv\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Payolutionelv\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Payolutionelv\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_payolutionins" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label">
				<label>PayUnity Payolution Installment</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Payolutionins\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Payolutionins\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Payolutionins\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_paybox" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label">
				<label>PayUnity Paybox Mobile Phone Payment</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paybox\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paybox\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paybox\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paybox\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_paypal" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label">
				<label>PayUnity PayPal</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paypal\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paypal\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paypal\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paypal\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_paysafecard" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label">
				<label>PayUnity paysafecard</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paysafecard\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paysafecard\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paysafecard\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paysafecard\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_prepayment" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label">
				<label>PayUnity Prepayment</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Prepayment\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Prepayment\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Prepayment\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_skrill" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="220" translate="label">
				<label>PayUnity Skrill (Moneybookers)</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Skrill\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Skrill\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Skrill\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Skrill\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_sofortueberweisung" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="230" translate="label">
				<label>PayUnity Sofortüberweisung</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Sofortueberweisung\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Sofortueberweisung\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="country_restriction" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="multiselect">
					<label><![CDATA[Country Restriction]]></label>
					<comment><![CDATA[You can restrict the available countries. If no
									country is selected all available will be shown.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\CountryRestriction</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Sofortueberweisung\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_visa" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="240" translate="label">
				<label>PayUnity Visa</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Visa\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Visa\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Visa\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Visa\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_vpay" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="250" translate="label">
				<label>PayUnity V PAY</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Vpay\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Vpay\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Vpay\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Vpay\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_paydirekt" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="260" translate="label">
				<label>PayUnity paydirekt</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paydirekt\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paydirekt\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paydirekt\Capturing</source_model>
				</field>
				<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Paydirekt\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_debitmastercard" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="270" translate="label">
				<label>PayUnity Debit MasterCard</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitmastercard\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitmastercard\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitmastercard\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitmastercard\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_debitvisa" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="280" translate="label">
				<label>PayUnity Debit Visa</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitvisa\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitvisa\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitvisa\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Debitvisa\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group><group id="payunitycw_chinaunionpay" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="290" translate="label">
				<label>PayUnity China Unionpay</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Chinaunionpay\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Chinaunionpay\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Chinaunionpay\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Chinaunionpay\Authorizationmethod</source_model>
				</field>
			</group><group id="payunitycw_creditcard" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="300" translate="label">
				<label>PayUnity Credit Card</label>
				<field id="model"/>
				<field id="active" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="0" translate="label" type="select">
					<label><![CDATA[Enabled]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="text">
					<label><![CDATA[Title]]></label>
				</field>
				<field id="description" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="textarea">
					<label><![CDATA[Description]]></label>
				</field>
				<field id="show_image" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="30" translate="label" type="select">
					<label><![CDATA[Show Image]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_payment_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40" translate="label" type="select">
					<label><![CDATA[Show Payment Id]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Show the payment id in the order information]]></comment> 
				</field>
				<field id="currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="50" translate="label" type="multiselect">
					<label><![CDATA[Allowed Currencies]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Creditcard\Currency</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="base_currency" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="60" translate="label" type="select">
					<label><![CDATA[Use Base Currency]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send amounts to PayUnity in store's base currency.]]></comment>
				</field>
				<field id="allowspecific" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="70" translate="label" type="allowspecific">
					<label><![CDATA[Payment from Applicable Countries]]></label>
					<source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
				</field>
				<field id="specificcountry" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="80" translate="label" type="multiselect">
					<label><![CDATA[Payment from Specific Countries]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Creditcard\Country</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="min_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label><![CDATA[Minimum Order Total]]></label>
				</field>
				<field id="max_order_total" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label><![CDATA[Maximum Order Total]]></label>
				</field>
				<field id="sort_order" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="120" translate="label" type="text">
					<label><![CDATA[Sort Order]]></label>
					<frontend_class>validate-number</frontend_class>
				</field>
				<field id="settlement" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="130" translate="label" type="select">
					<label><![CDATA[Invoice Settlement]]></label>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Settlement</source_model>
					<comment><![CDATA[Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.]]></comment>
				</field>
				<field id="invoice_email" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="140" translate="label" type="select">
					<label><![CDATA[Send Invoice Email]]></label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Send the invoice email automatically after invoice creation.]]></comment>
					<depends>
						<field id="settlement">direct</field>
					</depends>
				</field>
			<field id="active_brands" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="150" translate="label comment" type="multiselect">
					<label><![CDATA[Credit Card Brands]]></label>
					<comment><![CDATA[The allowed credit card
									brands can be restricted by
									this setting.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\ActiveBrands</source_model>
					<can_be_empty>1</can_be_empty>
				</field>
				<field id="channel_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="160" translate="label comment" type="text">
					<label><![CDATA[Entity ID]]></label>
					<comment><![CDATA[The Entity ID is a unique key for the identification
									of the unit which sends transactions into the system.
								]]></comment>
				</field>
				<field id="channel_conditions" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="170" translate="label comment" type="textarea">
					<label><![CDATA[Channel Conditions]]></label>
					<comment><![CDATA[Beside the default channel id a set of channel ids
									depending on the order total amount can be defined. Each line must
									contain a
									lower amount, an upper amount and the applicable channel
									id. The format is as follow 'lower amount;upper amount;channel
									id'. The following
									example shows a conrete on:
									'10.00;200.00;**************231353' The upper boundaries are not
									included. If you leave this field empty or if a
									amount is outside
									any range the default channel id is applied. If multiple
									conditions match, the last one is taken.
								]]></comment>
				</field>
				<field id="channel_id_nothreed" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="180" translate="label comment" type="text">
					<label><![CDATA[Entity ID (MoTo)]]></label>
					<comment><![CDATA[If this payment method should support recurring and
									moto transactions, you have to specify a second entity ID without
									3d secure.
								]]></comment>
				</field>
				<field id="capturing" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="190" translate="label comment" type="select">
					<label><![CDATA[Capturing]]></label>
					<comment><![CDATA[Should the amount be captured automatically after the
									order (direct) or should the amount only be reserved (deferred)?
									If instructed to do so you can also choose the risk based
									capturing. But please note that this feature can only be used if
									your account
									has the option activated. Contact your service
									provider for more
									information.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Creditcard\Capturing</source_model>
				</field>
				<field id="widget_style" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="200" translate="label comment" type="select">
					<label><![CDATA[COPYandPAY Style]]></label>
					<comment><![CDATA[Define the style to be used for the COPYandPAY payment
									form.
								]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\WidgetStyle</source_model>
				</field>
				<field id="authorizationMethod" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="210" translate="label comment" type="select">
					<label><![CDATA[Authorization Method]]></label>
					<comment><![CDATA[Select the authorization method to use for processing this payment method.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\Creditcard\Authorizationmethod</source_model>
				</field>
				<field id="alias_manager" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="220" translate="label comment" type="select">
					<label><![CDATA[Alias Manager]]></label>
					<comment><![CDATA[The alias manager allows the customer to select from a credit card previously stored. The sensitive data is stored by PayUnity.]]></comment>
					<source_model>Customweb\PayUnityCw\Model\Config\Source\AliasManager</source_model>
				</field>
			</group>
		</section>
	</system>
</config>
"* Required Fields","* Required Fields"
"-- No Status Change --","-- No Status Change --"
"3D Secure","3D Secure"
"3D Secure Authentication","3D Secure Authentication"
"3D Secure failed","3D Secure verification failed"
"3D Secure sucess","3D Secure successfull"
"A authorized transaction cannot be authorized again.","An authorised transaction cannot be authorised again."
"A authorized transaction cannot be marked as failed.","An authorised transaction cannot be marked as failed."
"A cancelled transaction cannot be authorized.","A cancelled transaction cannot be authorised."
"A cancelled transaction cannot be cancelled again.","A cancelled transaction cannot be cancelled again."
"A cancelled transaction cannot be captured.","A cancelled transaction cannot be captured"
"A cancelled transaction cannot be marked as failed.","A cancelled transaction cannot be marked as failed."
"A cancelled transaction cannot be refunded.","A cancelled transaction cannot be refunded"
"A captured transaction cannot be authorized.","A captured transaction cannot be authorised."
"A captured transaction cannot be cancelled.","A captured transaction cannot be cancelled."
"A captured transaction cannot be marked as failed.","A captured transaction cannot be marked as failed."
"A failed authorization cannot be authorized.","A failed authorisation cannot be authorised."
"A payment method may not available for customers from different counties. You can decide when this availability check should happen. We recommend to set it during authorization","Some payment methods are not be available for customers from certain counties. You can decide at what instant this availability check should happen. We recommend setting it to 'during authorisation'."
"A refund announcement has been initiated.","A refund announcement has been initiated."
"A refund was added over !amount.","A refund was added over !amount."
"A transaction may be uncertain, when the payment is not guaranteed. For example in case the credit card does not participate in the 3D procedure.","A transaction may be uncertain, when the payment is not guaranteed. For example in the case that the credit card does not participate in the 3D Secure procedure."
"Abort","Abort"
"About","About"
"Account Holder","Account Holder"
"Account Holder Name","Account holder"
"Account Holder: !holder","Account Holder: !holder"
"Account Number","Account Number"
"Account Number: !number","Account Number: !number"
"Account owner name.","Account holder"
"Account statement","Account statement"
"Account statement line 1","Account statement line 1"
"Account statement line 2","Account statement line 2"
"Account statement line 3","Account statement line 3"
"Action","Action"
"Actions","Actions"
"Activate","Active"
"Activate address checking","Activate address checking"
"Activate scoring","Activate scoring"
"Activate the payment method and Enter the Channel ID in the respective field for every payment method that you activate.","Activate the payment method and enter the Channel ID in the respective field for every payment method that you activate."
"Activate the payment method that you want to process transactions with.","Activate the payment method that you want to process transactions with."
"Active","Active"
"Active (all criteria + boniscore)","Active (all criteria + boniscore)"
"Active (all criteria)","Active (all criteria)"
"Active (hard criteria)","Active (hard criteria)"
"Active - Fail when corrections are proposed.","Active - Fail when corrections are proposed."
"Active - Ignore proposed corrections.","Active - Ignore proposed corrections."
"Active - Show proposed corrections in backend.","Active - Show proposed corrections in backend."
"Additional Information","Additional Information"
"Additional text which is displayed on the invoice (if using Open Invoice, or with active Module Invoicing).","Additional text which is displayed on the invoice (if using Open Invoice, or with active Module Invoicing)."
"Address Addition","Address Addition"
"Address check executed","Address check executed"
"Address check maximum","Address check maximum"
"Address check minimum","Address check minimum"
"Address check type","Address check type"
"Address check type invalid.","Address check type invalid."
"Addresses must be equal.","Addresses must be equal."
"Adjust Qty","Adjust Quantity"
"Adjustment Discount","Adjustment Discount"
"Adjustment Fee","Adjustment Fee"
"After payment selection","After payment method selection"
"After selecting payment method","After selecting payment method"
"Agreement","Agreement"
"Ajax authorization not supported.","Ajax authorization not supported"
"Alias","Alias"
"Alias For Display","Alias For Display"
"Alias Manager","Alias Manager"
"Alias manager","One-Click Checkout (Alias Manager)"
"All Customers","All Customers"
"Allow scoring","Allow scoring"
"Allowed Currencies","Allowed currencies"
"Allowed currencies for this payment method","Permitted currencies for this payment method"
"Already registered?","Already registered?"
"Always Checkout as Guest","Always Checkout as Guest"
"American Express","American Express"
"Amount","Amount"
"Amount Incl. Tax","Amount Incl. Tax"
"An unexpected error occurred while cancelling the transaction.","An unexpected error occurred while cancelling the transaction."
"An unexpected error occurred while capturing the transaction.","An unexpected error occurred while capturing the transaction."
"An unexpected error occurred while refunding the transaction.","An unexpected error occurred while refunding the transaction."
"An unknown error occured while identifying the cause for failure","An unknown error occured while identifying the cause for failure"
"Approved Payment Status","Approved payment Status"
"Are you sure you want to cancel this order?","Are you sure you want to cancel this order?"
"Austria","Austria"
"Author","Author"
"Authorization Amount","Authorisation Amount"
"Authorization Failed","Authorisation failed"
"Authorization Method","Authorisation Method"
"Authorization is uncertain.","Authorisation is uncertain"
"Authorization method 'Payment Page' does not support alias manager.","Authorization method 'Payment Page' does not support alias manager."
"Authorized Status","Authorised status"
"Await Notification","Await Notification"
"Awaiting confirmation","Awaiting confirmation"
"BIC","BIC"
"BIC: !bic","BIC: !bic"
"Bad signature format.","False signature format."
"Bancontact","Bancontact"
"Bank Code: !bank","Bank Code: !bank"
"Bank Country: !country","Bank Country: !country"
"Bank Location","Bank Location"
"Bank Name","Bank Name"
"Bank account number","Bank account number (IBAN)"
"Bank code number","Bank code number"
"Bank data","Bank data"
"Basic","Basic"
"Before payment selection","Before selection of the payment method"
"Before showing payment method","Before showing payment method"
"Belgium","Belgium"
"Beside the default channel id a set of channel ids depending on the order total amount can be defined. Each line must contain a lower amount, an upper amount and the applicable channel id. The format is as follow 'lower amount;upper amount;channel id'. The following example shows a conrete on: '10.00;200.00;23413113213131231353' The upper boundaries are not included. If you leave this field empty or if a amount is outside any range the default channel id is applied. If multiple conditions match, the last one is taken.","Beside the default channel ID a set of channel IDs depending on the order total amount can be defined. Each line must contain a lower amount, an upper amount and the applicable Channel ID. The format is as follow 'lower amount;upper amount;Channel ID' as shown in the following example: '10.00;200.00;23413113213131231353'. The upper boundaries are not included. If you leave this field empty or if an amount is outside any range the default channel id is applied. If multiple conditions match the last one is taken."
"BillSAFE","BillSAFE"
"BillSAFE (supports Payment Page and Hidden Authorization)","BillSAFE (supports Payment Page and Hidden Authorization)"
"Billing Address","Billing Address"
"Billing Information","Billing information"
"Billing address","Billing address"
"Boni score result","Boni score result"
"Brand","Brand"
"Brand Code","Brand Code"
"Brand Name","Brand Name"
"Buy","Buy"
"By setting the capturing the reservation can be captured directly after the order or later manually over the backend of the store.","With the capture setting, the transaction can be captured directly after the order or later manually over the backend of the store."
"COPYandPAY Style","COPYandPAY Style"
"CVC Code","CVC Code"
"CVC:","CVC:"
"Cache Timeout","Cache Timeout"
"Cancel","Cancel"
"Cancel ID","Cancel ID"
"Cancel pending orders when out of stock","Cancel pending orders when out of stock"
"Cancel status","Cancel status"
"Canceled PayUnityCw","Canceled PayUnityCw"
"Canceling of this payment not possible!","Cancellation of this payment not possible!"
"Cancellation is not supported by this transaction.","Cancellation is not supported by this transaction."
"Cancelled Status","Cancelled status"
"Cannot redirect from a deferred capture","Cannot redirect from a deferred capture"
"Cannot update item quantity.","Cannot update item quantity."
"Capture Amount","Amount to capture"
"Capture ID","Capture ID"
"Capture amount","Capture amount"
"Capture information","Capture information"
"Capture status","Capture status"
"Capture: Don't Close","Capture: Don't close"
"Captured Amount","Captured amount"
"Captured Status","Captured status"
"Captures","Captures"
"Captures & Refunds","Captures & Refunds"
"Capturing","Capturing"
"Card","Card"
"Card Brand","Card Brand"
"Card CVC/2","Card CVC/2"
"Card Expiration","Expiry Date"
"Card Expiry","Card expiry date"
"Card Holder Name","Card holder name"
"Card Number","Card number"
"Card Type","Credit card type"
"Card to use","Card to use"
"Cardnumber:","Cardnumber:"
"Cardtype:","Cardtype:"
"Carrier Title","Carrier Title"
"Carte Bleue","Carte Bleue"
"Channel Conditions","Channel Conditions"
"Channel ID","Channel ID"
"Channel ID (MoTo)","Channel ID (MoTo)"
"Channel ID (No 3D secure)","Channel ID (No 3D secure)"
"Check out faster","Check out faster"
"Check to activate the external checkout.","Check to activate the external checkout."
"Checkout","Checkout"
"Checkout as Guest","Checkout as a guest"
"Checkout as a Guest or Register","Checkout as a guest or register"
"Checkout as a new customer","Checkout as a new customer"
"Checkout using your account","Checkout using your account"
"Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status","Choose settlement after order in case you want an invoice to be created with your order. The status of the invoice will be set according to your specified capture status."
"Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.","Choose settlement after order in case you want an invoice to be created with your order. The status of the invoice will be set according to your specified capture status. For captured payments, an invoice is always created."
"City","City"
"Classes","Classes"
"Color","Color"
"Company Commercial Number","Company Commercial Number"
"Company VAT Number","Company VAT Number"
"Configure the payment portals in the section Configuration > Payment Portals in the backend of PayUnity according to the manual: <a href='!url'>!url</a>.","Configure the payment portals in the section Configuration > Payment Portals in the backend of PayUnity according to the manual: <a href='!url'>!url</a>."
"Confirm Mandate","Confirm Mandate"
"Confirm Password","Confirm Password"
"Connector Test Mode","Connector Test Mode"
"Connector Transaction ID","Connector Transaction ID"
"Consumer score type invalid.","Consumer score type invalid."
"Continue","Continue"
"Copy the Portal ID, Secret Key, Merchant ID and Sub Account ID from the Back office too the main module. You find this information under Configuration > Payment Portals in the tab API Parameter.","Copy the Portal ID, Secret Key, Merchant ID and Sub Account ID from the Back office too the main module. You find this information under Configuration > Payment Portals in the tab API Parameter."
"Copy the shown encryption key into the configuration.","Copy the shown encryption key into the configuration."
"Could not add proposed address changes.","Could not add proposed address changes."
"Could not convert object of type '!type' to string.","Could not convert object of type '!type' to string."
"Could not execute scheduled update.","Could not execute scheduled update."
"Could not find a brand for the given card number.","Could not find a brand for the given card number."
"Could not find an adapter for the authoriztion method !methodName.","Could not find an adapter for the authoriztion method !methodName."
"Could not find pressed button.","Could not find pressed button."
"Could not find template file at default location '!location'.","Could not find template file at default location '!location'."
"Could not find the payment method !paymentMethodName.","Could not find the payment method !paymentMethodName."
"Could not load layout from URL !url. Reason: !reason","Could not load layout from URL !url. Reason: !reason"
"Country","Country"
"Country Restriction","Country Restriction"
"Create Account","Create Account"
"Create New Order - Payment","Create New Order - Payment"
"Create a recurring payment, with an existing alias is not supported","Creating a recurring payment with an existing alias is not supported."
"Creating an account has many benefits:","Creating an account has many benefits:"
"Creation Date","Creation Date"
"Credit Card Brands","Credit Card Brands"
"Credit Card Number","Credit Card Number"
"Credit Memo History","Credit Memo History"
"Credit Memo Totals","Credit Memo Totals"
"Credit memo's total must be positive.","Credit memo's total must be positive."
"Creditor identifier: !CREDITOR_ID<br/> Mandate reference!CUSTOMER_ID: !MANDATE_REFERENCE<br/> Date: !DATE<br/> Customer: !CUSTOMER<br/> <br/> By signing this mandate form, you authorise (A) !MERCHANT_NAME to send instructions to your bank to debit your account and (B) your bank to debit your account in accordance with the instruction from !MERCHANT_NAME. <br/> As part of your rights, you are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited. Your rights are explained in a statement that you can obtain from your bank.","Creditor identifier: !CREDITOR_ID<br/> Mandate reference!CUSTOMER_ID: !MANDATE_REFERENCE<br/> Date: !DATE<br/> Customer: !CUSTOMER<br/> <br/> By signing this mandate form, you authorise (A) !MERCHANT_NAME to send instructions to your bank to debit your account and (B) your bank to debit your account in accordance with the instruction from !MERCHANT_NAME. <br/> As part of your rights, you are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited. Your rights are explained in a statement that you can obtain from your bank."
"Creditworthiness was denied.","Creditworthiness was denied."
"Currency","Currency"
"Current Configuration Scope:","Current Configuration Scope:"
"Custom CSS Url","Custom CSS URL"
"Custom Cancel Button","Custom Cancel Button"
"Custom JS Url","Custom JS URL"
"Custom Parameters","Custom Parameters"
"Custom Pay Button","Custom Pay Button"
"Customer Account","Customer account"
"Customer Alias Management","Customer Alias Management"
"Customer Date of Birth","Customer Date of Birth"
"Customer Gender","Customer Gender"
"Customer Phone Number","Customer Phone Number"
"Customer successfully returned from PayUnityCw","Customer successfully returned from PayUnityCw"
"Customer successfully returned from PayUnity","Customer successfully returned from the PayUnityCw payment page."
"Customer sucessfully returned from the PayUnityCw payment page.","Customer successfully returned from the PayUnityCw payment page."
"Customers","Customers"
"Customweb","Customweb"
"Czech Republic","Czech Republic"
"Dankort","Dankort"
"Date","Date"
"Date of Birth","Date of birth"
"Date of birth must be provided.","Date of birth must be provided."
"Day","Day"
"Deactivate","Deactivate"
"Dear customer we have received your billing and shipping information. In order to finish your order please create an account below or checkout as guest.","Dear customer we have received your billing and shipping information. In order to finalize your order please create an account below or checkout as a guest."
"Dear customer, it appears to us that your payment was successful, but we are still waiting for confirmation. You can wait on this page and we will redirect you after we received the confirmation. Or you can close this window and we will send out an order confirmation email.","Dear customer, it appears to us that your payment was successful, but we are still waiting for confirmation. You can wait on this page and we will redirect you after we received the confirmation. Or you can close this window and we will send out an order confirmation email."
"Debtor ID","Debtor ID"
"Decline Message","Decline Message"
"Declined by test data.","Declined by test data."
"Default Config","Default Configuration"
"Deferred","Deferred"
"Deferred (authorization)","Deferred (authorisation)"
"Deferred capturing","Deferred capturing"
"Deferred settlement","Deferred settlement"
"Define a maximal order total for this checkout to be available.","Define a maximal order total for this checkout to be available."
"Define a minimal order total for this checkout to be available.","Define a minimal order total for this checkout to be available."
"Define the height of the iframe element (only has an effect if iframe authorization is selected).","Define the height of the iFrame element (only effective if iFrame authorization is selected)."
"Define the payment methods, the customer can choose from. List them separated by a space.","Define the payment methods the customer can choose from. List them separated by a space."
"Define the style to be used for the COPYandPAY payment form.","Define the style to be used for the COPYandPAY payment form."
"Delayed","Deferred"
"Delete","Delete"
"Delivery address","Delivery address"
"Denied Payment Status","Denied Payment Status"
"Deny scoring","Deny scoring"
"Deptor ID","Debtor ID"
"Description","Description"
"Diners Club","Diners Club"
"Direct (Payment Page / Hidden Authorization)","Direct (Payment Page / Hidden Authorization)"
"Direct Debits","Direct Debits (ELV)"
"Direct E-Banking","Direct E-Banking"
"Direct capture after order","Direct capturing"
"Directly after order","Direct capturing"
"Directly after order (sale)","Direct capturing"
"Disabled","Deactivated"
"Discount","Discount"
"Discount Amount","Discount Amount"
"Discover","Discover"
"Display","Display"
"Display Conditions","Display Conditions"
"Display Customer ID","Display Customer ID"
"Do not display","Do not display"
"Do not modify styling","Do not modify styling"
"Do not send basket","Do not send basket"
"Don't change order status","Don't change order status"
"Due Date","Due Date"
"During authorization","During the authorisation"
"During the authorization","During the authorization"
"Dynamic styling","Dynamic styling"
"Edit","Edit"
"Edit Invoice %s","Edit Invoice %s"
"Email Address","Email Address"
"Empty version number field name","Empty version number field name"
"Enable or disable logging.","Enable or disable logging."
"Enable the payment method","Enable this payment method"
"Enabled","Enabled"
"Enter the Security Sender, UserID and Password that is provided to you by PayUnity.","Enter the Security Sender, User ID and Password  provided to you by PayUnity."
"Enter the date on which your card expires.","Enter the card expiry date."
"Entity ID","Entity ID"
"Entity ID (MoTo)","Entity ID (MoTo)"
"Error with the length of the validation date.","Error with the length of the validation date."
"Excl. Tax","Excl. Tax"
"Expiry Date:","Expiry Date:"
"Expiry Month","Expiry Month"
"Expiry Year","Expiry Year"
"External","External (Payment Requests will be sent to the acquiring systems)"
"External Checkout","External Checkout"
"External Checkout: Guest Checkout","External Checkout: Guest Checkout"
"Failed","Failed"
"Failed payment authorization.","The authorization failed."
"FemaIframe Heightle","FemaIframe Heightle"
"Female","Female"
"Figure !number:","Figure !number:"
"First name","First name"
"For recurring and moto transactions to work, you have to specify a separate channel.","For recurring and moto transactions to work, you have to specify a separate channel."
"Forgot Your Password?","Forgot Your Password?"
"Forgot your password?","Forgot your password?"
"Frontend API does not currently support transactions from country @country.","Frontend API does not currently support transactions from country @country."
"Frontend Version (IFrame)","Frontend Version (IFrame)"
"Frontend Version (Payment Page)","Frontend Version (Payment Page)"
"Gender","Gender"
"Gender is invalid.","Gender is invalid."
"Gender is missing.","Gender is missing."
"Germany","Germany (DE)"
"Get Update","Get Update"
"Gl??ubiger-Identifikationsnummer: !CREDITOR_ID<br/> Mandatsreferenz: !MANDATE_REFERENCE<br/> Datum: !DATE<br/> <br/> Ich !CUSTOMER erm??chtige !MERCHANT_NAME widerruflich, lt. meinen vorherigen Eingaben den oben genannten Betrag und die von mir zuk??nftig zu entrichtenden Zahlungen bei F??lligkeit von meinem Konto mittels Lastschrift einzuziehen. Zugleich weise ich mein Kreditinstitut an, die von auf mein Konto gezogenen Lastschriften einzul??sen.<br/> <br/> Hinweis: Ich kann innerhalb von acht Wochen, beginnend mit dem Belastungsdatum, die Erstattung des belasteten Betrages verlangen. Es gelten dabei die mit meinem Kreditinstitut vereinbarten Bedingungen.<br/> Hiermit best??tige ich das SEPA-Lastschriftmandat","Creditor ID: !CREDITOR_ID<br/> Mandate reference: !MANDATE_REFERENCE<br/> Date: !DATE<br/> <br/> I !CUSTOMER, authorise  !MERCHANT_NAME revocably to use the account information provided by me to directly debit my bank account for any future payments to be made. Also, I In addition, I instruct my bank to process the direct debit payments. <br/> <br/> Note: Within eight weeks from the debit date I can demand a refund for the debited amount. The conditions agreed upon with my credit institute apply.<br/> I hereby confirm the SEPA direct debit mandate."
"Green","Green"
"Hash calculating method","Hash calculating method"
"Here you can activate Payone address checking.","Here you can activate Payone address checking."
"Here you can activate a scoring check on transactions. Setting this option to inactive means that the values selected in allow scoring, uncertain scoring and deny scoring are ignored.","Here you can activate a scoring check on transactions. Setting this option to inactive means that the values selected in allow scoring, uncertain scoring and deny scoring are ignored."
"Here you can choose if the basket (invoice items) should be sent to PayUnity. If quantities in your shop often exceed 999 you may wish to deactivate this option, as this is the maximum number supported by PayUnity. This option may not be deactivated if you are using the Payment Page.","Here you can choose if the basket (invoice items) should be sent to PayUnity. If quantities in your shop often exceed 999 you may wish to deactivate this option, as this is the maximum number supported by PayUnity. This option may not be deactivated if you are using the Payment Page."
"Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to Authorize.net. The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id}).","Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to PayUnity. The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id})."
"Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to PayUnity . The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id}).","Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to PayUnity. The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id})."
"Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to payone. The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id}).","Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to payone. The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id})."
"Here you can set if you are a trusted shop with payolution. Activate this feature according to your contract with payolution.","Here you can set if you are a trusted shop with payolution. Activate this feature according to your contract with payolution."
"Here you can set the maximum cart amount for which addresses should be checked.","Here you can set the maximum cart amount for which addresses should be checked."
"Here you can set the maximum cart amount for which scoring should be checked.","Here you can set the maximum cart amount for which scoring should be checked."
"Here you can set the maximum value for which addresses should be checked","Here you can set the maximum value for which addresses should be checked"
"Here you can set the maximum value for which scoring should be checked","Here you can set the maximum value for which scoring should be checked"
"Here you can set the minimum cart amount for which addresses should be checked.","Here you can set the minimum cart amount for which addresses should be checked."
"Here you can set the minimum cart amount for which scoring should be checked.","Here you can set the minimum cart amount for which scoring should be checked."
"Here you can set the minimum value for which addresses should be checked","Here you can set the minimum value for which addresses should be checked"
"Here you can set the minimum value for which scoring should be checked","Here you can set the minimum value for which scoring should be checked"
"Here you can set the payment information displayed to the customer when paying the merchant directly. You may use {id} as a placeholder for the transaction id. You may use simple html such as &lt;b&gt; to format the information.","Here you can set the payment information displayed to the customer when paying the merchant directly. You may use {id} as a placeholder for the transaction id. You may use simple html such as &lt;b&gt; to format the information."
"Here you can set the time that a validation is valid, in hours.","Here you can set the time that a validation is valid, in hours."
"Here you can set the type of address check to be used. If 'Person' is selected and the customer is not from Germany, 'Basic' will be used.","Here you can set the type of address check to be used. If 'Person' is selected and the customer is not from Germany, 'Basic' will be used."
"Here you can set the version of the Frontend API to be used when using the authorization method IFrame. Version 2 offers a way for you to define templates with custom CSS and JavaScript, while still being PCI DSS SAQ A compliant. Make sure 'Frontend-URL PCI DSS SAQ A compliant' is listed in the PayUnity backend under Configuration > Payment Portals > (Portal) > API-Parameter, that FinanceGate Frontend is active under selected channels under Configuration > Payment Portals > Portal > General. After that, make sure that your template is set up correctly.","Here you can set the version of the Frontend API to be used when using the authorization method IFrame. Version 2 offers a way for you to define templates with custom CSS and JavaScript, while still being PCI DSS SAQ A compliant. Make sure 'Frontend-URL PCI DSS SAQ A compliant' is listed in the PayUnity backend under Configuration > Payment Portals > (Portal) > API-Parameter, that FinanceGate Frontend is active under selected channels under Configuration > Payment Portals > Portal > General. After that, make sure that your template is set up correctly."
"Here you can set the version of the Frontend API to be used when using the authorization method Payment Page. Version 2 offers a way for you to define templates with custom CSS and JavaScript, while still being PCI DSS SAQ A compliant. Make sure 'Frontend-URL PCI DSS SAQ A compliant' is listed in the PayUnity backend under Configuration > Payment Portals > (Portal) > API-Parameter, that FinanceGate Frontend is active under selected channels under Configuration > Payment Portals > Portal > General. After that, make sure that your template is set up correctly.","Here you can set the version of the Frontend API to be used when using the authorization method Payment Page. Version 2 offers a way for you to define templates with custom CSS and JavaScript, while still being PCI DSS SAQ A compliant. Make sure 'Frontend-URL PCI DSS SAQ A compliant' is listed in the PayUnity backend under Configuration > Payment Portals > (Portal) > API-Parameter, that FinanceGate Frontend is active under selected channels under Configuration > Payment Portals > Portal > General. After that, make sure that your template is set up correctly."
"Here you can set the version of the Frontend API to be used when using the authorization method iFrame Authorization (Frontend). Version 2 offers a way for you to define templates with custom CSS and JavaScript, while still being PCI DSS SAQ A compliant. Make sure 'Frontend-URL PCI DSS SAQ A compliant' is listed in the PayUnity backend under Configuration > Payment Portals > (Portal) > API-Parameter, that FinanceGate Frontend is active under selected channels under Configuration > Payment Portals > Portal > General. After that, make sure that your template is set up correctly.","Here you can set the version of the Frontend API to be used when using the authorization method iFrame Authorization (Frontend). Version 2 offers a way for you to define templates with custom CSS and JavaScript, while still being PCI DSS SAQ A compliant. Make sure 'Frontend-URL PCI DSS SAQ A compliant' is listed in the PayUnity backend under Configuration > Payment Portals > (Portal) > API-Parameter, that FinanceGate Frontend is active under selected channels under Configuration > Payment Portals > Portal > General. After that, make sure that your template is set up correctly."
"Here you can set which solvency ratings are denied. The selected values have higher priority than the ones selected in allow scoring and uncertain scoring.","Here you can set which solvency ratings are denied. The selected values have higher priority than the ones selected in allow scoring and uncertain scoring."
"Here you can set which solvency ratings are let through while setting the transaction status to uncertain. The selected values have higher priority than the ones selected in allow scoring but lower priority than the values selected in deny scoring.","Here you can set which solvency ratings are let through while setting the transaction status to uncertain. The selected values have higher priority than the ones selected in allow scoring but lower priority than the values selected in deny scoring."
"Here you can set which solvency ratings are let through. The selected values have lower priority than the ones selected in uncertain scoring and deny scoring.","Here you can set which solvency ratings are let through. The selected values have lower priority than the ones selected in uncertain scoring and deny scoring."
"Here you may specify which CSS classes should be applied to the input field, if 'Use Style' is set to 'Classes'. These styles must be available to the page displaying the form.","Here you may specify which CSS classes should be applied to the input field, if 'Use Style' is set to 'Classes'. These styles must be available to the page displaying the form."
"Hide","Hide"
"Hide Conditions","Hide Conditions"
"Hosted iFrame Configuration","Hosted iFrame Configuration"
"Hungary","Hungary"
"I agree that that Klarna can use my adress data for identity and scoring checks. I am aware that I can revoke my !consent at any time in the future. The general terms and conditions of the merchant apply.","I agree that Klarna can use my address data to identity and for scoring of checks. I am aware that I can revoke my !consent at any time in the future. The general terms and conditions of the merchant apply."
"I agree to the presented terms.","* I agree to the presented terms."
"I want to submit the mandate.","I want to submit the mandate."
"IBAN","IBAN"
"IBAN number","Bank account number (IBAN)"
"IBAN: !iban","IBAN: !iban"
"IFrame Height","Iframe Height"
"IP could not be retrieved.","IP could not be retrieved."
"Identification number: !number","Identification number: !number"
"If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, it won't be available until the order is canceled (see timeout option below). Enable this setting to cancel the customer's pending orders when he returns to the cart page and a product is not available anymore.","If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, the item will be reserved until the payment is cancelled (see timeout option below). Activate this option if you do NOT wish to reserve the item in case of an aborted or failed payment. The order will then be cancelled immediately."
"If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, it won't be available until the order is canceled (see timeout option below). Enable this setting to cancel the customer's pending orders when he returns to the cart page and a product is not available anymore. Be careful though: If you enable this option, there is a chance that payments are made with the corresponding order being already canceled.","If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, it won't be available until the order is canceled (see timeout option below). Enable this setting to cancel the customer's pending orders when he returns to the cart page and a product is not available anymore. Be careful though: If you enable this option, there is a chance that payments are made with the corresponding order already being canceled."
"If it is not applicable leave the field empty.","If it is not applicable leave the field empty."
"If the status is 'Pending' it is unclear whether the transaction will be cancelled successfully.","If the status is 'Pending' it is unclear whether the transaction will be cancelled successfully."
"If the status is 'Pending' it is unclear whether the transaction will be captured successfully.","If the status is 'Pending' it is unclear whether the transaction will be captured successfully."
"If the status is 'Pending' it is unclear whether the transaction will be refunded successfully.","If the status is 'Pending' it is unclear whether the transaction will be refunded successfully."
"If the test mode is selected the test account is used. Otherwise the configured account is used.","If the test mode is selected the test account is used. Otherwise the configured account is used."
"If the test mode is used, then the test parameter is used.","If the test mode is used, then the test parameter is used."
"If this payment method should support recurring and moto transactions, you have to specify a second channel without 3d secure.","If this payment method should support recurring and moto transactions, you have to specify a second channel without 3d secure."
"If this payment method should support recurring and moto transactions, you have to specify a second entity ID without 3d secure.","If this payment method should support recurring and moto transactions, you have to specify a second entity ID without 3D secure."
"If you have a specific pagestyle for PayPal, you can specify it here.","If you have a specific pagestyle for PayPal, you can specify it here."
"If you have any issues with the module please feel free to contact us. See our <a href='https://www.sellxed.com/en/support' target='_blank'>support page</a> for more information.","If you have any issues with the module please feel free to contact us. See our <a href='https://www.sellxed.com/en/support' target='_blank'>support page</a> for more information."
"If you select this checkbox, the payment will be captured at the PSP and you can not add additional captures.","If you select this checkbox, the payment will be captured at the PSP and you can not add additional captures."
"If you use Klarna as processor, please specify your the merchant id (EID) of your Klarna account.","If you use Klarna as your processor, please specify your merchant ID (EID) of your Klarna account."
"If you use Klarna as processor, this setting controls the display of the conditions element. If you fulfill the Klarna requirements of displaying all necessary information in another way, and Klarna has recognized this, you may deactivate this option.","If you use Klarna as processor, this setting controls the display of the conditions element. If you fulfill the Klarna requirements of displaying all necessary information in another way, and Klarna has recognized this, you may deactivate this option."
"If you wish to use a custom style, you may either automatically detect your shop style (dynamic styling), or define the CSS above. You can either use in-line css with the 'Inline Style'-setting, or reference defined classes using the 'Classes'-setting.","If you wish to use a custom style, you may either automatically detect your shop style (dynamic styling), or define the CSS above. You can either use in-line css with the 'Inline Style'-setting, or reference defined classes using the 'Classes'-setting."
"If you would like to use recurring payments, you need our magento module ""Customweb Subscription"". For further information go to <a href=""http://www.sellxed.com/shop/"" target=""_blank"">sellxed.com</a>.","If you would like to use recurring payments, you need our Magento module ""Customweb Subscription"". For further information go to <a href=""http://www.sellxed.com/shop/"" target=""_blank"">sellxed.com</a>."
"Iframe Height","Iframe Height"
"Image Brand Selection","Image Brand Selection"
"In case ajax authorization is used, the brand of the credit card is detected by the card number and the card number is validated accordingly. This setting enables the restriction of the allowed brands.","In case ajax authorization is used, the brand of the credit card is detected by the card number and the card number is validated accordingly. This setting enables the restriction of the allowed brands."
"In case the security hash option is enabled, the hash secret is required.","In case the security hash option is enabled, the hash secret is required."
"In case the security hash option is enabled, the hash secret is required. This is only needed by the hidden authorization.","In case the security hash option is enabled, the hash secret is required. This is only needed by the hidden authorization."
"In some cases it may take a few seconds until the transaction is successfully authorized. When this option is enabled, the notification is awaited.","In some cases it may take a few seconds until the transaction is successfully authorized. When this option is enabled, the notification is awaited."
"In some cases the transaction requires a few seconds until it is successfully authorized. When this option is enabled the user is hold until the transaction reach the final state. If this is not the case in 30 seconds a message is shown to the customer, which explains the situation.","In some cases the transaction requires a few seconds until it is successfully authorized. When this option is enabled the user is on hold until the transaction reaches the final state. If this is not the case in 30 seconds a message is shown to the customer, which explains the situation."
"Inactive","Inactive"
"Incl. Tax","Incl. Tax"
"Information","Information"
"Inline Style","Inline Style"
"Instructions","Instructions"
"Integrator Test Mode","Integrator Test Mode"
"Internal","Internal (The payment information will not be sent to the acquiring bank)."
"Invalid login or password.","Invalid login or password."
"Invoice Appendix","Invoice Appendix"
"Invoice Delivery Method","Invoice Delivery Method"
"Invoice Items","Invoice Items"
"Invoice Settlement","Invoice Settlement"
"Invoice Totals","Invoice Totals"
"Invoice settlement","Invoice settlement"
"Invoice was created successfully","Invoice was successfully created"
"Invoices","Invoices"
"Issue Number","Issue Number"
"It is not possible to checkout in your country.","It is not possible to checkout in your country."
"Italy","Italy (IT)"
"Item","Item"
"Items","Items"
"Items Refunded","Items Refunded"
"JCB","JCB"
"Key / Hash did not match","Key / Hash did not match"
"Klarna","Klarna"
"Klarna (supports Hidden Authorization)","Klarna (supports Hidden Authorization)"
"Klarna (supports Payment Page)","Klarna (supports Payment Page)"
"Klarna: Conditions","Klarna: Conditions"
"Klarna: Merchant ID","Klarna: Merchant ID"
"Last name","Last name"
"Legal note","Legal note"
"Live HMAC Key","Live HMAC Key"
"Live Identifier (PBX_IDENTIFIANT)","Live Identifier (PBX_IDENTIFIANT)"
"Live Mode","Live Mode"
"Live Password","Live Password"
"Live Rang (PBX_RANG)","Live Rang (PBX_RANG)"
"Live Site ID (PBX_SITE)","Live Site ID (PBX_SITE)"
"Logging","Logging"
"Login","Login"
"Login & Register","Login & Register"
"Login and password are required.","Login and password are required."
"Maestro","Maestro"
"Mail Order / Telephone Order (MoTo)","Mail Order/Telephone Order (MoTo)"
"Mail order/telephone order authorization","Mail order/telephone order authorization"
"Male","Male"
"Manage Stores","Manage Stores"
"Manage PayUnity Aliases","Manage PayUnity Aliases"
"Mandate","Mandate"
"Mandate (@identification)","Mandate (@identification)"
"Mandate text","Mandate text"
"MasteCcard (Debit)","MasterCard (Debit)"
"MasterCard","MasterCard"
"Maximum Order Total","Maximal order total"
"Merchant ID","Merchant ID"
"Merchant Name","Merchant Name"
"Message","Message"
"Method Title","Method Title"
"Minimum Order Total","Minimum order total"
"Mit der ??bermittlung der f??r die Abwicklung des Rechnungskaufes und einer Identit??ts- und Bonit??tspr??fung erforderlichen Daten an payolution bin ich einverstanden. <a target='_top' href='!url'>Meine Einwilligung</a> kann ich jederzeit mit Wirkung f??r die Zukunft widerrufen.","Mit der ??bermittlung der f??r die Abwicklung des Rechnungskaufes und einer Identit??ts- und Bonit??tspr??fung erforderlichen Daten an payolution bin ich einverstanden. <a target='_top' href='!url'>Meine Einwilligung</a> kann ich jederzeit mit Wirkung f??r die Zukunft widerrufen."
"Month","Month"
"My PayUnity Aliases","My PayUnity Aliases"
"Narrative Text 1st line","Narrative Text 1st line"
"Narrative Text 2nd line","Narrative Text 2nd line"
"Narrative Text 3rd line","Narrative Text 3rd line"
"Netherlands","Netherlands (NL)"
"New Order","New Ordern"
"New Order Status","New order status"
"New card","New card"
"Next Update Execution Date","Next Update Execution Date"
"No","No"
"No BIC provided.","No BIC provided."
"No IBAN provided.","No IBAN provided."
"No Items","No Items"
"No Name Provided","No Name Provided"
"No additional payment information available.","No additional payment information available"
"No authorization method found for payment method !method.","No authorization method found for payment method !method."
"No button returned.","No button returned."
"No channel id is set for the payment method '!paymentMethodName'.","No channel ID is set for the payment method '!paymentMethodName'."
"No item type set.","No item type set."
"No payment methods have been configured for the generic method.","No payment methods have been configured for the generic method."
"No redirect URL provided. Contact PayUnity.","No redirect URL provided. Contact PayUnity."
"No redirection URL provided. Contact PayUnity.","No redirection URL provided. Contact PayUnity."
"No refund possible.","No refund possible"
"No response has been received.","No response has been received."
"No shipping method needed.","No shipping method needed."
"No, use the dropdown.","No, use the dropdown menu."
"None","None"
"None (processing by merchant)","None (processing by merchant)"
"None (processing by merchant, supports Server Authorization)","None (processing by merchant, supports Server Authorization)"
"Notification types","Notification types"
"Only an authorized transaction can be marked as finally declined.","Only authorized transactions can be marked as finally declined."
"Only an uncertain transaction can be marked as finally declined.","Only uncertain transactions can be marked as finally declined."
"Only authorized transaction can be cancelled.","Only authorised transaction can be cancelled."
"Only authorized transaction can be captured.","Only authorised transactions can be captured."
"Only authorized transaction can be refunded.","Only authorised transaction can be refunded."
"Only captured transaction can be refunded.","Only captured transaction can be refunded."
"Only on captured transaction the flag customer refusing to pay can be set.","It is only possible to set a flag for customers refusing to pay on captured transactions."
"Operation Mode","Operation mode"
"Order #","Order Number"
"Order Grand Total","Order Grand Total"
"Order Information","Order Information"
"Order Prefix","Order Prefix"
"Order Review","Order Review"
"Order Summary","Order Summary"
"Order Total","Order total"
"Order cancelled, because the customer was too long in the payment process of PayUnity.","Order cancelled because the customer was in the payment process of PayUnity for too long."
"Order prefix","Order prefix"
"Order was placed using %s","Order was placed using %s"
"Orders","Orders"
"Orders with with payments that are longer pending than the specified timeout in minutes are cancelled automatically.","Orders, with payments that have been pending for longer than the specified time-out in minutes, are cancelled automatically. To use this feature you have to setup the Magento cron."
"Page Style Name","Page Style Name"
"Paid Amount","Paid Amount"
"Partial captures of Klarna payments cannot be created in the shop. This can only be done in the PSP backend.","Partial captures of Klarna payments cannot be created in the shop. This can only be done in the PSP backend."
"Partial refund not possible. You may retry with the total transaction amount.","Partial refund not possible. The sum is larger than the total captured amount."
"Partial refunds of Klarna payments cannot be created in the shop. This can only be done in the PSP backend.","Partial refunds of Klarna payments cannot be created in the shop. This can only be done in the PSP backend."
"Password","Password"
"Password and password confirmation are not equal.","""Password"" and ""Password Confirmation"" don't match."
"Pay with ____paymentMethodName____","Pay with ____paymentMethodName____"
"Payment","Payment"
"Payment ID","Payment ID"
"Payment Id","Payment ID"
"Payment Information","Payment Information"
"Payment Method","Payment Method"
"Payment Methods","Payment Methods"
"Payment captured successfully.","Payment captured successfully"
"Payment could not be authorized. Cancelling order. Reason : ","Payment could not be authorised. Order is being cancelled for the reason:"
"Payment details can be entered after placing the order","Payment details can be entered after placing the order"
"Payment from Applicable Countries","Payment from applicable countries"
"Payment from Specific Countries","Payment from specific countries"
"Payment from applicable countries","Payment from applicable countries"
"Payment from specific countries","Payment from specific countries"
"Payment is pending at PayUnityCw","Payment is pending with PayUnityCw"
"Payment method description","Payment method description"
"Payment with %s","Payment with %s"
"Payment with PayUnity","Payment with PayUnity"
"Payments may be updated periodically (interval in minutes). To use this feature you have to setup magento cron","The state of the payment will be be updated periodically (interval in minutes) via API if activated. To use this feature you have to setup Magento cron."
"Payolution","Payolution"
"Payolution (Server Authorization)","Payolution (Server Authorization)"
"Payolution (supports Server Authorization)","Payolution (supports Server Authorization)"
"Payolution - Due Date","Payolution - Due Date"
"Payolution - Reference","Payolution - Reference"
"Payolution - Transaction ID","Payolution - Transaction ID"
"Payolution - Trusted Shop","Payolution - Trusted Shop"
"Payolution - Workorder ID","Payolution - Workorder ID"
"Payolution terms","Payolution terms"
"Pending","Pending"
"Pending PayUnityCw","Pending PayUnityCw"
"Person (DE only)","Person (DE only)"
"Phone","Phone"
"Phone Number","Phone number"
"Place Order","Place Order"
"Plain","Plain"
"Please accept the terms and conditions.","Please accept the terms and conditions."
"Please agree to all the terms and conditions before placing the order.","Please agree to all the terms and conditions before placing the order."
"Please check the entered CVC number.","Please check the entered CVC number."
"Please check the entered credit card number.","Please check the entered credit card number."
"Please enter here the Bank Identifier Code (BIC).","Please enter here the Bank Identifier Code (BIC)."
"Please enter here the CVC code from your card. You find the code on the back of the card.","Please enter the CVC code from your card. You can find the code on the back of the card."
"Please enter here the International Bank Account Number (IBAN).","Please enter here the International Bank Account Number (IBAN)."
"Please enter here the account holder name on the card.","Please enter here the account holder name on the card."
"Please enter here the account holder name.","Please enter here the account holder name."
"Please enter here the account number of your bank account.","Please enter here the International Bank Account Number (IBAN)."
"Please enter here the bank code of your bank.","Please enter here the bank code of your bank"
"Please enter here the card holder name on the card.","Please enter the card holder's name."
"Please enter here the commercial number of your company.","Please enter the commercial number of your company."
"Please enter here the name of the account owner.","Please enter the name of the account owner"
"Please enter here the number on your card.","Please enter your card number."
"Please enter here the sales tax number of your company.","Please enter here the sales tax number of your company."
"Please enter here your gender.","Please enter your gender here."
"Please enter here your phone number.","Please enter your phone number here."
"Please enter here your social security number.","Please enter your social security number."
"Please enter the location of your bank.","Please enter the location of your bank."
"Please enter the name of your bank.","Please enter the name of your bank."
"Please enter your IBAN number.","Please enter your IBAN number."
"Please enter your gender.","Please enter your gender."
"Please enter your issue number, if there is no CVC code on your card.","Please enter your issue number, if there is no CVC code on your card."
"Please enter your phone number.","Please enter your phone number."
"Please fill out the form completely","Please fill out the form completely"
"Please flash or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.","Please flash or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>."
"Please flash or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.', 'http://www.sellxed.com/en/support","Please empty or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.', 'http://www.sellxed.com/en/support"
"Please flush or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.","Please flush or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>."
"Please log in below:","Please log in below:"
"Please pay the order to the following account.","Please pay the order to the following account."
"Please read and accept the terms and conditions.","Please read and accept the terms and conditions."
"Please select","Please select"
"Please select a shipping method before sending the order.","Please select a shipping method before sending the order."
"Please select a shipping provider.","Please select a shipping provider."
"Please select the Bank Identifier Code (BIC) of your bank.","Please select the Bank Identifier Code (BIC) of your bank."
"Please select the day of your birth.","Please select the day of your birth."
"Please select the expiry month on your card.","You have to enter the month of the card expiry"
"Please select the expiry year on your card.","Please select your card's year of expiry."
"Please select the month of your birth.","Please select the month of your birth."
"Please select the point in the checkout process when the different checks should be processed.","Please select the point in the checkout process when the different checks should be processed."
"Please select the processor by which the invoice should be processed.","Please select the processor by which the invoice should be processed."
"Please select the the year of your birth.","Please select the the year of your birth."
"Please specify the merchant id (EID) of your Klarna account.","Please specify the merchant id (EID) of your Klarna account."
"Please wait, processing your order...","Please wait, processing your order..."
"Please wait...","Please wait..."
"Poland","Poland"
"Portal ID","Portal ID"
"Postal code","Postal code"
"Price","Price"
"Process ID","Process ID"
"Processor","Processor"
"Product","Product"
"Product Name","Product name"
"Proposed address changes","Proposed address changes"
"Purchase Point","Point of Purchase"
"Qty","Qty"
"Quantity can not be larger than 999.","Quantity can not be larger than 999."
"Received 'paid' action, however amount was incorrect (!actual instead of !expected).","Received 'paid' action, however amount was incorrect (!actual instead of !expected)."
"Received message with action @action.","Received message with action @action."
"Recurring Payments","Recurring Payments"
"Red","Red"
"Redirection was required to url @url.","Redirection was required to url @url."
"Reference No.","Reference No."
"Refund (announcement) is only available for the full order, not only parts.","Refund (announcement) is only available for the full order, not only parts."
"Refund Amount","Refunded amount"
"Refund ID","Refund ID"
"Refund amount","Refunded amount"
"Refund information","Refund information"
"Refund status","Refund status"
"Refunded Amount","Refunded amount"
"Refunds","Credit Memo"
"Register","Register"
"Register to Create an Account","Register to create an account"
"Register with us for future convenience:","Register with us for future convenience:"
"Release Date","Release Date"
"Review","Review"
"Risk Based Capture","Risk Based Capture"
"Rounding Adjustment","Rounding Adjustments"
"Rounding Fee","Rounding Fee"
"Row Total","Row Total"
"SEPA Mandate ID","SEPA Mandate ID"
"SKU:","SKU:"
"Sales","Sales"
"Sales Tax Number","Sales Tax Number"
"Save","Save"
"Saved cards:","Saved cards:"
"Score","Score"
"Scoring check executed","Scoring check executed"
"Scoring check maximum","Scoring check maximum"
"Scoring check minimum","Scoring check minimum"
"Secret Key","Secret Key"
"Secret for Encryption","Secret for Encryption"
"Secret key","Secret key"
"Security Hash","Security Hash"
"Security Hash Secret","Security Hash"
"Security Sender","Security Sender"
"See order and shipping status","See order and shipping status"
"Select 'ALL'","Select 'ALL'"
"Select 'PA' and 'DB' for PAYMENTS and 'ALL' for REGISTRATIONS.","Select 'PA' and 'DB' for PAYMENTS and 'ALL' for REGISTRATIONS."
"Select Brand","Select card brand"
"Select Method","Select Method"
"Select the authorization method to use for processing this payment method.","Select the authorisation method to use in order to process this payment method. (Please be aware that the hidden mode for credit cards (if available) requires additional PCI certification requirements. Contact PayUnity for additional information)."
"Select the date of your birth.","Select your date of birth."
"Select the date on which your card expires.","Select the expiry date of your card."
"Select the hash calculation method to use. This value must correspond with the selected value in the back-end of PayUnity.","Select the hash calculation method to use. This value must correspond with the selected value in the back-end of PayUnity."
"Selected IBAN","Selected IBAN"
"Send Basket","Send Basket"
"Send Invoice Email","Send Invoice Email"
"Send amounts to PayUnity in base currency.","Send amounts to PayUnity in base currency."
"Send amounts to PayUnity in store's base currency.","Send amounts to PayUnity in store's base currency."
"Send basket","Send basket"
"Send invoice email","Send invoice email"
"Send it by e-mail.","Send it by e-mail."
"Send it by postal services.","Send it by postal services."
"Send the invoice email automatically after invoice creation.","Send the invoice email automatically after invoice creation."
"Set direct capture after order. In this case your authoization will directly be captured on your customer's credit card","With 'direct capturing' the the customer is debited directly after completion of payment. In the case of 'deferred capturing' the amount is debited at a moment defined by you."
"Set the sort order to show the external checkouts.","Set the sort order to show the external checkouts."
"Set to 'none'.","Set to 'none'."
"Settlement after order","Settlement after order"
"Setup","Setup"
"Shipping & Handling","Shipping & Handling"
"Shipping Address","Delivery address"
"Shipping Amount","Shipping Amount"
"Shipping Information","Shipping Information"
"Shipping Method","Shipping Method"
"Shipping Methods","Shipping Methods"
"Shipping Refund","Shipping Refund"
"Shipping provider","Shipping provider"
"Shop","Shop"
"Short Installation Instructions:","Short Installation Instructions:"
"Should the amount be captured automatically after the order (direct) or should the amount only be reserved (deferred)?","Should the amount be captured automatically after the order (direct) or should the amount only be reserved (deferred)?"
"Should the amount be captured automatically after the order (direct) or should the amount only be reserved (deferred)? If instructed to do so you can also choose the risk based capturing. But please note that this feature can only be used if your account has the option activated. Contact your service provider for more information.","Should the amount be captured automatically after the order (direct) or should the amount only be reserved (deferred)? If instructed to do so you can also choose the risk based capturing. But please note that this feature can only be used if your account has the option activated. Contact your service provider for more information."
"Show","Show"
"Show Account Selection","Show Account Selection"
"Show Further Settings and Information","Show further settings and information"
"Show Image","Show Image"
"Show Payment Id","Show Payment ID"
"Show the payment id","Show the payment ID"
"Show the payment id in the order information","Show the payment ID in the order information."
"Show the payment image","Show the payment image"
"Show the payment image on the checkout page","Show the payment image on the checkout page"
"Sign In","Sign In"
"Slovak Republic","Slovak Republic"
"Social Security Number","Social Security Number"
"Sorry, your payment request has been declined by AfterPay.","Sorry, your payment request has been declined by AfterPay."
"Sort Code","Sort Code"
"Sort Order","Sort Order"
"Spain","Spain"
"State is required for country @country.","State is required for country @country."
"Status","State"
"Status Message","Status message"
"Store the payment details","Store the payment details"
"Stored payment details","Stored payment details"
"Street","Street"
"Sub Account ID","Sub Account ID"
"Submit","Submit"
"Submit Order","Submit Order"
"Submitting order information...","Submitting order information..."
"Subtotal","Subtotal"
"Success","Successful"
"Successful","Successful"
"Successful online refund of amount","Successful online refund of amount"
"Successful payment authorization.","Successful payment authorization"
"Successfull online refund of amount","Successful online refund of amount"
"Support","Support"
"Switzerland","Switzerland (CH)"
"Tax Amount","Tax Amount"
"Technical Issue Message","Technical Issue Message"
"Technical issue: This payment methods is not available at the moment.","Technical issue: This payment method is not available at the moment."
"Terms and Conditions","Terms and Conditions"
"Test Mode","Test Mode"
"Test Transaction","Test Transaction"
"The 'Integrator Test Mode' sends all payments to PayUnity, but no financial institute is contacted. In the 'Connector Test Mode' the transaction is sent to the corresponding financial institute as test transaction. In live mode all transactions are processed.","The 'Integrator Test Mode' sends all payments to PayUnity, but no financial institute is contacted. In the 'Connector Test Mode' the transaction is sent to the corresponding financial institute as test transaction. In live mode all transactions are processed."
"The Entity ID is a unique key for the identification of the unit which sends transactions into the system.","The Entity ID is a unique key for the identification of the unit which sends transactions into the system."
"The HMAC key as defined in the live backoffice of PayUnity. You may need generate the HMAC key. Please take a look at the manual how to do this.","The HMAC key as defined in the live backoffice of PayUnity. You may need generate the HMAC key. Please take a look at the manual how to do this."
"The IBAN is invalid.","The IBAN is invalid."
"The IBAN is missing.","The IBAN is missing."
"The Klarna payment method cannot be used by companies.","The Klarna payment method cannot be used by companies."
"The Klarna payment method cannot be used in the customer's country.","The Klarna payment method cannot be used in the customer's country."
"The SEPA mandate schema does not contain the tag '!tag'. This tag is required.","The SEPA mandate schema does not contain the tag '!tag'. This tag is required."
"The SHA signatures do not match.","SHA signatures do not match"
"The VAT category is invalid.","The VAT category is invalid."
"The VAT category is missing.","The VAT category is missing."
"The VAT number is missing.","The VAT number is missing."
"The alias could not be found.","The alias could not be found."
"The alias has been deleted.","The alias has been deleted."
"The alias manager allows the customer to select from a credit card previously stored. The credit card data is stored by PayUnity.","The alias manager allows the customer to pay with a previously stored credit card. The credit card data is stored with PayUnity."
"The alias transaction could not be conductet with error code: !code, and reason: !reason:","The alias transaction could not be conducted with error code: !code, and reason: !reason:"
"The amount could not be verified.","The amount could not be verified."
"The amount of !amount is authorized.","The amount of !amount is authorized."
"The amount of !amount is captured.","The amount of !amount is captured."
"The authorization is confirmed.","The authorization has been approved."
"The authorization is denied: !errorMessage","The authorization has been denied: !errorMessage"
"The authorization method '!method' is not supported.","The authorization method '!method' is not supported."
"The authorization was denied.","The authorization was denied."
"The authorization was not successful","Authorization error. Authorization not successful."
"The brand of the credit card is detected by the card number if hidden authorization is used. If the payment page is used, the user has to select the brand. The allowed credit card brands can be restricted by this setting.","The brand of the credit card is detected by the card number if hidden authorization is used. If the payment page is used, the user has to select the brand. The allowed credit card brands can be restricted by this setting."
"The brand with external name '!key' was not present in the map.","The brand with external name '!key' was not present in the map."
"The brand with key '!key' was not found in the card information map.","The brand with key '!key' was not found in the card information map."
"The cancellation could not be conducted. Error code: !error.","The cancellation could not be conducted. Error code: !error."
"The capture amount (!captureAmount) cannot be greater than the authorized amount (!authorizedAmount).","The capture amount (!captureAmount) cannot be greater than the authorized amount (!authorizedAmount)."
"The capture failed.","The capture failed."
"The capture item with SKU '@sku' has a higher amount (@amountItem) as the original item (@amountOriginal).","The capture item with SKU '@sku' has a higher amount (@amountItem) than the original item (@amountOriginal)."
"The capture item with SKU '@sku' is not present in the original order.","The capture item with SKU '@sku' is not present in the original order."
"The capturing could not be conducted. Error code: !error.","The capturing could not be conducted. Error code: !error."
"The card brand selection is automatically done depending on the entered card number. If no Javascript is active in the browser, a drop down is shown. In case JavaScript a the brand logos can be shown. Should the brand selection using images?","The card brand selection is automatically done depending on the entered card number. If no Javascript is active in the browser, a drop down is shown. In case JavaScript a the brand logos can be shown should the brand selection using images."
"The chamber of commerce number is incorrect.","The chamber of commerce number is incorrect."
"The chamber of commerce number is missing.","The chamber of commerce number is missing."
"The channel ID is a unique key for the identification of the unit which sends transactions into the system.","The channel ID is a unique key for the identification of the unit which sends transactions into the system."
"The comapny name is incorrect.","The company name is incorrect."
"The company name is missing.","The company name is missing."
"The company vat number number needs to be set.","The company vat number number needs to be set."
"The configuration has been saved.","The configuration has been saved."
"The controller class '@controller' does not provide any method with annotation 'Customweb_Payment_Endpoint_Annotation_ExtractionMethod' and valid output.","The controller class '@controller' does not provide any method with the annotation 'Customweb_Payment_Endpoint_Annotation_ExtractionMethod' and valid output."
"The country code of the phone number is invalid.","The country code of the phone number is invalid."
"The country code of the phone number is missing.","The country code of the phone number is missing."
"The currency preference is invalid.","The currency preference is invalid."
"The currency preference is missing.","The currency preference is missing."
"The customer can decide, how to receive the invoice.","The customer can decide, how to receive the invoice."
"The customer does not finish the payment with in the timeout.","The customer does not finish the payment with in the timeout."
"The date of birth is invalid.","The date of birth is invalid."
"The date of birth is missing.","The date of birth is missing."
"The date of birth is required.","The date of birth is required."
"The date of birth needs to be set.","The date of birth needs to be set."
"The debtor ID for processing SEPA mandates.","The debtor ID for processing SEPA mandates."
"The department name is invalid.","The department name is invalid."
"The department name is missing.","The department name is missing."
"The email address is invalid.","The email address is invalid."
"The email address is missing.","The email address is missing."
"The establishment number is invalid.","The establishment number is invalid."
"The establishment number is missing.","The establishment number is missing."
"The expected format is '!format' where the 'MM' means the month number and 'YY' the year number.","The expected format is '!format' where the 'MM' means the month in two digits and 'YY' the year in two digits."
"The first of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off.","The first of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off."
"The first of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off. The placeholder {reference_id} can be used to display the reference number on the account statement.","The first of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off. The placeholder {reference_id} can be used to display the reference number on the account statement."
"The gender is required.","The gender is required."
"The given BIC is invalid.","The given BIC is invalid."
"The given CVC code has the wrong length.","The given CVC code has the wrong length."
"The given IBAN is invalid.","The given IBAN is invalid."
"The given address was invalid.","The given address was invalid."
"The given card number has an invalid check sum.","The given card number has an invalid check sum."
"The given card number has an invalid length.","The given card number has an invalid length."
"The given hmac key is empty.","No HMAC key entered."
"The given identifier is empty.","The given identifier is empty."
"The given password is empty.","No password entered."
"The given rang is empty. It must be a nummeric value with length of two.","The given rang is empty. It must be a numeric value with length of two."
"The given rang is invalid. It must be a nummeric value with length of two.","The given rang is invalid. It must be a numeric value with length of two."
"The given site number is empty.","The given site number is empty."
"The given transaction is not of type Customweb_PayUnityCw_Authorization_Transaction.","The given transaction is not of type Customweb_PayUnityCw_Authorization_Transaction."
"The height of the iframe containing the input field.","The height of the iframe containing the input field."
"The house number addition is invalid.","The house number addition is invalid."
"The house number addition is missing.","The house number addition is missing."
"The house number is invalid.","The house number is invalid."
"The house number is missing.","The house number is missing."
"The identifier as defined in the backoffice of PayUnity.","The identifier as defined in the backoffice of PayUnity."
"The initials are invalid.","The initials are invalid."
"The initials are missing.","The initials are missing."
"The integrity of the response message and parameters can be guaranteed via a digital signature generated with SHA-1 algorithm. This has to be enabled in the PSP administration panel.","The integrity of the response message and parameters can be guaranteed via a digital signature generated with SHA-1 algorithm. This has to be enabled in the PSP administration panel."
"The integrity of the response message and parameters can be guaranteed via a digital signature generated with SHA-1 algorithm. This has to be enabled in the PSP administration panel. This is only needed by the hidden authorization.","The integrity of the response message and parameters can be guaranteed via a digital signature generated with SHA-1 algorithm. This has to be enabled in the PSP administration panel. This is only needed by the hidden authorization."
"The invoice could not be captured and processed by","The invoice could not be captured and processed by PayUnity."
"The invoice could not be captured and processed. Reason:","The invoice could not be captured and processed. Reason: !reason"
"The invoice could not be found.","The invoice could not be found."
"The invoice has been changed.","The invoice has been changed."
"The invoice no longer exists.","The invoice no longer exists."
"The language preference is invalid.","The language preference is invalid."
"The language preference is missing.","The language preference is missing."
"The layout does not contain the tag '!layout_tag'.","The layout does not contain the tag '!layout_tag'."
"The merchant id provided by PayUnity","The merchant id provided by PayUnity"
"The merchant name, displayed to customer on SEPA mandates.","The merchant name, displayed to customer on SEPA mandates."
"The minimum age for ordering with Payolution is 18 years.","The minimum age for ordering with Payolution is 18 years."
"The name of the city is invalid.","The name of the city is invalid."
"The name of the city is missing.","The name of the city is missing."
"The name prefix is invalid.","The name prefix is invalid."
"The name prefix is missing.","The name prefix is missing."
"The next update execution date indicates when the next cron is run to update the transaction state.","The next update execution date indicates when the next cron is run to update the transaction state."
"The order is not fully completed. Please contact the store owner.","The payment could not be conducted."
"The order number is already excisting.","The order number is already excisting."
"The order number is invalid.","The order number is invalid."
"The order number is missing.","The order number is missing."
"The orderline is invalid.","The orderline is invalid."
"The orderline is missing.","The orderline is missing."
"The parent transaction reference already exists.","The parent transaction reference already exists."
"The parent transaction reference is invalid.","The parent transaction reference is invalid."
"The parent transaction reference is missing.","The parent transaction reference is missing."
"The password is provided by PayUnity.","The password is provided by PayUnity."
"The payment could not be authorized, because it is not certain.","Payment could not be authorised as it is uncertain."
"The payment form will be displayed after submitting the order.","The payment form will be displayed after submitting the order."
"The payment has been accepted.","The payment has been accepted."
"The payment has been denied.","The payment has been denied."
"The payment method !paymentMethod does not support recurring payment.","The payment method !paymentMethod does not support recurring payments."
"The payment method !paymentMethodName does not support the currency '!currency'.","The payment method !paymentMethodName does not support the currency '!currency'."
"The payment method !paymentMethodName is not available in your country ('!country').","The payment method !paymentMethodName is not available in your country ('!country')."
"The payment method '!paymentMethod' cannot be used for Moto payments.","The payment method '!paymentMethod' cannot be used for MoTo payments."
"The payment update has been made.","The payment update has been completed."
"The portal id provided by PayUnity","The portal id provided by PayUnity"
"The postal code is invalid.","The postal code is invalid."
"The postal code is missing.","The postal code is missing."
"The product quantity is invalid.","The product quantity is invalid."
"The product quantity is missing.","The product quantity is missing."
"The provided signature does not match with the one calculated.","The provided signature does not match the one calculated."
"The rang (PBX_RANG) as defined in the live backoffice of PayUnity. It must be nummeric value with length two. In the backoffice it may be displayed as a three char value. You must truncate the first digit.","The rang (PBX_RANG) as defined in the live backoffice of PayUnity. It must be nummeric value with length two. In the back office it may be displayed as a three char value. You must truncate the first digit."
"The refund announcement failed.","The refund announcement failed."
"The refund failed due to an unkown reason.","The refund failed due to an unknown reason."
"The refund item with SKU '@sku' has a higher amount (@amountItem) as the original item (@amountOriginal).","The refund item with SKU '@sku' has a higher amount (@amountItem) as the original item (@amountOriginal)."
"The refund item with SKU '@sku' is not present in the original order.","The refund item with SKU '@sku' is not present in the original order."
"The refunding could not be conducted. Error code: !error.","The refund could not be conducted. Error code: !error."
"The response seems not to be valid.","The response seems to be invalid."
"The second of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off.","The second of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off."
"The second of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off. The placeholder {reference_id} can be used to display the reference number on the account statement.","The second of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off. The placeholder {reference_id} can be used to display the reference number on the account statement."
"The secret key set in the PayUnity backend, on https://payunity.com/bip/login.prc.","The secret key set in the PayUnity backend, on https://payunity.com/bip/login.prc."
"The secret key set in the PayUnity backend, on backendUrlLive____.","The secret key set in the PayUnity backend, on backendUrlLive____."
"The sender ID is provided by PayUnity.","The Sender ID is provided by PayUnity."
"The sender ID is provided by PayUnity. This is only needed by the hidden authorization.","The sender ID is provided by PayUnity. This is only needed by the hidden authorization."
"The shopping cart has been altered!","The shopping cart has been altered!"
"The signature could not be verified.","The signature could not be verified."
"The site id (PBX_RANG) as defined in the backoffice of PayUnity.","The Site ID (PBX_RANG) as defined in the back office of PayUnity."
"The style of the input field, using inline CSS. This style will be appkied if 'Use Style' is set to 'Inline Style'.","The style of the input field, using inline CSS. This style will be appkied if 'Use Style' is set to 'Inline Style'."
"The sub account id provided by PayUnity","The sub account id provided by PayUnity"
"The surname is invalid.","'The surname is invalid."
"The surname is missing.","The surname is missing."
"The telephone number is invalid.","The telephone number is invalid."
"The telephone number is missing.","The telephone number is missing."
"The text which will be displayed on the account- and credit card statements. This will be cut at 27 characters to make a new line. It is not possible to force a new line throught the us of e.g. spaces (' ').","The text which will be displayed on the account- and credit card statements. This will be cut at 27 characters to make a new line. It is not possible to force a new line throught the us of e.g. spaces (' ')."
"The third of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off.","The third of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off."
"The third of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off. The placeholder {reference_id} can be used to display the reference number on the account statement.","The third of three possible lines to be displayed on account and credit card statements. This line may be up to 27 characters long, all further characters will be cut off. The placeholder {reference_id} can be used to display the reference number on the account statement."
"The total order amount is invalid.","The total order amount is invalid."
"The total order amount is missing.","The total order amount is missing."
"The total refund amount (!totalRefundedAmount) cannot be greater than the captured amount (!capturedAmount).","The total refund amount (!totalRefundedAmount) cannot be greater than the captured amount (!capturedAmount)."
"The transaction cannot be cancelled online.","The transaction cannot be cancelled online."
"The transaction cannot be captured online.","The transaction cannot be captured online."
"The transaction cannot be refunded online.","The transaction cannot be refunded online."
"The transaction failed due to an unknown reason.","The transaction failed due to an unknown reason."
"The transaction failed with error code !code.","The transaction failed with error code: !code"
"The transaction is only executed in the test system. The goods should not be delivered.","The transaction is only executed in the test system. The goods should not be delivered."
"The transaction reaches an invalid state, because the response of PayUnity does corresponds to the specification. Most likely your account is misconfigured (e.g. wrong entity ID).","The transaction reaches an invalid state, because the response of PayUnity does not correspond to the specification. Most likely your account is misconfigured (e.g. wrong entity ID)."
"The transaction was cancelled by the user.","The transaction was cancelled by the user."
"The unit price is invalid.","The unit price is invalid."
"The unit price is missing.","The unit price is missing."
"The user ID is provided by PayUnity.","The User ID is provided by PayUnity."
"The validation at step authorize is not supported for ajax and hidden authorization","The validation at step authorize is not supported for ajax and hidden authorization"
"The whole transaction is cancelled.","The entire transaction has been cancelled."
"The width of the iframe containing the input field.","The width of the iframe containing the input field."
"There has been a problem during the processing of your payment.","There has been a problem during the processing of your payment."
"There has been a problem during the processing of your payment. Please contact the shop owner to make sure your order was placed successfully.","There has been a problem during the processing of your payment. Please contact the shop owner to make sure your order was placed successfully."
"There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %1","There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %1"
"There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %s","There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %s"
"There is already a customer registered using this email address. Please login using this email address or enter a different email address to register your account.","There is already a customer registered using this email address. Please login using this email address or enter a different email address to register your account."
"There is some information missing.","There is some information missing."
"There seems to be a problem with the PayUnity module.","There seems to be a problem with the PayUnity module."
"There was an error validating the login and password.","There was an error validating the login and password."
"This URL has to be entered in the backend of PayUnity as TransactionStatus URL.","This URL has to be entered in the backend of PayUnity as TransactionStatus URL."
"This account is not confirmed. <a href=""%s"">Click here</a> to resend confirmation email.","This account is not confirmed. <a href=""%s"">Click here</a> to resend the confirmation email."
"This is a brief installation instruction of the main and most important installation steps. It is important that you strictly follow the check-list. Only by doing so, the secure usage in correspondence with all security regulations can be guaranteed.","This is a brief installation instruction of the main and most important installation steps. It is important that you strictly follow the check-list. Only by doing so, the secure usage in correspondence with all security regulations can be guaranteed."
"This is a brief instruction of the main and most important installation steps, which need to be performed when installing the PayUnity module. For detailed instructions regarding additional and optional settings, please refer to the enclosed instructions in the zip.","This is a brief instruction of the most important installation steps, which need to be performed when installing the PayUnity module. For detailed instructions regarding additional and optional settings, please refer to the enclosed instructions in the zip."
"This message is returned by PayUnity for this transaction.","This message has been sent by PayUnity for this transaction."
"This message is shown to the customer, when he fails to pass the solvency check.","This message is shown to the customer, when he fails to pass the solvency check."
"This message is shown to the customer, when the system of PayUnity is not reachable or unavailable.","This message is shown to the customer, when the system of PayUnity is not reachable or unavailable."
"This password is used for recurring payments and for the alias manager as well as for the server authorization. This is the password of the PayUnity account.","This password is used for recurring payments and for the alias manager as well as for the server authorization. This is the password of the PayUnity account."
"This payment method is unavailable.","This payment method is unavailable."
"This setting can be used to activate resp. deactivate the 3D secure check. Please check with PayBox if you are allowed to deactivate the 3D secure check. This setting has only an effect when you use the payment page.","This setting can be used to activate/deactivate the 3D Secure check. Please check with PayBox if you are allowed to deactivate the 3D Secure check. This setting only has an effect if you use the payment page."
"This status is set, when the payment was successfull and it is authorized.","This status is set when the payment was successful and it is authorised."
"This transaction is already closed for further captures.","This transaction is already closed for further captures"
"This transaction is already closed for further refunds.","This transaction has been closed for further refunds."
"This transaction no longer exists.","This transaction no longer exists."
"Timeout for pending payments (min)","Time-out for pending payments (in min)."
"Titel","Title"
"Title","Title"
"Title of the payment method","Name of the payment method"
"To use the Klarna payment method, please specify the Klarna merchant id (EID).","To use the Klarna payment method, please specify the Klarna merchant ID (EID)."
"To use the Klarna payment method, the billing and shipping addresses must not differ.","To use the Klarna payment method, the billing and shipping addresses must not differ."
"To use this payment method you need to add a state to your address.","To use this payment method you need to add a state to your address."
"Total","Total"
"Total Shipping Charges","Total shipping charges"
"Track order history","Track order history"
"Transaction","Transaction"
"Transaction Authorized","Transaction authorised"
"Transaction Cancelled","Transaction cancelled"
"Transaction Captured","Transaction captured"
"Transaction History","Transaction History"
"Transaction Id","Transaction ID"
"Transaction Paid","Transaction paid"
"Transaction Uncertain","Transaction uncertain"
"Transaction Updates","Transaction Updates"
"Transaction cancelled successfully","Transaction cancelled successfully"
"Transaction captured successfully","Transaction captured successfully"
"Transaction failed with an unkown reason.","Transaction failed with an unkown reason."
"Transaction failed with code @code and message @message.","Transaction failed with code @code and message @message."
"Transaction failed with code @code.","Transaction failed with code @code."
"Transaction history","Transaction History"
"Transaction was authorized over the deferred authorization mechanism.","Transaction was authorized over the deferred authorization mechanism."
"TransactionStatus URL","TransactionStatus URL"
"Transactions","Transactions"
"Unable to save the invoice.","Unable to save the invoice."
"Uncertain Status","Uncertain status"
"Uncertain scoring","Uncertain scoring"
"Uncertain transactions","Uncertain transactions"
"Unexpected transaction context type.","Unexpected transaction context type."
"Unfortunately, we cannot handle this purchase via Klarna. Please select an alternative payment method to complete your order.","Unfortunately, we cannot handle this purchase via Klarna. Please select an alternative payment method to complete your order."
"United Kingdom","United Kingdom"
"Unknown error occurred while processing your request.","Unknown error occurred while processing your request."
"Update","Update"
"Update Qty's","Adjust Quantity"
"Update interval for payments","Update interval for payments"
"Use Base Currency","Use Base Currency"
"Use Default","Use Default"
"Use Style","Use Style"
"Use base currency","Use base currency"
"User ID","User ID"
"User Id","User ID"
"User Password","User Password"
"V PAY","V PAY"
"VISA","VISA"
"VISA (Debit)","VISA (Debit)"
"VISA Electron","VISA Electron"
"Validation","Validation"
"Validation: The amounts do not match.","Validation: The amounts do not match."
"Validation: The currencies do not match.","Validation: The currencies do not match."
"Validation: The payment brands do not match.","Validation: The payment brands do not match."
"Validation: The payment types do not match.","Validation: The payment types do not match."
"Version","Version"
"Version 1 (Classic)","Version 1 (Classic)"
"Version 2 (PCI DSS SAQ A)","Version 2 (PCI DSS SAQ A)"
"View","View"
"Visa","Visa"
"Wait On Success Page","Wait On Success Page"
"Waiting for bank","Waiting for bank"
"Waiting for client payment","Waiting for customer payment"
"Waiting for risk management","Waiting for risk management"
"We are sorry to have to inform you that your request for AfterPay Open Invoice on your order is not accepted by AfterPay. This is because of various (temporary) reasons. We advise you to choose a different payment method to complete your order.","We are sorry to have to inform you that your request for AfterPay Open Invoice on your order is not accepted by AfterPay. This is because of various (temporary) reasons. We advise you to choose a different payment method to complete your order."
"We are sorry to have to inform you that your request for AfterPay Open Invoice on your order is not accepted by AfterPay. This is because one or more fields appeared to be incorrect. We advise you to check all entered values and try again or choose a different payment method to complete your order.","We are sorry to have to inform you that your request for AfterPay Open Invoice on your order is not accepted by AfterPay. This is because one or more fields appeared to be incorrect. We advise you to check all entered values and try again or choose a different payment method to complete your order."
"We cannot get the transaction instance.","We can not establish a transaction."
"We experienced a problem with your sellxed payment extension. For more information, please visit the configuration page of the plugin.","We experienced a problem with your sellxed payment extension. For more information, please visit the configuration page of the PayUnity plugin."
"Webhook Secret Key","Webhook Secret Key"
"Webhook URL","Webhook URL"
"When the store is not available (network outage, server failure or any other outage), when the feedback of PayUnity is sent, then the transaction state is not updated. Hence no order confirmation e-mail is sent and the order is not in the paid state. By activating the transaction update, such transactions can be authorized later over direct link. To use this feature the update service must be activated and the API username and the API password must be set.","When the store is not available (network outage, server failure or any other outage), when the feedback of PayUnity is sent, then the transaction state is not updated. Hence no order confirmation e-mail is sent and the order is not in the paid state. By activating the transaction update, such transactions can be authorized later over direct link. To use this feature the update service must be activated and the API username and the API password must be set."
"When using an external checkout, the customer can either be asked to chose an option to authenticate (as guest, register or login) or he can always be checked out as guest. For the second option to work, guest checkout has to be enabled in magento.","When using an external checkout, the customer can either be asked to choose an option to authenticate (as guest, register or login) or he can always be checked out as guest. For the second option to work, guest checkout has to be enabled in Magento."
"Wrapper for encrypted notifcation","Wrapper for encrypted notifcation"
"Year","Year"
"Yellow","Yellow"
"Yes","Yes"
"Yes, use images for the brand selection.","Yes, use images for the brand selection."
"You can allow the customers to manage/delete their aliases in the their account.","You can allow the customers to manage/delete their aliases in their account."
"You can change this brand code, if your PSP supports different countries for this payment method","You can change this brand code, if your PSP supports different countries for this payment method."
"You can configure under the Webhook in the backend of PayUnity under Administration > Webhooks.","You can configure under the Webhook in the backend of PayUnity under Administration > Webhooks."
"You can decide on the order status new orders should have after they are processed successfully.","You can specify the order status for new orders that have been processed successfully."
"You can decide on the order status new orders should have that have an uncertain authorization status.","You can specify the order status for new orders that have an uncertain authorisation status."
"You can define custom parameters that are sent to the PSP with every request. Syntax: 'customParameters[RISK_MID]=123123', one parameter per line.","You can define custom parameters that are sent to the PSP with every request. Syntax: 'customParameters[RISK_MID]=123123', one parameter per line."
"You can restrict the available countries. If no country is selected all available will be shown.","You can restrict the available countries. If no country is selected all available will be shown."
"You can specify the order status for new orders that have an uncertain authorisation status.","You can specify the order status for new orders that have an uncertain authorisation status."
"You can specify the order status for orders that are approved after being in a uncertain state.","You can specify the order status for orders that are approved after being in an uncertain state."
"You can specify the order status for orders that are captured either directly after the order or manually in the backend.","You can specify the order status for orders that are captured either directly after the order or manually in the back-end."
"You can specify the order status for orders that are denied after being in a uncertain state.","You can specify the order status for orders that are denied after being in an uncertain state."
"You can specify the order status when an order is cancelled.","You can specify the order status for cancelled orders."
"You can specify the secret key during the setup of the webhook.","You can specify the secret key during the setup of the webhook."
"You can switch between the different environments, by selecting the corresponding operation mode.","You can switch between the different environments, by selecting the corresponding operation mode."
"You can switch between the different test modes.","You can switch between the different test modes."
"You can use a custom JavaScript file on the payment page and iframe authorization.","You can use a custom JavaScript file on the payment page and iFrame authorization."
"You can use a custom cancel button image for the payment page and iframe authorization.","You can use a customized cancel button image for the payment page and iFrame authorization."
"You can use a custom css file on the payment page and iframe authorization.","You can use a custom CSS file on the payment page and iFrame authorization."
"You can use a custom pay button image for the payment page and iframe authorization.","You can use a custom pay button image for the payment page and iFrame authorization."
"You created the order.","You created the order."
"You do not specify yet a user id. Please specify in the main configurations a user id.","You have not set a User ID. Please set a User ID in the main configurations."
"You do not specify yet a user password. Please specify in the main configurations a user password.","You have not set a user password. Please set a user password in the main configurations."
"You do not specify yet the global entity ID. Please specify in the main configurations a global entity id.","You have not determined the global entity ID. Please do so in the main configurations."
"You have no stored aliases.","You have no stored aliases."
"You have to enter a card number.","You have to enter a card number."
"You have to enter bank code of your bank.","Enter your bank's bank code."
"You have to enter commercial number of you company.","Please enter the commercial number of your company."
"You have to enter either the CVC code or the issue number of your card.","You have to enter either the CVC code or the issue number of your card."
"You have to enter sales tax number of you company.","You have to enter sales tax number of you company."
"You have to enter sales tax number of your company.","Please enter your company's sales tax number."
"You have to enter the BIC.","You have to enter the BIC of your bank."
"You have to enter the CVC code from your card.","Enter the CVC code from your card"
"You have to enter the IBAN.","You have to enter your IBAN number."
"You have to enter the account holder name on the card.","You have to enter the account holder name on the card."
"You have to enter the account holder name.","You have to enter the account holder name."
"You have to enter the card holder name on the card.","You have to enter the card holder name."
"You have to enter the location of your bank.","Please enter the location of your bank."
"You have to enter the name of the account owner.","Please enter the name of the account owner"
"You have to enter the name of your bank.","You have to enter the name of your bank."
"You have to enter your IBAN Number.","You have to enter your IBAN number."
"You have to enter your bank account number.","Please enter here the International Bank Account Number (IBAN)."
"You have to enter your gender.","Please enter your gender."
"You have to enter your social security number.","Please enter your social security number."
"You may choose one of the cards you paid before on this site.","You may choose from one of the cards previously used on this site."
"You may choose one of your stored payment details.","You may choose one of your stored payment details."
"You must accept the terms of the mandate.","You must accept the terms of the mandate."
"You must agree to the presented mandate text.","You must agree to the presented mandate text."
"You must agree to the terms of service.","You must agree to the terms of service."
"You receive an Entity ID when you create your PayUnity account. The entity ID specified here will be taken, when no specific Entity ID is defined in the payment method settings.","You will receive an Entity ID when you create your PayUnity account. The entity ID specified here will be used, when no specific Entity ID is defined in the payment method settings."
"You will be redirect to the order confirmation page.","You will be redirected to the order confirmation page."
"You've reached the maximum amount of Afterpay paments.","You've reached the maximum amount of Afterpay paments."
"Your CoC number is incorrect.","Your CoC number is incorrect."
"Your address is invalid or incomplete.","Your address is invalid or incomplete."
"Your age is below 18.","Your age is below 18."
"Your browser does not allow the execution of JavaScript. Please activate JavaScript in your browser and reload this page.","Your browser does not allow the execution of JavaScript. Please activate JavaScript in your browser and reload this page."
"Your checkout session expired.","Your checkout session expired."
"Your email address is invalid or incomplete.","Your email address is invalid or incomplete."
"Your order amount is too high.","Your order amount is too high."
"Your order amount is too low.","Your order amount is too low."
"Your request to use Afterpay has been declined.","Your request to use Afterpay has been declined."
"PayUnityCw Aliases","PayUnityCw Aliases"
"____paymentMethodName____","____paymentMethodName____"
"PayUnity","PayUnity"
"PayUnity Aliases","PayUnity Aliases"
"PayUnity Error","PayUnity Error"
"PayUnity Parameters","PayUnity Parameters"
"PayUnity Refund Items","PayUnity Refund Items"
"PayUnity Section","PayUnity Section"
"PayUnity Transactions","PayUnity Transactions"
"PayUnity Unique ID","PayUnity Unique ID"
"PayUnity sends the invoice to the customer. It can be either send by postal services or by e-mail.","PayUnity sends the invoice to the customer. It can be either send by postal services or by e-mail."
"PayUnity: Alias Management","PayUnity: Alias Management"
"customweb ltd","customweb ltd"
"http://www.sellxed.com/shop/en/software/manual/index/en/magento-payone-zahlungs-extension.html#chapter_3_1","http://www.sellxed.com/shop/en/software/manual/index/en/magento-payone-zahlungs-extension.html#chapter_3_1"
"md5","md5"
"no","No"
"or","or"
"sha2-384","sha2-384"
"yes","Yes"

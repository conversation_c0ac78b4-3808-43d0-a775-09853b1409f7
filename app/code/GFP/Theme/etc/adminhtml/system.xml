<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="catalog">
            <group id="product_detail_page" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Product Detail Page</label>
                <field id="lazyload" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Lazy Load Page Content</label>
                    <comment/>
                    <source_model>GFP\Theme\Model\Config\Source\Page\LazyLoad</source_model>
                </field>
            </group>
        </section>
    </system>
</config>


<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2019 Amasty (https://www.amasty.com)
 * @package Amasty_ThankYouPage
 */

namespace Mstage\SubscribeForCleverreach\Plugin\Amasty\ThankYouPage;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\Observer;
use Magento\Newsletter\Model\SubscriptionManager;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\OrderRepository;

class OrderSuccessObserver
{
    /**
     * @var SubscriptionManager
     */
    private $_newsletterSubscriber;

    /**
     * @var OrderRepository
     */
    private $orderRepository;
    private ScopeConfigInterface $scopeConfig;

    /**
     * @param SubscriptionManager $subscriber
     * @param OrderRepository $orderRepository
     */
    public function __construct(
        SubscriptionManager $subscriber,
        OrderRepository $orderRepository,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->_newsletterSubscriber = $subscriber;
        $this->orderRepository = $orderRepository;
        $this->scopeConfig = $scopeConfig;
    }

    public function beforeExecute(\Amasty\ThankYouPage\Observer\OrderSuccessObserver $subject, Observer $observer)
    {
        if($this->scopeConfig->getValue('subscribe_cleverreach/general/enabled', \Magento\Store\Model\ScopeInterface::SCOPE_STORE)){
            $orderIds = $observer->getEvent()->getOrderIds();
            $orderId = $orderIds[0];
            $order = $this->orderRepository->get($orderId);

            $this->subscribeNewsletterFromOrder($order);
        }
        return [$observer];
    }

    /**
     * @param OrderInterface $order
     */
    private function subscribeNewsletterFromOrder(OrderInterface $order)
    {
        if ($order) {
            try {
                $email = $order->getCustomerEmail();;
                $this->_newsletterSubscriber->subscribe($email, $order->getStoreId());
            } catch (\Exception $e) {  }
        }
    }
}
<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="sales.email.order.renderers">
            <block class="Mstage\CWPicker\Block\Order\Email\Items\Order\DefaultOrder" name="mstage.email.order.renderers.simple" as="simple" template="Mstage_CWPicker::email/items/order/default.phtml"/>
            <block class="Mstage\CWPicker\Block\Order\Email\Items\Order\DefaultOrder" name="mstage.email.order.renderers.configurable" as="configurable" template="Mstage_CWPicker::email/items/order/default.phtml"/>
            <block class="Mstage\CWPicker\Block\Order\Email\Items\Order\DefaultOrder" name="mstage.email.order.renderers.virtual" as="virtual" template="Mstage_CWPicker::email/items/order/default.phtml"/>
        </referenceBlock>
    </body>
</page>

<?php

namespace CopeX\AmastyReviews\ViewModel;

use Amasty\AdvancedReview\Model\Sources\Recommend;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Review implements ArgumentInterface
{

    private \Amasty\AdvancedReview\Helper\Config $config;

    public function __construct(\Amasty\AdvancedReview\Helper\Config $config)
    {
        $this->config = $config;
    }

    /**
     * @return \Amasty\AdvancedReview\Helper\Config
     */
    public function getConfig(){
        return $this->config;
    }

    public function isRecommended($review)
    {
        return $this->config->isRecommendFieldEnabled() && $review->getData('is_recommended') == Recommend::RECOMMENDED;
    }

}
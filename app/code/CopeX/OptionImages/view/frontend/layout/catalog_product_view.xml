<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="product.info.options.wrapper">
            <container name="copex.optionimages.additional.images" after="product.info.options">
                <block class="Magento\Framework\View\Element\Template"
                       name="copex_optionimages_wrapper"
                       template="CopeX_OptionImages::wrapper.phtml"/>
            </container>
        </referenceBlock>
    </body>
</page>

<?php

namespace CopeX\CartDiscountPercentage\Plugin\Magento\Quote\Model\Cart;

use Magento\Checkout\Model\Session;
use CopeX\CartDiscountPercentage\Helper\SpecialPrice;

class CartTotalRepository
{

    private Session $session;
    private SpecialPrice $specialPrice;


    public function __construct(
        Session $session,
        SpecialPrice $specialPrice
    ) {
        $this->session = $session;
        $this->specialPrice = $specialPrice;
    }

    /**
     * @param \Magento\Quote\Model\Cart\CartTotalRepository $subject
     * @param \Magento\Quote\Api\Data\TotalsInterface $result
     * @return \Magento\Quote\Api\Data\TotalsInterface
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGet(
        \Magento\Quote\Model\Cart\CartTotalRepository $subject,
        \Magento\Quote\Api\Data\TotalsInterface $result,
        $cartId
    ) {
        $totalOriginalPrice = $this->specialPrice->calculateTotalOriginalPrice($this->session->getQuote()
            ->getAllVisibleItems());
        $subtotal = $result->getSubtotalInclTax();
        $totalReductionPercent = $this->specialPrice->calculateReductionPercent($totalOriginalPrice, $subtotal);
        $result['total_original_price'] = $totalOriginalPrice;
        $result['total_is_reduction_active'] = round($totalReductionPercent) > 1;
        $result['total_reduction_in_percent'] = $totalReductionPercent;
        $result['total_reduction'] = $totalOriginalPrice - $subtotal;
        $subtotalSegment = $result['total_segments']['subtotal'];
        $subtotalSegment->setData('total_original_price', $totalOriginalPrice);
        $subtotalSegment->setData('total_reduction', $totalOriginalPrice - $subtotal);
        $subtotalSegment->setData('total_is_reduction_active', round($totalReductionPercent) > 1);
        $subtotalSegment->setData('total_reduction_in_percent', $totalReductionPercent);
        return $result;
    }
}
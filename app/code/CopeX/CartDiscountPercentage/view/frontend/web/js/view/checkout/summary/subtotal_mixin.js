/**
 * @api
 */

define([
    'jquery',
    'Magento_Checkout/js/view/summary/abstract-total',
    'Magento_Checkout/js/model/quote',
    'Magento_Customer/js/customer-data'
], function ($, totalModel, quote, customerData) {
    'use strict';


    return function( target) {
        return target.extend({
            defaults: {
                template: 'CopeX_CartDiscountPercentage/checkout/summary/subtotal'
            },

            initialize: function () {
                this._super();
                this.cart = customerData.get('cart');
            },

            getTotalOriginalPrice: function () {
                return this.getFormattedPrice(this.getTotalOriginalPriceRaw());
            },

            getTotalOriginalPriceRaw: function(){
                let price = 0;
                $.each(quote.getItems(), function(key, quoteItem){
                    if (quoteItem.hasOwnProperty('base_original_price')) {
                        price += quoteItem['base_original_price'] * quoteItem['qty'];
                    }
                    else {
                        price += quoteItem['product']['price'] * quoteItem['qty'];
                    }
                });
                return price;
            },

            getReductionInPercent: function () {
                let originPrice = this.getTotalOriginalPriceRaw();
                if (originPrice == 0) {
                    return 0;
                }
                return 100 - Math.round((this.getSubtotalRaw() / originPrice) * 100);
            },

            getSubtotal: function () {
                return this.getFormattedPrice(this.getSubtotalRaw());
            },

            getSubtotalRaw: function() {
                return quote.getTotals()().subtotal_incl_tax;
            },

            getFormattedPrice(price){
                return totalModel().getFormattedPrice(price)
            }
        });
    }
});

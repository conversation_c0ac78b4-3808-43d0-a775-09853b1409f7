<?php
/** @var \Magento\Framework\Escaper $escaper */
/** @var $block \Magento\Framework\View\Element\Template */
?>
<script>
    const loadedScripts = {};
    const lazyLoadJS = function(source, callback = null, type = 'module', wait = 0) {
        setTimeout( () => {
        if(! (source in loadedScripts)){
            loadedScripts[source] = {
                initialized: false,
                script: document.createElement('script')
            };
            loadedScripts[source].script.type = type;
            loadedScripts[source].script.src = source;
            loadedScripts[source].script.defer = loadedScripts[source].script.async = true;
            loadedScripts[source].script.fetchPriority = "medium";
            loadedScripts[source].script.addEventListener('load', () => { loadedScripts[source].initialized = true;});
            typeof callback === "function" && loadedScripts[source].script.addEventListener('load', callback);
            document.head.appendChild(loadedScripts[source].script)
        }
        else if(loadedScripts[source].initialized === false){
            typeof callback === "function" && loadedScripts[source].script.addEventListener('load', callback);
        }
        else { typeof callback === "function" && callback(); }
        },wait);
    }
    window.lazyLoadJs = lazyLoadJS;

    const loadedCss = {};
    const lazyLoadCss = function(source){
        setTimeout(()=> {
            if(! (source in loadedCss)){
                loadedCss[source] = document.createElement('link')
                loadedCss[source].rel = 'preload';
                loadedCss[source].as = 'style';
                loadedCss[source].href = source;
                loadedCss[source].addEventListener('load', (event) => {
                    event.target.onload=null;event.target.rel='stylesheet'
                });
                document.head.append(loadedCss[source]);
            }
        },0);
    }
    window.lazyLoadCss = lazyLoadCss;
    window.evaluateScript = (scriptTag) => {
        let script = document.createElement('script');
        script.type = scriptTag.type || 'text/javascript';
        if( scriptTag.hasAttribute('src') ) script.src = scriptTag.src;
        script.innerHTML = scriptTag.innerHTML;
        document.head.appendChild(script);
        document.head.removeChild(script);
    };
    window.evaluateScriptsByElement = (el) => {
        Array.from(el.getElementsByTagName("script")).forEach((e) => {
            evaluateScript(e);
        });
    };
</script>
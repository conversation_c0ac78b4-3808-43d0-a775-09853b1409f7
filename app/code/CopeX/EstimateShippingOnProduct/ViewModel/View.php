<?php
/**
 * View
 * @copyright Copyright © 2020 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\EstimateShippingOnProduct\ViewModel;

use CopeX\EstimateShippingOnProduct\Helper\Calculate;
use Magento\Directory\Model\CountryFactory;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\View\Element\Template;
use Magento\Catalog\Block\Product\View as ProductView;

class View implements ArgumentInterface
{
    /**
     * @var ProductView
     */
    private $productView;
    /**
     * @var Calculate
     */
    private $calculator;
    /**
     * @var CountryFactory
     */
    private $countryFactory;
    /**
     * @var SerializerInterface
     */
    private $serializer;

    public function __construct(
        ProductView $productView,
        Calculate $calculator,
        CountryFactory $countryFactory,
        SerializerInterface $serializer
    ) {
        $this->productView = $productView;
        $this->calculator = $calculator;
        $this->countryFactory = $countryFactory;
        $this->serializer = $serializer;
    }

    public function getRates()
    {
        $rates = $this->calculator->getShippingRatesByProduct($this->getProduct());
        return $this->calculator->prepareShippingRates($rates);
    }

    public function convertToJson($rates)
    {
        $jsonData = [];
        foreach ($rates as $carrier)
            foreach ($carrier as $rate) {
                $jsonData[trim($rate['code'])] = $rate;
            }
        return $this->serializer->serialize($jsonData);
    }

    public function getProduct()
    {
        return $this->productView->getProduct();
    }

    public function getCountryCode(){
        return $this->calculator->getDefaultCountryCode();
    }

    public function isVisible()
    {
        return $this->calculator->isEnabled() && $this->getProduct() && $this->getProduct()->isSalable();
    }

    public function getCountryNameByCode($countryCode)
    {
        $country = $this->countryFactory->create()->loadByCode($countryCode);
        return $country->getName();
    }

    public function allowOtherCountries(){
        return $this->calculator->showMore();
    }

    public function useExtraPriceBox(){
        return $this->calculator->useExtraPriceBox();
    }

    public function updateQuote(){
        return $this->calculator->updateQuote();
    }
}

<?php
/**
 * Shipping
 * @copyright Copyright © 2020 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Directory\Block\Data;

/** @var SvgIcons $hyvaicons */
$hyvaicons = $viewModels->require(SvgIcons::class);
/** @var $block \Magento\Backend\Block\Template */
/** @var $viewModel CopeX\EstimateShippingOnProduct\ViewModel\View */
$viewModel = $block->getViewModel();
$heroicons = $viewModels->require(HeroiconsOutline::class);
if ($viewModel->isVisible() && $viewModel->getRateCount() > 1) :
    $rates = $viewModel->getRates();
    ?>
    <?php if ($rates) : ?>
    <style>
        #country {
            width: 100%;
        }
    </style>
    <div x-cloak x-data="initShippingBlock()" x-init="$nextTick(() => {updatePriceLoad()})">
    <h3 class="text-lg font-semibold text-primary">
        <?= $escaper->escapeHtml(__('Shipping Rates to %1', $viewModel->getCountryNameByCode($viewModel->getCountryCode()))) ?>
    </h3>
    <div id="shipping-rates-extra-note">
        <span id="shipping-extend-title"></span>
        <span id="shipping-extend-placeholder"></span>
    </div>
    <form id="product-shipping-method">
        <?php foreach ($rates as $carrier) : ?>
            <?php foreach ($carrier as $rate) : ?>
                <div class="mt-4 mb-4">
                    <div class="flex">
                        <input name="shipping-method"
                               type="radio"
                               id="<?= $rate['code'] ?>"
                               value="<?= $rate['code'] ?>"
                               data-extended-shipping-time="<?= $rate['extended_shipping_time'] ?>"
                               data-shipping-carrier-code="<?= $rate['carrier'] ?>"
                               data-shipping-method-code="<?= $rate['method'] ?>"
                               data-price="<?= $rate['price'] ?>"
                               @change="applyShippingMethod($event.target)">
                        <label class="ml-2" for="<?= $rate['code'] ?>">
                            <span class="title"><?= $rate['title'] ?></span>
                            <?= $rate['price_formated'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endforeach; ?>
    </form>
    <div x-data="hyva.modal()">
        <?php if ($viewModel->allowOtherCountries()) : ?>
        <button @click="show('outer', $event)" type="button" class="btn btn-primary" aria-haspopup="dialog">
            <?= $escaper->escapeHtml(__('Estimate shipping for other country')); ?>
        </button>
        <?php endif; ?>
        <div x-cloak x-bind="overlay('outer')" x-spread="overlay('outer')"
             class="fixed inset-0 flex items-center justify-center text-left bg-black bg-opacity-50 z-30">
            <div x-ref="outer" role="dialog" aria-labelledby="outer-label"
                 class="inline-block max-h-screen overflow-auto bg-white shadow-xl rounded-lg p-8 text-primary">
                <div class="relative">
                    <button @click="hide; cleanContent()" class="absolute right-0">
                        <?= $heroicons->xHtml('w-8 h-8', null, null); ?>
                    </button>
                    <div class="font-semibold">
                        <?= $escaper->escapeHtml(__('Please choose your country')); ?>
                    </div>
                    <div class="mt-4 country-select">
                        <?= $block->getLayout()->getBlockSingleton(Data::class)->getCountryHtmlSelect(); ?>
                    </div>
                    <input type="hidden" name="product_id" id="product_id" value="<?= $viewModel->getProduct()->getId(); ?>"/>
                    <div class="mt-4 flex">
                        <button @click="estimateShipping" type="button" class="btn btn-primary w-full" name="btn-estimate-shipping" id="btn-estimate-shipping" type="button">
                            <?= $escaper->escapeHtml(__('Estimate shipping for other country')); ?>
                        </button>
                    </div>
                    <ul id="btn-estimate-shipping-results" class="mt-4"></ul>
                </div>
            </div>
        </div>
    </div>
    <script>
        "use strict";
        const CACHE_KEY_CART_DATA = 'cart-data';
        const CACHE_KEY_PRODUCT_DATA = 'product-data';
        const CACHE_KEY_CURRENT_SHIPPING_PRICE = 'shipping-price';

        function initShippingBlock() {
            return {
                init() {
                    this.options = {
                        updateQuote: false,
                        updatePriceBox: true,
                        useExtraPriceBox: false,
                        showExtraShippingBox: true,
                        productId: document.getElementById('product_id').value
                    };
                    this.getShippingMethodFromLocalStorage();
                },
                cleanContent() {
                    const resultElem = document.getElementById('btn-estimate-shipping-results');
                    resultElem.innerHTML = '';
                },
                updatePriceLoad() {
                    const price = <?= (float)$viewModel->getProduct()->getFinalPrice() ?>;
                    const shippingPrice = parseFloat(hyva.getBrowserStorage().getItem(CACHE_KEY_CURRENT_SHIPPING_PRICE));
                    const newPrice = price + shippingPrice;
                    window.dispatchEvent(
                        new CustomEvent(
                            'update-prices-<?= $viewModel->getProduct()->getId() ?>',
                            {
                                detail: {
                                    tierPrices: [],
                                    oldPrice: {
                                        amount: <?= (float)$viewModel->getProduct()->getPrice() ?>
                                    },
                                    finalPrice: {
                                        amount: newPrice
                                    }
                                }
                            }
                        )
                    );
                },
                estimateShipping() {
                    const resultElem = document.getElementById('btn-estimate-shipping-results');
                    resultElem.innerHTML = '';
                    const reqUrl = '<?= $block->getUrl('estimateshipping/product/estimate'); ?>';
                    const productId = '<?= $viewModel->getProduct()->getId() ?>';
                    const countryId = document.getElementById('country').value;
                    fetch(reqUrl, {
                        method: "POST",
                        headers: {
                            contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                        },
                        body: new URLSearchParams({
                            form_key: hyva.getFormKey(),
                            product_id: productId,
                            country_id: countryId
                        })
                    }).then((response) => {
                        return response.json();
                    })
                        .then((data) => {
                            if (data.error) {
                                dispatchMessages([{
                                    type: 'error',
                                    text: data.error
                                }], 3000)
                            }
                            else {
                                this.drawMethods(data, resultElem);
                            }
                        })
                        .catch(() => {
                            dispatchMessages([{
                                type: 'error',
                                text: "<?= $escaper->escapeJs(__('Something went wrong. Please try again later.')); ?>"
                            }], 3000)
                        });
                },
                drawMethods(respData, resultElem) {
                    const allData = respData.data;
                    if (Object.keys(allData).length > 0) {
                        Object.values(allData).forEach(items => {
                            if (items.length > 0) {
                                items.forEach((item) => {
                                    let li = document.createElement('li');
                                    li.innerHTML = item.title + '  ' + item.price_formated;
                                    if (item.extended_shipping_time) {
                                        li.innerHTML = li.innerHTML + '. '
                                            + '<?= $escaper->escapeHtml(__('Estimate shipping for other country')); ?>' + ' - '
                                            + item.extended_shipping_time;
                                    }
                                    resultElem.appendChild(li);
                                });
                            }
                        });
                    }
                },
                applyShippingMethod(elem) {
                    this.storeShippingMethodToSession(elem.value);
                    this.storeShippingMethodToLocalStorage(elem.value);
                    this.handleShippingMethodChange(elem);
                },
                storeShippingMethodToSession(shippingMethod) {
                    const reqUrl = '<?= $block->getUrl('estimateshipping/shippingmethod/setmethod'); ?>';
                    fetch(reqUrl, {
                        method: "POST",
                        headers: {
                            contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                        },
                        body: new URLSearchParams({
                            form_key: hyva.getFormKey(),
                            shipping_method: shippingMethod
                        })
                    })
                },
                storeShippingMethodToLocalStorage(shippingMethod) {
                    hyva.getBrowserStorage().setItem(CACHE_KEY_PRODUCT_DATA, JSON.stringify({
                        shippingMethod: shippingMethod
                    }));
                },
                getShippingMethodFromLocalStorage() {
                    const productData = JSON.parse(hyva.getBrowserStorage().getItem(CACHE_KEY_PRODUCT_DATA));
                    if (productData && productData.shippingMethod) {
                        const shippingMethodRadio = document.getElementById(productData.shippingMethod);
                        if (shippingMethodRadio) {
                            shippingMethodRadio.checked = true;
                            this.handleShippingMethodChange(shippingMethodRadio);
                        }
                    }
                },
                handleShippingMethodChange(shippingMethodRadio) {
                    if (this.options.updatePriceBox) {
                        this.updatePrice(shippingMethodRadio.getAttribute('data-price'));
                    }
                    if (this.options.showExtraShippingBox) {
                        const time = shippingMethodRadio.getAttribute('data-extended-shipping-time');
                        this.showExtraShipping(time);
                    }
                    if (this.options.updateQuote) {
                        hyva.getBrowserStorage().setItem(CACHE_KEY_CART_DATA, JSON.stringify({
                            shippingCarrierCode: shippingMethodRadio.getAttribute("shipping-carrier-code"),
                            shippingMethodCode: shippingMethodRadio.getAttribute("shipping-method-code")
                        }));
                    }
                },
                storeShippingPriceToLocalStorage(price) {
                    hyva.getBrowserStorage().setItem(CACHE_KEY_CURRENT_SHIPPING_PRICE, price);
                },
                updatePrice(price) {
                    this.storeShippingPriceToLocalStorage(price);
                    this.updatePriceLoad();
                },
                showExtraShipping(string) {
                    if (string !== '') {
                        const title = '<?= $escaper->escapeHtml( __('Estimate shipping for other country')) ?>';
                        document.getElementById('shipping-extend-title').innerHTML = title + ': ';
                        document.getElementById('shipping-extend-placeholder').innerHTML = string;
                    } else {
                        document.getElementById('shipping-extend-title').innerHTML = '';
                        document.getElementById('shipping-extend-placeholder').innerHTML = string;
                    }
                }
            }
        }
    </script>
<?php endif; ?>
    </div>
<?php endif; ?>

<?php

namespace CopeX\RegistrationHandler\Observer\Email;

use Magento\Customer\Model\Customer;

class AddCompanyCC implements \Magento\Framework\Event\ObserverInterface
{

    /**
     * Execute observer
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(
        \Magento\Framework\Event\Observer $observer
    ) {
        $builder = $observer->getEvent()->getBuilder();
        $templateVars = $observer->getEvent()->getTemplateVars();
        $customerData = $templateVars->getCustomer();
        if($customerData instanceof Customer){
            $email = $customerData->getEmail();
            $companyEmail = $customerData->getCompanyEmail();
            if ($companyEmail && $companyEmail != $email) {
                $builder->addCc($companyEmail, $customerData->getName());
            }
        }
    }
}

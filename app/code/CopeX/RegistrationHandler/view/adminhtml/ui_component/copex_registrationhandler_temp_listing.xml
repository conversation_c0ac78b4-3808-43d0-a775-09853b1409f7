<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider"
                  xsi:type="string">copex_registrationhandler_temp_listing.copex_registrationhandler_temp_listing_data_source
            </item>
        </item>
    </argument>
    <settings>
        <spinner>copex_registrationhandler_temp_columns</spinner>
        <deps>
            <dep>copex_registrationhandler_temp_listing.copex_registrationhandler_temp_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource component="Magento_Ui/js/grid/provider" name="copex_registrationhandler_temp_listing_data_source">
        <settings>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>CopeX_RegistrationHandler::Temp</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="copex_registrationhandler_temp_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>temp_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="copex_registrationhandler_temp_columns">
        <selectionsColumn name="ids">
            <settings>
                <indexField>temp_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="temp_id">
            <settings>
                <filter>text</filter>
                <sorting>asc</sorting>
                <label translate="true">ID</label>
            </settings>
        </column>
        <column name="email">
            <settings>
                <filter>text</filter>
                <label translate="true">Email</label>
            </settings>
        </column>
        <column name="firstname">
            <settings>
                <filter>text</filter>
                <label translate="true">Firstname</label>
            </settings>
        </column>
        <column name="lastname">
            <settings>
                <filter>text</filter>
                <label translate="true">Lastname</label>
            </settings>
        </column>
        <column name="company">
            <settings>
                <filter>text</filter>
                <label translate="true">Company</label>
            </settings>
        </column>
        <column name="password">
            <settings>
                <filter>text</filter>
                <label translate="true">Password</label>
            </settings>
        </column>
    </columns>
</listing>

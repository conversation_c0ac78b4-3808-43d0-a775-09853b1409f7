<?php

namespace CopeX\RegistrationHandler\Controller\Adminhtml\MassApprove;

use CopeX\RegistrationHandler\Helper\Data;
use Magento\Backend\App\Action;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Ui\Component\MassAction\Filter;

/**
 * Class Index
 * @package CopeX\RegistrationHandler\Controller\Adminhtml\MassApprove
 */
class Index extends Action
{
    const ROUTE_PATH = 'customer';

    /** @var CustomerRepositoryInterface */
    private $customerRepositoryInterface;

    /** @var CollectionFactory */
    private $collectionFactory;

    /** @var Filter */
    private $filter;

    /** @var Data */
    private $dataHelper;

    /**
     * Index constructor.
     * @param Action\Context $context
     * @param CustomerRepositoryInterface $customerRepositoryInterface
     * @param CollectionFactory $collectionFactory
     * @param Filter $filter
     * @param Data $dataHelper
     */
    public function __construct(
        Action\Context $context,
        CustomerRepositoryInterface $customerRepositoryInterface,
        CollectionFactory $collectionFactory,
        Filter $filter,
        Data $dataHelper
    )
    {
        $this->customerRepositoryInterface = $customerRepositoryInterface;
        $this->collectionFactory = $collectionFactory;
        $this->filter = $filter;
        $this->dataHelper = $dataHelper;
        parent::__construct($context);
    }

    /**
     * Dispatch request
     *
     * @return ResultInterface|ResponseInterface
     */
    public function execute()
    {
        $createdCollection = $this->collectionFactory->create();
        $updatedRows = 0;

        try {
            $collection = $this->filter->getCollection($createdCollection);
        } catch (\Exception $e) {
            return $this->dataHelper->messageRedirect(self::ROUTE_PATH, __('Customer collection could not be loaded.'), Data::MSG_ERROR);
        }

        foreach ($collection as $customer) {
            try {
                $customer = $this->customerRepositoryInterface->getById($customer->getId());
            } catch (\Exception $e) {
                continue;
            }

            $isApprovedAttribute = $customer->getCustomAttribute(Data::IS_APPROVED);

            if ($isApprovedAttribute != null) {
                $currentApprovedState = boolval($isApprovedAttribute->getValue()) ? 1 : 0;
                $newApprovedState = !$currentApprovedState;
                $customer->setCustomAttribute(Data::IS_APPROVED, $newApprovedState);

                try {
                    $this->customerRepositoryInterface->save($customer);
                } catch (\Exception $e) {
                    continue;
                }

                $updatedRows++;
            }
        }

        return $this->dataHelper->messageRedirect(self::ROUTE_PATH, __('The state of %1 customer(s) was changed.', $updatedRows), Data::MSG_SUCCESS);
    }
}

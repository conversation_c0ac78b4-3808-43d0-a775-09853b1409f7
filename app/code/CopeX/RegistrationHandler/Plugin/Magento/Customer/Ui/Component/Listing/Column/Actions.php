<?php
/**
 * Created by PhpStorm.
 * User: stephan-copex
 * Date: 12.06.17
 * Time: 08:10
 */

namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Ui\Component\Listing\Column;

use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;

class Actions
{

    protected $context;
    protected $urlBuilder;

    public function __construct(ContextInterface $context,
                                UrlInterface $urlBuilder)
    {
        $this->context = $context;
        $this->urlBuilder = $urlBuilder;
    }


    public function afterPrepareDataSource(
        \Magento\Customer\Ui\Component\Listing\Column\Actions $subject,
        array $dataSource
    )
    {
        if (isset($dataSource['data']['items'])) {
            $storeId = $this->context->getFilterParam('store_id');

            foreach ($dataSource['data']['items'] as &$item) {
                $item[$subject->getData('name')]['approve'] = [
                    'href' => $this->urlBuilder->getUrl("copex_registration/approve/", ["entity_id" => $item['entity_id']]),
                    'label' => __('Activate/Deactivate Customer'),
                    'hidden' => false,
                ];
            }
        }

        return $dataSource;
    }


}
<?php
/**
 * Blocks customer after he did register and unblock him by activating his account in backend
 * Copyright (C) 2017  CopeX
 * This file is part of CopeX/RegistrationHandler.
 * CopeX/RegistrationHandler is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account;

use CopeX\RegistrationHandler\Helper\Data;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class Confirm
 * @package CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account
 */
class Confirm
{
    const ROUTE_PATH = '*/*/login';

    /** @var CustomerRepositoryInterface */
    private $customerRepository;

    /** @var AccountManagementInterface */
    private $customerAccountManagement;

    /** @var Data */
    private $dataHelper;

    /**
     * Confirm constructor.
     * @param CustomerRepositoryInterface $customerRepository
     * @param AccountManagementInterface $customerAccountManagement
     * @param Data $dataHelper
     */
    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        AccountManagementInterface $customerAccountManagement,
        Data $dataHelper
    )
    {
        $this->customerRepository = $customerRepository;
        $this->customerAccountManagement = $customerAccountManagement;
        $this->dataHelper = $dataHelper;
    }

    /**
     * @param \Magento\Customer\Controller\Account\Confirm $subject
     * @param \Closure $proceed
     * @return Redirect|mixed
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function aroundExecute(
        \Magento\Customer\Controller\Account\Confirm $subject,
        \Closure $proceed
    )
    {
        $customerId = $subject->getRequest()->getParam('id', false);
        $key = $subject->getRequest()->getParam('key', false);

        if (empty($customerId) || empty($key)) {
            throw new \Exception(__('Bad request.'));
        }

        $customer = $this->customerRepository->getById($customerId);
        $isApprovedAttribute = $customer->getCustomAttribute(Data::IS_APPROVED);

        if ($isApprovedAttribute && $isApprovedAttribute->getValue()) {
            return $proceed();
        } else {
            $email = $customer->getEmail();
            $status = $this->customerAccountManagement->getConfirmationStatus($customer->getId());

            if ($status != AccountManagementInterface::ACCOUNT_CONFIRMED) {
                $this->customerAccountManagement->activate($email, $key);
            }

            return $this->dataHelper->messageRedirect(self::ROUTE_PATH, __("Email address was confirmed, but your account is not activated yet!"), Data::MSG_ERROR);
        }
    }
}

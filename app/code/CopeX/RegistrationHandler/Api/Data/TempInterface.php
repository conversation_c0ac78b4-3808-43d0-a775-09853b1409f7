<?php

namespace CopeX\RegistrationHandler\Api\Data;

/**
 * Interface TempInterface
 * @package CopeX\RegistrationHandler\Api\Data
 */
interface TempInterface
{
    const EMAIL = 'email';
    const TEMP_ID = 'temp_id';

    /**
     * Get temp_id
     * @return string|null
     */
    public function getTempId();

    /**
     * Set temp_id
     * @param string $tempId
     * @return TempInterface
     */
    public function setTempId($tempId);

    /**
     * Get email
     * @return string|null
     */
    public function getEmail();

    /**
     * Set email
     * @param string $email
     * @return TempInterface
     */
    public function setEmail($email);
}

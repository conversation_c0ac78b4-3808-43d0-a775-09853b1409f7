<?php

namespace CopeX\MediaManagerFiles\Plugin;

use Magento\Cms\Model\Wysiwyg\Images\Storage;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\ValidatorException;

class PdfThumbnail
{
    public const PDF_ICON_PATH = '/wysiwyg/pdf-icon.png';

    private \Magento\Cms\Helper\Wysiwyg\Images $cmsWysiwygImages;
    private \Magento\Framework\Filesystem\Directory\Read $directory;

    private DirectoryList $directoryList;

    public function __construct(
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Cms\Helper\Wysiwyg\Images $cmsWysiwygImages,
        DirectoryList $directoryList
    ) {
        $this->directory = $filesystem->getDirectoryRead(DirectoryList::MEDIA);
        $this->cmsWysiwygImages = $cmsWysiwygImages;
        $this->directoryList = $directoryList;
    }

    /**
     * @throws FileSystemException
     * @throws ValidatorException
     */
    public function afterGetThumbnailUrl(
        Storage $subject,
        string $result,
        string $filePath,
        bool $checkFile = false
    ): string {
        $fileInfo = pathinfo($filePath);
        if ($fileInfo['extension'] === 'pdf') {
            $thumbPath = $this->directoryList->getPath(DirectoryList::MEDIA) . self::PDF_ICON_PATH;
            $thumbRelativePath = ltrim($this->directory->getRelativePath($thumbPath), '/\\');
            $baseUrl = rtrim($this->cmsWysiwygImages->getBaseUrl(), '/');
            return str_replace('\\', '/', $baseUrl . '/' . $thumbRelativePath);
        }
        return $result;
    }
}

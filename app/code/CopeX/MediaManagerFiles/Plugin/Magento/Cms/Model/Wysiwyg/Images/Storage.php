<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\MediaManagerFiles\Plugin\Magento\Cms\Model\Wysiwyg\Images;

use Magento\Framework\Exception\LocalizedException;

class Storage
{
    protected \Magento\Framework\View\Asset\Repository $assetRepo;
    private \Magento\Framework\App\State $state;
    private bool $isAreaCodeSet = false;

    public function __construct(
        \Magento\Framework\View\Asset\Repository $_assetRepo,
        \Magento\Framework\App\State $state
    ) {
        $this->assetRepo = $_assetRepo;
        $this->state = $state;
    }

    /**
     * @param                                           $source
     * @param                                           $keepRatio
     */
    public function aroundResizeFile(
        \Magento\Cms\Model\Wysiwyg\Images\Storage $subject,
        \Closure $proceed,
        $source,
        $keepRatio = true
    ): bool|string {
        $this->init();
        try {
            $result = $proceed($source, $keepRatio);
        } catch (LocalizedException  $e) {
            return $this->assetRepo->getUrl(\Magento\Cms\Model\Wysiwyg\Images\Storage::THUMB_PLACEHOLDER_PATH_SUFFIX);
        }
        return $result;
    }

    private function init(): void
    {
        try {
            if (! $this->isAreaCodeSet) {
                $this->isAreaCodeSet = true;
                $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
            }
        } catch (\Exception $e) {
        }
    }
}

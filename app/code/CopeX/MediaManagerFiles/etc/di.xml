<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Cms\Model\Wysiwyg\Images\Storage">
        <plugin name="CopeX_MediaManagerFiles_Plugin_Magento_Cms_Model_Wysiwyg_Images"
                type="CopeX\MediaManagerFiles\Plugin\Magento\Cms\Model\Wysiwyg\Images\Storage" sortOrder="10"
                disabled="false"/>
        <plugin name="PdfThumbnail"
                type="CopeX\MediaManagerFiles\Plugin\PdfThumbnail"/>
    </type>
    <type name="Magento\MediaGallerySynchronization\Model\FetchMediaStorageFileBatches">
        <arguments>
            <argument name="fileExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
                <item name="pdf" xsi:type="string">pdf</item>
            </argument>
        </arguments>
    </type>
</config>

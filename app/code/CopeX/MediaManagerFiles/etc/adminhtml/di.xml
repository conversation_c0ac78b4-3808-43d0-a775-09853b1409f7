<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Cms\Model\Wysiwyg\Images\Storage">
        <arguments>
            <argument name="extensions" xsi:type="array">
                <item name="allowed" xsi:type="array">
                    <item name="jpg" xsi:type="string">image/jpg</item>
                    <item name="jpeg" xsi:type="string">image/jpeg</item>
                    <item name="png" xsi:type="string">image/png</item>
                    <item name="gif" xsi:type="string">image/gif</item>
                    <item name="pdf" xsi:type="string">application/pdf</item>
                </item>
                <item name="image_allowed" xsi:type="array">
                    <item name="jpg" xsi:type="string">image/jpg</item>
                    <item name="jpeg" xsi:type="string">image/jpeg</item>
                    <item name="png" xsi:type="string">image/png</item>
                    <item name="gif" xsi:type="string">image/gif</item>
                    <item name="pdf" xsi:type="string">application/pdf</item>
                </item>
                <item name="media_allowed" xsi:type="array">
                    <item name="flv" xsi:type="string">video/x-flv</item>
                    <item name="avi" xsi:type="string">video/x-msvideo</item>
                    <item name="mov" xsi:type="string">video/x-sgi-movie</item>
                    <item name="rm" xsi:type="string">application/vnd.rn-realmedia</item>
                    <item name="wmv" xsi:type="string">video/x-ms-wmv</item>
                    <item name="pdf" xsi:type="string">application/pdf</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\MediaGallerySynchronization\Model\FetchMediaStorageFileBatches">
        <arguments>
            <argument name="fileExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
                <item name="pdf" xsi:type="string">pdf</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\MediaGalleryIntegration\Plugin\SaveImageInformation">
        <arguments>
            <argument name="imageExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
                <item name="pdf" xsi:type="string">pdf</item>
            </argument>
        </arguments>
    </type>
</config>

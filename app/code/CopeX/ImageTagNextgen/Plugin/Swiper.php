<?php

namespace CopeX\ImageTagNextgen\Plugin;



use CopeX\ImageTagNextgen\Helper\NextGenImages;

class Swiper
{

    private NextGenImages $nextGenImages;

    public function __construct(NextGenImages $nextGenImages)
    {
        $this->nextGenImages = $nextGenImages;
    }

    /**
     * @param Swiper $subject
     * @param        $result
     * @param        $images
     */
    public function afterPrepareImages(\CopeX\Swiper\ViewModel\Swiper $subject, $result, $images)
    {
        foreach($result as $key => $image){
            foreach(["url","thumb","lightbox"] as $type){
                if(isset($image['responsive'][$type]['srcset'])){
                    $result[$key]['responsive'][$type]['sources'] = $this->nextGenImages->getNextGenSources($image['responsive'][$type]['srcset']);
                }
            }
        }
        return $result;
    }
}
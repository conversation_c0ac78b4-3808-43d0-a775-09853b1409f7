<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <catalog>
            <productalert>
                <allow_price>0</allow_price>
                <allow_stock>0</allow_stock>
                <email_price_template>catalog_productalert_email_price_template</email_price_template>
                <email_stock_template>catalog_productalert_email_stock_template</email_stock_template>
                <email_identity>general</email_identity>
            </productalert>
            <productalert_cron>
                <error_email />
                <error_email_template>catalog_productalert_cron_error_email_template</error_email_template>
                <error_email_identity>general</error_email_identity>
            </productalert_cron>
        </catalog>
    </default>
</config>

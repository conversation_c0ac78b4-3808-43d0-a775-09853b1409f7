<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="CopeX\CheckInvalidCache\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="CopeX\CheckInvalidCache\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">fpcLogger</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">CopeX\CheckInvalidCache\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\App\Cache">
        <plugin name="check_cache_plugin" type="CopeX\CheckInvalidCache\Plugin\CheckCachePlugin" sortOrder="10" />
    </type>
</config>

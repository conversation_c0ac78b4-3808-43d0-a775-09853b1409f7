<?php


namespace CopeX\StaffMemberCard\Helper;

use Magento\Framework\App\Helper\AbstractHelper;

class Data extends AbstractHelper
{

    protected $_staffModel;

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \MageB2B\Staff\Model\Staff $staff
    )
    {
        parent::__construct($context);
        $this->_staffModel = $staff;
    }


    /**
     * @param $id
     * @return string
     */
    public function getSalesStaffFromCustomerId($id)
    {
        $staffs = $this->_staffModel->getCustomerStaffIds($id);
        $staff = reset($staffs);
        return $staff;
    }
}

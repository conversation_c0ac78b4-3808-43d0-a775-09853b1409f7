<?php


namespace CopeX\StaffMemberCard\Block\Staff;

use Exception;
use MageB2B\Staff\Model\StaffRepository;

class Card extends \Magento\Framework\View\Element\Template
{

    protected $_customerSession;
    protected $_helper;
    protected $_staffRepository;
    protected $_imageUploader;

    protected $_notAllowedEmails = ["<EMAIL>"];

    /**
     * Constructor
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Customer\Model\Session $customerSession,
        \CopeX\StaffMemberCard\Model\ImageUploader $imageUploader,
        \CopeX\StaffMemberCard\Helper\Data $helper,
        StaffRepository $staffRepository,
        array $data = []
    )
    {
        parent::__construct($context, $data);

        $this->_customerSession = $customerSession;
        $this->_helper = $helper;
        $this->_staffRepository = $staffRepository;
        $this->_imageUploader = $imageUploader;
    }

    /**
     * @return array
     **/
    public function getCardData()
    {
        try {
            $id = $this->_customerSession->getId();
            if ($id) {
                $staffId = $this->_helper->getSalesStaffFromCustomerId($id);
                if ($staffId) {
                    $staff = $this->_staffRepository->getById($staffId);
                    $data = $staff->getData();
                    if (array_search($staff->getEmail(), $this->_notAllowedEmails) !== false) {
                        return [];
                    }
                    if ($staff->getStaffImage()) {
                        $url = $this->_imageUploader->getMediaUrl($data["staff_image"]);
                        $data["staff_image"] = $url;
                    }
                    return $data;
                }
            }
        } catch (Exception $exception) {
            $this->_logger->error($exception);
        }
        return [];
    }
}

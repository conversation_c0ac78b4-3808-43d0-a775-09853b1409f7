<?php

namespace CopeX\StaffMemberCard\Observer\Email;

use Magento\Customer\Model\Customer;
use Magento\Framework\Event\Observer;
use Magento\Framework\Mail\Template\TransportBuilder;
use MageB2B\Staff\Model\StaffRepository;

class AddStaffCC implements \Magento\Framework\Event\ObserverInterface
{

    /**
     * @var \CopeX\StaffMemberCard\Helper\Data
     */
    private $data;
    /**
     * @var StaffRepository
     */
    private $staffRepository;

    public function __construct(\CopeX\StaffMemberCard\Helper\Data $data, StaffRepository $staffRepository)
    {
        $this->data = $data;
        $this->staffRepository = $staffRepository;
    }

    /**
     * Execute observer
     * @param Observer $observer
     * @return void
     */
    public function execute(
        Observer $observer
    ) {
        try {
            /** @var TransportBuilder $builder */
            $builder = $observer->getEvent()->getBuilder();
            $templateVars = $observer->getEvent()->getTemplateVars();
            $customerData = $templateVars->getCustomer();
            if ($customerData instanceof Customer) {
                $id = $customerData->getId();
                $staffMemberId = $this->data->getSalesStaffFromCustomerId($id);
                if ($staffMemberId) {
                    $staff = $this->staffRepository->getById($staffMemberId);
                    $builder->addBcc($staff->getEmail());
                }
            }
        } catch(\Exception $e) {}
    }
}

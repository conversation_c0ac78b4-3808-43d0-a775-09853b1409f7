<?xml version="1.0" ?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="product.info">
            <block as="seolink" class="Magento\Framework\View\Element\Template" name="seolink"
                   template="CopeX_ProductDetailSeoLink::hyva/seolink.phtml">
                <arguments>
                    <argument name="view_model" xsi:type="object">CopeX\ProductDetailSeoLink\ViewModel\SeoLink</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>

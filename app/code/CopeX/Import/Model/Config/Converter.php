<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 23.05.17
 * Time: 12:03
 */

namespace CopeX\Import\Model\Config;


class Converter implements \Magento\Framework\Config\ConverterInterface
{
    const XML_TAG_NAME = 'copex_import';
    protected $ignoredElements = array('#text', '#comment');

    public function convert($source)
    {
        $xpath = new \DOMXPath($source);
        $importProfiles = $xpath->query("//default/".self::XML_TAG_NAME);
        $profileInfo = [];
        foreach ($importProfiles as $key => $importProfile) {

                $data = $this->Xml2Array($importProfile);
                $profileInfo += $data;

        }


        return ['import_profiles' => $profileInfo];
    }

    protected function Xml2Array($root) {
        $result = array();
        if ($root->hasAttributes()) {
            $attrs = $root->attributes;
            foreach ($attrs as $attr) {
                $result['@attributes'][$attr->name] = $attr->value;
            }
        }

        if ($root->hasChildNodes()) {
            $children = $root->childNodes;
            if ($children->length == 1) {
                $child = $children->item(0);
                if (in_array($child->nodeType, array(XML_TEXT_NODE, XML_CDATA_SECTION_NODE)) ) {
                    $result['_value'] = $child->nodeValue;
                    return count($result) == 1
                        ? $result['_value']
                        : $result;
                }
            }
            $groups = array();
            foreach ($children as $child) {
                if (in_array($child->nodeName, $this->ignoredElements) ) {
                    continue;
                }
                if (!isset($result[$child->nodeName])) {
                    $result[$child->nodeName] = $this->Xml2Array($child);
                } else {
                    if (!isset($groups[$child->nodeName])) {
                        $result[$child->nodeName] = array($result[$child->nodeName]);
                        $groups[$child->nodeName] = 1;
                    }
                    $result[$child->nodeName][] = $this->Xml2Array($child);
                }
            }
        }

        return $result;
    }
}
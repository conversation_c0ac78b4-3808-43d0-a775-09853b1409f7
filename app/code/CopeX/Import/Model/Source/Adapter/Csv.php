<?php
/**
 * CopeX_Import
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category    CopeX
 * @package     CopeX_Import
 * @copyright   Copyright © 2014 CopeX (http://copex.io/)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 * @ *
 * XML streamer based on https://github.com/prewk/XmlStreamer/blob/master/XmlStreamer.php
 */
namespace CopeX\Import\Model\Source\Adapter;

use Magento\Framework\App\Filesystem\DirectoryList;

class Csv extends \Magento\ImportExport\Model\Import\Source\Csv
{
    /**
     * Csv constructor.
     * @param string                        $file
     * @param \Magento\Framework\Filesystem $fileSystem
     * @param string                        $delimiter
     * @param string                        $enclosure
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function __construct($file, \Magento\Framework\Filesystem $fileSystem, $delimiter = ',', $enclosure = '"')
    {
        $directory = $fileSystem->getDirectoryRead(DirectoryList::VAR_DIR);
        /** @var \Magento\Framework\Filesystem\Directory\Read $directory */
        parent::__construct($file, $directory, $delimiter, $enclosure);
        $this->rewind();
    }
}
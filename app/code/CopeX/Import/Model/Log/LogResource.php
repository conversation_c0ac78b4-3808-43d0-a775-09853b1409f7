<?php

namespace CopeX\Import\Model\Log;

use CopeX\Import\Model\LogFactory;
use CopeX\Import\Model\LogRepository;
use Monolog\Logger;

class LogResource
{
    /**
     * @var LogFactory
     */
    private $logFactory;

    /**
     * @var LogRepository
     */
    private $repository;

    public function __construct(LogFactory $logFactory, LogRepository $repository)
    {
        $this->logFactory = $logFactory;
        $this->repository = $repository;
    }

    public function saveExecution(\CopeX\Import\Model\Import $import){
        $logger = $import->getLogger();
        /** @var \CopeX\Import\Model\Log $log */
        $log = $this->logFactory->create();
        $log->setStartedAt($import->getStartDate());
        $log->setFinishedAt($import->getEndDate());
        $log->setName($import->getProfile());
        $log->setStatus($logger->hasErrors() ? 0 : 1);
        $log->setOutput($this->formatOutput($logger->getEntries()));
        $this->repository->save($log);
    }

    protected function formatOutput($output)
    {
        return implode("<br>", array_map(function($entry){
            return Logger::getLevelName($entry['level']) . ": " . $entry['message'];
        },$output));
    }

}
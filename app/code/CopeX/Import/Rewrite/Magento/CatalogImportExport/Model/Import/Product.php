<?php
/**
 * Product
 * @copyright Copyright © 2019 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\Import\Rewrite\Magento\CatalogImportExport\Model\Import;

use Magento\ImportExport\Model\Import;

class Product extends \Magento\CatalogImportExport\Model\Import\Product
{

    /**
     * Gather and save information about product links.
     * Must be called after ALL products saving done.
     * @return \Magento\CatalogImportExport\Model\Import\Product
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    protected function _saveLinks()
    {
        $behavior = $this->getBehavior();
        if ($behavior == Import::BEHAVIOR_ADD_UPDATE) {
            $this->_parameters['behavior'] = Import::BEHAVIOR_APPEND;
            parent::_saveLinks();
            $this->_parameters['behavior'] = $behavior;
        } else {
            parent::_saveLinks();
        }
        return $this;
    }

    public function validateRow(array $rowData, $rowNum)
    {
        if (isset($this->_parameters['no_validation'])) {
            if (isset($this->_validatedRows[$rowNum])) {
                return true;
            }
            $this->_validatedRows[$rowNum] = true;
            $this->_processedEntitiesCount++;
            $sku = $rowData[self::COL_SKU];
            $this->skuProcessor->addNewSku(
                $sku,
                $this->prepareNewSkuData($sku, $rowData)
            );
        } else {
            $behavior = $this->getBehavior();
            if ($behavior == Import::BEHAVIOR_ADD_UPDATE) {
                $this->_parameters['behavior'] = Import::BEHAVIOR_APPEND;
                parent::validateRow($rowData, $rowNum);
                $this->_parameters['behavior'] = $behavior;
            } else {
                parent::validateRow($rowData, $rowNum);
            }
        }
        return $this;
    }

    private function prepareNewSkuData($sku, $rowData)
    {
        $data = [];
        $lowerSku = strtolower($sku);
        if(isset($this->_oldSku[$lowerSku])){
            foreach ($this->_oldSku[$lowerSku] as $key => $value) {
                $data[$key] = $value;
            }

            $data['attr_set_code'] = $this->_attrSetIdToName[$this->_oldSku[$lowerSku]['attr_set_id']];
        }
        else {
            $data = [
                'row_id' => null,
                'entity_id' => null,
                'type_id' => $rowData[self::COL_TYPE],
                'attr_set_id' => $this->_attrSetNameToId[$rowData[self::COL_ATTR_SET]],
                'attr_set_code' => $rowData[self::COL_ATTR_SET],
            ];
        }

        return $data;
    }

    /**
     * Get existing images for current bunch
     * @param array $bunch
     * @return array
     */
    protected function getExistingImages($bunch)
    {
        $this->refreshProductCache();
        return parent::getExistingImages($bunch);
    }

    /**
     * Function to prevent memory increase in import
     */
    protected function refreshProductCache()
    {
        $productRepositoryReflection = new \ReflectionProperty(
            '\Magento\CatalogImportExport\Model\Import\Product',
            'productRepository'
        );
        $productRepositoryReflection->setAccessible(true);
        $productRepositoryReflection->getValue($this)->cleanCache();
    }
}
<?php

namespace CopeX\Import\Console\Command;

use Magento\Framework\App\ObjectManagerFactory;
use Symfony\Component\Console\Input\InputOption;
use Magento\ImportExport\Model\Import;

class Line extends AbstractCommand
{

    protected function configure()
    {
        $this->setName('copex:import:line')
            ->setDescription('Import per line');
        $this->addOption('line', 'l', InputOption::VALUE_OPTIONAL, 'Line Number', '1');
        $this->setBehavior(Import::BEHAVIOR_ADD_UPDATE);
        parent::configure();
    }


    protected function process()
    {
        $this->importModel->initProcess();
        $this->exportAdapter->setOutput($this->outputInterface);
        $this->importModel->setExportAdapter($this->exportAdapter);
        $this->importModel->processMapping($this->inputInterface->getOption("line"));
        $this->exportAdapter->render();
        $this->importModel->processAfter();
    }
}

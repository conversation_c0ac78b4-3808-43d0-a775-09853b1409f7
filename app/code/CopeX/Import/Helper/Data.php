<?php

/**
 * CopeX_Import
 * NOTICE OF LICENSE
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 * @category    CopeX
 * @package     CopeX_Import
 * @copyright   Copyright © 2014 CopeX (http://copex.io/)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 * @ *
 */

namespace CopeX\Import\Helper;

use CopeX\Import\Model\Config\Data as ImportConfig;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{

    const XML_PATH_IMPORT_PROFILES = "import_profiles";
    protected $importConfig;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime $date
     */
    protected $date;
    /**
     * @var \Magento\Framework\App\Helper\Context
     */
    private $context;

    /**
     * Data constructor.
     * @param \Magento\Framework\App\Helper\Context       $context
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $date
     * @param ImportConfig                                $config
     */
    function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,

        ImportConfig $config
    ) {
        $this->importConfig = $config;
        $this->date = $date;
        parent::__construct($context);
        $this->context = $context;
    }

    public function getImportProfiles()
    {
        return $this->importConfig->get(self::XML_PATH_IMPORT_PROFILES);
    }

    /**
     * @param string $path Enter a %s to substitute with the current profile.
     * @param        $profileName
     * @return array
     */
    public function getConfigNode($path, $profileName)
    {
        $profile = $this->importConfig->get(self::XML_PATH_IMPORT_PROFILES . "/" . $profileName);
        return array_key_exists($path, $profile) ? $profile[$path] : [];
    }

    /**
     * Convert strings with underscores into CamelCase
     * @param string $string
     * @param bool   $first_char_caps
     * @return mixed
     */
    public function underscoreToCamelCase($string, $firstCharCaps = true)
    {
        if ($firstCharCaps === true) {
            $string[0] = strtoupper($string[0]);
        }
        $func = create_function('$c', 'return strtoupper($c[1]);');
        return preg_replace_callback('/_([a-z])/', $func, $string);
    }

    /**
     * @param $element
     * @param $attributeName
     * @return string
     */
    public function getAttribute($element, $attributeName)
    {
        $attributeValue = "";
        if (isset($element['@attributes'][$attributeName])) {
            $attributeValue = $element['@attributes'][$attributeName];
        }
        return $attributeValue;
    }

    /**
     * @param $element
     * @return array
     */
    public function getHelperParts($element)
    {
        $helperParts = [];
        $helperName = $this->getAttribute($element, 'helper');
        if ($helperName) {
            $helperParts = explode('::', $helperName);
        }
        return $helperParts;
    }

    /**
     * @param $element
     * @return array
     */
    public function getAttributes($element)
    {
        if (isset($element['@attributes'])) {
            return $element['@attributes'];
        }
        return [];
    }
}
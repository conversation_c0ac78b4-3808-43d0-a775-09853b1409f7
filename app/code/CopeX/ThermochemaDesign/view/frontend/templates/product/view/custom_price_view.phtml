<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>

<?php
/** @var \Magento\Catalog\Pricing\Render\FinalPriceBox $block */

$product = $block->getProduct();
$currencyFormatter = $this->helper('Magento\Framework\Pricing\Helper\Data');

/** @var $priceHelper \CopeX\Winline\Helper\PriceHelper */
$priceHelper = $this->helper('CopeX\Winline\Helper\PriceHelper');

$listPrice = $product->getListPrice();
$finalPrice = $product->getFinalPrice();
$roundedFinal = round($finalPrice, 2) . '';
$simplePrices = $product->getData('simple_prices');
if (!isset($simplePrices[$roundedFinal]) || $simplePrices[$roundedFinal] === false) :
    $discount = $priceHelper->calculatePercentage($product);

    $discount = str_replace("-", "", $discount);

    $listPrice = $currencyFormatter->currency($listPrice, true, false);
    $finalPrice = $currencyFormatter->currency($finalPrice, true, false);
    $printableListPrice = $listPrice . " " . __("per") . " " . $product->getColli();
    $discounts = [];
    if ($product->getData('discount1') < 0) {
        $discounts [] = number_format(str_replace("-", "", $product->getData('discount1'))) . " %";
    }
    if ($product->getData('discount2') < 0) {
        $discounts [] = number_format(str_replace("-", "", $product->getData('discount2'))) . " %";
    }
    if ($finalPrice != $listPrice) {
        $showCustomPrices = true;
    } else {
        $showCustomPrices = false;
    }
    ?>
    <?php if ($showCustomPrices): ?>
        <?php if ($product->getListPrice()) : ?>
            <div class="custom_price_product_view">
                <?= __("Listprice") . ": " . $printableListPrice ?>
            </div>
        <?php endif; ?>
        <?php if ($discounts): ?>
            <div class="custom_price_product_view">
                <?= __("Discount") . ": " . implode(__(' + '), $discounts) ?>
            </div>
        <?php endif; ?>

    <?php endif;
endif;
?>

<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <move element="minicart" destination="copex_authheader"/>
        <!--<referenceBlock name="product.info.addtocart" remove="true"/>-->
        <!--<referenceBlock name="category.product.addto" remove="true"/>-->
        <referenceBlock name="custom.price.view" remove="true"/>
        <!--<referenceBlock name='category.products.list'>-->
            <!--<action method='setTemplate'>-->
                <!--<argument name='template' xsi:type='string'>CopeX_ThermochemaDesign::catalog/product/list-without-cart.phtml</argument>-->
            <!--</action>-->
        <!--</referenceBlock>-->

    </body>
</page>
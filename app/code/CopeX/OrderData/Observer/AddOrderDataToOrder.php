<?php

namespace CopeX\OrderData\Observer;

use CopeX\OrderData\Model\Data\OrderData;

class AddOrderDataToOrder implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * transfer the order comment from the quote object to the order object during the
     * sales_model_service_quote_submit_before event
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /* @var $order \Magento\Sales\Model\Order */
        $order = $observer->getEvent()->getOrder();

        /** @var $quote \Magento\Quote\Model\Quote $quote */
        $quote = $observer->getEvent()->getQuote();

        foreach (OrderData::DATA_ATTRIBUTES as $attribute) {
            $order->setData($attribute, $quote->getData($attribute));
        }
    }

}

<?php

namespace CopeX\OrderData\Controller\Index;

use Magento\Checkout\Model\Session;
use \Magento\Sales\Api\OrderRepositoryInterface;
use \Magento\Sales\Api\Data\OrderInterface;
use \Magento\Sales\Model\OrderFactory;
use \Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\UrlFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;

use Magento\Framework\Registry;


class Index extends \Magento\Framework\App\Action\Action
{
    const UPLOADDIR = "/ordermedia/";

    protected $resultJsonFactory;

    /**
     * @var \Magento\Framework\Controller\Result\RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
    protected $messageManager;

    protected $repository;

    protected $directoryList;

    protected $session;


    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        ManagerInterface $messageManager,
        DirectoryList $directoryList,
        Session $session,
        \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory
    )
    {
        $this->messageManager = $messageManager;
        $this->directoryList = $directoryList;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->session = $session;
        return parent::__construct($context);
    }


    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Json $result */

        $quote = $this->getQuote();
        $files = $this->getRequest()->getFiles();
        if ($files) {
            if (sizeof($files)<=5) {
                $folderName = md5(time() . $quote->getCustomerLastname());
                $success = $this->createMediaFolder($folderName, $files);
                $result = $this->resultJsonFactory->create();
                if ($success) {
                    $result->setData(['success' => $success, 'name' => $folderName]);
                } else {
                    $result->setData(['error' => $success]);
                }
            }else{
                $result->setData(['error' => 'Only 5 items allowed.']);
            }
        } else {
            $result->setData(['error' => 'No files received.']);
        }
        return $result;
    }

    public function getQuote()
    {
        return $this->session->getQuote();
    }

    private function createMediaFolder($dirname, $media)
    {
        try {
            $varDir = $this->directoryList->getPath(DirectoryList::MEDIA) . self::UPLOADDIR . $dirname;
            if (isset($media)) {
                if (!is_dir($varDir)) {
                    mkdir($varDir, 0777, true);
                }
                foreach ($media as $item) {
                    $this->upload($item["tmp_name"], $varDir . "/" . $item["name"]);

                }
                return true;
            }

        } catch
        (\Exception $exception) {
            $this->messageManager->addErrorMessage(
                __("An error occurred")
            );
        }
        return false;
    }

    protected function upload($from, $to)
    {
        return copy($from, $to);
    }
}
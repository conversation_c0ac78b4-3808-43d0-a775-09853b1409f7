<?php

namespace CopeX\OrderData\Helper;

use \Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use ZipArchive;

class OrderM<PERSON><PERSON><PERSON>per extends AbstractHelper
{
    protected $directoryList;
    public $storeManager;

    const UPLOADDIR = "/ordermedia/";
    const MEDIA_CONTROLLER_URL = "uploadmedia/data/index/?media_path=";

    /**
     * Data constructor.
     * @param \Magento\Framework\Registry $registry
     * @param DirectoryList $directoryList
     */
    public function __construct(
        DirectoryList $directoryList,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    )
    {
        $this->directoryList = $directoryList;
        $this->storeManager=$storeManager;
    }

    public function getMediaZip($dirname)
    {
        if ($dirname) {
            $zipname = 'bestellanhang.zip';
            $zip = new ZipArchive();
            try {
                $varDir = $this->directoryList->getPath(DirectoryList::MEDIA) . self::UPLOADDIR . $dirname;
            } catch (FileSystemException $e) {
                return false;
            }
            $zip->open($zipname, (ZipArchive::CREATE | ZipArchive::OVERWRITE));
            $files = new RecursiveIteratorIterator (new RecursiveDirectoryIterator($varDir), RecursiveIteratorIterator::LEAVES_ONLY);
            foreach ($files as $name => $file) {
                $new_filename = substr($name, strrpos($name, '/') + 1);
                if (file_exists($file) && is_file($file)) {
                    $zip->addFile($file, $new_filename);
                }
            }
            $zip->close();
            return $zipname;
        }
        return false;
    }

    public function createUrlToMedia($mediaPath)
    {
        $baseUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_LINK);
        return $baseUrl . OrderMediaHelper::MEDIA_CONTROLLER_URL .$mediaPath;
    }
}
<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Hyva\Admin\Block\FrontendHyvaGrid">
        <arguments>
            <argument name="gridTemplate" xsi:type="string">Hyva_Admin::grid.phtml</argument>
        </arguments>
    </type>
    <type name="Hyva\Admin\Model\GridBlockRenderer">
        <arguments>
            <argument name="gridClass" xsi:type="string">Hyva\Admin\Block\FrontendHyvaGrid</argument>
        </arguments>
    </type>
    <type name="Hyva\Admin\Controller\Ajax\Paging">
        <plugin name="CopeX_HyvaAdminFrontend_Plugin_Hyva_Admin_Controller_Ajax_Paging" type="CopeX\HyvaAdminFrontend\Plugin\Hyva\Admin\Controller\Ajax\Paging" sortOrder="10" disabled="false"/>
    </type>
</config>

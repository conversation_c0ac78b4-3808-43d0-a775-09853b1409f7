<?php
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
?>
<script>
    window.addEventListener('alpine:init', () => {
        Alpine.data('initSmileAttribute', ( options) => ({
            options             : options,
            searchLabel         : '<?= __("Search (%s)") ?>',
            showMoreLabel       : '<?= __("Show more") ?>',
            showLessLabel       : '<?= __("Show less") ?>',
            noSearchResultLabel : '<?= __("No value matching the search <b>%s</b>.") ?>',
            expanded: false,

            /**
             * Component initialization
             */
            init: async function () {
                this.items = this.options.items.map(this.addItemId.bind(this));

                var lastSelectedIndex = Math.max.apply(null, (this.items.map(
                    function (v, k) {return v['is_selected'] ? k : 0;}))
                );
                this.maxSize = Math.max(this.options.maxSize, lastSelectedIndex + 1);

                this.initSearchPlaceholder();
                this.onShowLess();
                this.displaySearch = this.displayShowMore();
                this.displayedItems = this.getDisplayedItems();
            },

            /**
             * Init the place holder
             */
            initSearchPlaceholder: function () {
                var examples = this.items.slice(0, 2).map(function (item) {return item.label});

                if (this.items.length > 2) {
                    examples.push('...');
                }

                // Tip to make html chars interpretable by the browser when the value is get from JS.
                let placeholder = document.createElement('div');
                placeholder.innerHTML = this.searchLabel.replace('%s', examples.join(', '));

                this.searchPlaceholder = placeholder.textContent;
            },

            /**
             * Triggered when typing on the search input
             */
            onSearchChange: function (event) {
                var text = event.target.value;
                if (text.trim() === "") {
                    this.fulltextSearch = null;
                    this.onShowLess();
                } else {
                    this.fulltextSearch = text;
                    this.onShowMore();
                }

                return true;
            },

            /**
             * Triggered when leaving the search field.
             */
            onSearchFocusOut: function(event) {
                var text = event.target.value;
                if (text.trim() === "") {
                    this.fulltextSearch = null;
                }
            },

            /**
             * Retrieve additional Results
             *
             * @param callback
             */
            loadAdditionalItems: function (callback) {
                fetch(this.options.ajaxLoadUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                })
                .then((response) =>  response.json())
                .then((data) => {
                        this.items = data.map(this.addItemId.bind(this));
                        this.options.hasMoreItems = false;

                        if (callback) {
                            return callback();
                        }
                    }
                );
            },

            /**
             * Retrieve items to display
             *
             * @returns {*}
             */
            getDisplayedItems: function () {
                var items = this.items;

                if (this.expanded === false) {
                    items = this.items.slice(0, this.maxSize);
                }

                if (this.getFulltextSearch()) {
                    var searchTokens    = this.slugify(this.getFulltextSearch()).split('-');
                    var lastSearchToken = searchTokens.splice(-1, 1)[0];

                    items = items.filter(function(item) {
                        var isValidItem = true;
                        var itemTokens = this.slugify(item.label).split('-');
                        searchTokens.forEach(function(currentToken) {
                            if (itemTokens.indexOf(currentToken) === -1) {
                                isValidItem = false;
                            }
                        })
                        if (isValidItem && lastSearchToken) {
                            var ngrams = itemTokens.map(function(token) {return token.substring(0, lastSearchToken.length)});
                            isValidItem = ngrams.indexOf(lastSearchToken) !== -1;
                        }
                        return isValidItem;
                    }.bind(this))
                }
                this.result = items.length;
                return items;
            },

            /**
             * Does the search have a result
             */
            hasSearchResult: function () {
                return this.getDisplayedItems().length > 0
            },

            /**
             * Search result message
             */
            getSearchResultMessage : function() {
                return this.noSearchResultLabel.replace("%s", '"' + this.getFulltextSearch() + '"')
            },

            getFulltextSearch : function() {
                return this.fulltextSearch;
            },

            /**
             * Callback for the "Show more" button
             */
            onShowMore: function () {
                if (this.options.hasMoreItems) {
                    this.loadAdditionalItems(this.onShowMore.bind(this));
                } else {
                    this.expanded = true;
                }

                this.displayedItems = this.getDisplayedItems();
            },

            /**
             * Index the text to be searched.
             */
            slugify: function(text) {
                return text.toString().toLowerCase()
                    .replace(/\s+/g, '-')                                              // Replace spaces with -
                    .replace(/[^\w\u0400-\u052F\u2DE0-\u2DFF\uA640-\uA69F'\-]+/g, '')  // Remove all non-word chars
                    .replace(/\-\-+/g, '-')                                            // Replace multiple - with single -
                    .replace(/^-+/, '')                                                // Trim - from start of text
            },

            /**
             * Callback for the "Show less" button
             */
            onShowLess: function () {
                this.expanded = false;
                this.displayedItems = this.getDisplayedItems();
            },

            /**
             * Check if the filter can be expanded
             *
             * @returns {boolean}
             */
            enableExpansion : function () {
                return this.options.hasMoreItems || this.items.length > this.maxSize;
            },

            /**
             * Displays the "Show More" link
             *
             * @returns {*|boolean}
             */
            displayShowMore: function () {
                return this.enableExpansion() && this.expanded === false && !this.getFulltextSearch();
            },

            /**
             * Displays the "Show Less" link
             *
             * @returns {*|boolean}
             */
            displayShowLess: function () {
                return this.enableExpansion() && this.expanded === true && !this.getFulltextSearch();
            },

            /**
             * Add an id to items.
             */
            addItemId: function (item) {
                item.id = Math.floor(Math.random() * 1000000) + "_option_";
                item.displayProductCount = this.options.displayProductCount && (item.count >= 1);

                return item;
            },
        }));
    });
</script>

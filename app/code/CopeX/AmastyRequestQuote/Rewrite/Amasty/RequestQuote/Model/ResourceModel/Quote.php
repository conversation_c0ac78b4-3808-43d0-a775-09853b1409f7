<?php
declare(strict_types=1);

namespace CopeX\AmastyRequestQuote\Rewrite\Amasty\RequestQuote\Model\ResourceModel;

use CopeX\AmastyRequestQuote\Helper\Data;
use Magento\Framework\DB\Select;
use Magento\Framework\Exception\LocalizedException;

class Quote extends \Amasty\RequestQuote\Model\ResourceModel\Quote
{
    /**
     * Overwrite method to access additionally added custom fields
     * recipient_address, additional_field, note, free_text, transportation_fee
     *
     * @inheritdoc
     * @throws LocalizedException
     */
    protected function _getLoadSelect($field, $value, $object)
    {
        $select = parent::_getLoadSelect($field, $value, $object);
        $this->joinAmastyExtendQuote($select);

        return $select;
    }

    /**
     *
     * @param \Zend_Db_Select $select
     * @throws LocalizedException
     */
    private function joinAmastyExtendQuote($select)
    {
        $select->joinInner(
            ['amquote_extend' => $this->getAmastyQuoteTable()],
            "amquote_extend.quote_id = " . $this->getMainTable() . ".entity_id",
            [
                Data::FIELD_RECIPIENT_ADDRESS,
                Data::FIELD_ADDITIONAL_FIELD,
                Data::FIELD_NOTE,
                Data::FIELD_FREE_TEXT,
                Data::FIELD_TRANSPORTATION_FEE
            ]
        )
            ->order('updated_at ' . Select::SQL_DESC)
            ->limit(1);
    }
}

<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Amasty\RequestQuote\Model\Source\PdfVariables">
        <plugin name="CopeX_AmastyRequestQuote_Plugin_Amasty_RequestQuote_Model_Source_PdfVariables"
                type="CopeX\AmastyRequestQuote\Plugin\Amasty\RequestQuote\Model\Source\PdfVariables" sortOrder="10"
                disabled="false"/>
    </type>
    <type name="Amasty\RequestQuote\Model\Pdf\PdfInformation">
        <plugin name="CopeX_AmastyRequestQuote_Plugin_Amasty_RequestQuote_Model_Pdf_PdfInformation"
                type="CopeX\AmastyRequestQuote\Plugin\Amasty\RequestQuote\Model\Pdf\PdfInformation" sortOrder="10"
                disabled="false"/>
    </type>
    <type name="Amasty\RequestQuote\Controller\Move\InCart">
        <plugin name="CopeX_AmastyRequestQuote_Plugin_Amasty_RequestQuote_Controller_Move_InCartPlugin"
                type="CopeX\AmastyRequestQuote\Plugin\Amasty\RequestQuote\Controller\Move\InCartPlugin" sortOrder="10"
                disabled="false"/>
    </type>
    <preference for="Amasty\RequestQuote\Model\ResourceModel\Quote"
                type="CopeX\AmastyRequestQuote\Rewrite\Amasty\RequestQuote\Model\ResourceModel\Quote"/>
</config>

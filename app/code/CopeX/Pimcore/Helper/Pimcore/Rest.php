<?php
/**
 * Rest
 * @copyright Copyright © 2020 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\Pimcore\Helper\Pimcore;

use CopeX\Import\Helper\Import;
use CopeX\Import\Model\Mapper;
use CopeX\Pimcore\Helper\Data;
use CopeX\Pimcore\Model\Pimcore\TypeInterface;
use CopeX\Pimcore\Model\Pimcore\Types;
use Magento\Catalog\Model\ResourceModel\Product;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\StoreManagerInterface;

class Rest extends Data
{

    /**
     * @var Types
     */
    protected $types;

    public function __construct(
        Context $context,
        Mapper $mapper,
        Import $importHelper,
        CollectionFactory $productCollectionFactory,
        Product $productResource,
        StoreManagerInterface $storeManager,
        Types $types
    ) {
        parent::__construct($context, $mapper, $importHelper, $productCollectionFactory, $productResource,
            $storeManager);
        $this->types = $types;
    }

    public function parsePimcoreProduct($transport)
    {
        $this->parseJson($transport);
        $itemData = current($transport->getItems());
        if (!$this->isDataValid($itemData)) {
            $transport->setData('skip', true);
            $this->importHelper->getLogHelper()
                ->log(
                    __("INVALID PRODUCT: Skipping product with pimcore ID: {$itemData[self::PIMCORE_ID]}"),
                    \Zend\Log\Logger::WARN
                );
        }
    }

    public function parseJson($transport)
    {
        $item = current($transport->getItems());
        $newValues = $item;
        unset($newValues[TypeInterface::PIMCORE_ELEMENTS]);
        unset($newValues[TypeInterface::ID]);
        $newValues [self::PIMCORE_ID] = $item[TypeInterface::ID];
        $newValues += $this->types->handleElements($item);
        $transport->setItems([$newValues]);
    }

}
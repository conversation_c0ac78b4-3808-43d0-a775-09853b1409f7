<?php /** @noinspection ALL */

namespace CopeX\Pimcore\Model\Downloader;

use CopeX\Pimcore\Model\Cache\Type as CacheType;

class Http extends \CopeX\Import\Model\Downloader\Http
{
    const SUCCESS = 'success';
    const DATA = 'data';
    const ID = 'id';
    const CONDITION = 'condition';
    const NAME = 'name';
    const VALUE = 'value';
    const ELEMENTS = 'elements';
    const AT = '@';

    /**
     * @var \CopeX\Pimcore\Helper\Pimcore
     */
    protected $pimcoreHelper;
    protected $directoryList;
    protected $objectFactory;

    /** @var \Magento\Framework\App\CacheInterface */
    protected $cache;

    public function __construct(
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList,
        \CopeX\Import\Helper\Log $logger,
        \CopeX\Pimcore\Helper\Pimcore $pimcoreHelper,
        \Magento\Framework\DataObjectFactory $objectFactory,
        \Magento\Framework\App\CacheInterface $cache
    ) {
        parent::__construct($directoryList, $logger);
        $this->pimcoreHelper = $pimcoreHelper;
        $this->objectFactory = $objectFactory;
        $this->cache = $cache;
    }

    public function download(\Magento\Framework\DataObject $connectionInfo, $target = "")
    {
        $cacheKey = $this->getCacheKey($connectionInfo);
        if (!$downloadResult = $this->cache->load($cacheKey)) {
            $downloadResult = $this->_download($connectionInfo);
            $downloadResult = json_decode($downloadResult, true);
            if ($downloadResult && $downloadResult[self::SUCCESS] == "true") {
                $objects = $this->transferToIDArray($downloadResult);
                $this->processDownloader($objects, $connectionInfo);
                $downloadResult = $objects;
            }
            $returnResult = json_encode($downloadResult);
            if ($downloadResult && $connectionInfo->getCache()) {
                $this->cache->save($returnResult, $cacheKey, [CacheType::CACHE_TAG], $connectionInfo->getCache());
            }
            if ($target) {
                $fileName = $this->pimcoreHelper->getBaseDir() . DIRECTORY_SEPARATOR . $target;
                file_put_contents($fileName, $returnResult);
            }
        } else {
            $downloadResult = json_decode($downloadResult, true);
        }
        return $downloadResult;
    }

    protected function processDownloader(&$objects, $connectionInfo)
    {
        if ($connectionInfo->hasDownloaders()) {
            foreach ($objects as $objectId => $object) {
                foreach ($connectionInfo->getDownloaders() as $downloader) {
                    $downloaderObject = $this->transferToObject($downloader);
                    $condition = $downloaderObject->getAttribute(self::CONDITION);
                    if ($condition && !$this->hasConditionElement($condition, $objects[$objectId])) {
                        continue;
                    }
                    $this->setCommand($downloaderObject, $object);
                    $objectInfo = $this->_download($downloaderObject);
                    $objectInfoArray = json_decode($objectInfo, true);
                    if ($objectInfoArray && $objectInfoArray[self::SUCCESS] == "true") {
                        $objects[$objectId] += $objectInfoArray[self::DATA];
                    }
                }
            }
        }
    }

    protected function hasConditionElement($condition, $object)
    {
        foreach ($object[self::ELEMENTS] as $element) {
            if ($element[self::NAME] == $condition && $element[self::VALUE]) {
                return true;
            }
        }
        return false;
    }

    protected function setCommand($parameters, $replacements = [])
    {
        if ($parameters->hasCommandReplace()) {
            $commandReplace = $parameters->getCommandReplace();
            foreach ($commandReplace as $replaceKey => $replaceValue) {
                $commandReplace[$replaceKey] = $replacements[$replaceValue] ? $replacements[$replaceValue]
                    : $replaceValue;
            }
            $parameters->setCommandReplace($commandReplace);
            $parameters->setCommand(sprintf($parameters->getCommand(), ...array_values($commandReplace)));
        }
    }

    protected function transferToObject($array)
    {
        $object = $this->objectFactory->create();
        $object->setData($array);
        $object->setAttribute($object->getData(self::AT));
        $object->unsetData(self::AT);
        return $object;
    }

    protected function transferToIDArray($array)
    {
        $objects = [];
        if (isset($array[self::DATA][self::ID])) {
            $objects[$array[self::DATA][self::ID]] = $array[self::DATA];
        } else {
            foreach ($array[self::DATA] as $object) {
                $objects[$object[self::ID]] = $object;
            }
        }
        return $objects;
    }

    protected function setUrl($connectionInfo)
    {
        $url = $this->getApiUrl($connectionInfo);
        $connectionInfo->setUrl($url);
    }

    /**
     * Copy of Ho_Import_Model_Downloader_Http::download + Timeout Setting
     * @param      $connectionInfo
     * @param bool $cache
     * @return string file_content
     * @throws \Exception
     */
    protected function _download($connectionInfo)
    {
        $command = $connectionInfo->getCommand();
        if (!$command) {
            throw new \Exception(__("No valid command given: %1", $connectionInfo->getCommand()));
        }

        $this->logHelper->log(
            __("Downloading command %1", $command),
            \Zend\Log\Logger::DEBUG
        );
        $content = $this->pimcoreHelper->callPimcore(
            $connectionInfo->getCommand(),
            $connectionInfo->getParameters(),
            $connectionInfo->getData('store'),
            $connectionInfo->getApiPath()
        );

        return $content;
    }

    public function getCacheKey($connectionInfo)
    {
        return $connectionInfo->getCommand() . "_" . implode("_", $connectionInfo->getParameters() ?? []);
    }
}

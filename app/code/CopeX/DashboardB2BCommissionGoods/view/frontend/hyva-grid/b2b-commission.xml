<?xml version="1.0"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Hyva_Admin:etc/hyva-grid.xsd">
    <source>
        <collection>CopeX\DashboardB2BCommissionGoods\Model\ResourceModel\B2BCommissionGoods\Collection</collection>
        <defaultSearchCriteriaBindings>
            <field name="KundeNr" method="Magento\Customer\Model\Session::getCustomer" property="number" />
        </defaultSearchCriteriaBindings>
    </source>
    <columns rowAction="view">
        <include>
            <column name="kunde_nr" label="Kunde" initiallyHidden="true"/>
            <column name="artikel_nr" label="Artikel"/>
            <column name="artikel_bez" label="Name"/>
<!--            <column name="charge" label="Charge"/>-->
<!--            <column name="charge_info" label="Info"/>-->
            <column name="artikel_preis" label="Preis" type="price"/>
            <column name="artikel_menge" label="Menge"/>
            <column name="artikel_einheit" label="Einheit"/>
            <column name="dt_zugang" label="Zugang" />
            <column name="dt_abgang" label="Abgang"/>
            <column name="leih_typ" label="Typ"/>
            <column name="n_status" label="Status" initiallyHidden="true"/>
        </include>
    </columns>
    <entityConfig>
        <label>
            <singular>Commission Goods</singular>
            <plural>Commission Goods</plural>
        </label>
    </entityConfig>
    <navigation>
        <pager>
            <defaultPageSize>30</defaultPageSize>
            <pageSizes>30, 60, 120</pageSizes>
        </pager>
        <sorting>
            <defaultSortByColumn>AuftragDatum</defaultSortByColumn>
            <defaultSortDirection>desc</defaultSortDirection>
        </sorting>
    </navigation>
</grid>

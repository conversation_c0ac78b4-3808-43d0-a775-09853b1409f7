<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\DashboardB2BCommissionGoods\Model;

use CopeX\DashboardB2BCommissionGoods\Api\Data\B2BCommissionGoodsInterface;
use Magento\Framework\Model\AbstractModel;

class B2BCommissionGoods extends AbstractModel implements B2BCommissionGoodsInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\CopeX\DashboardB2BCommissionGoods\Model\ResourceModel\B2BCommissionGoods::class);
    }



    /**
     * @inheritDoc
     */
    public function getKundeNr()
    {
        return $this->getData(self::KUNDENR);
    }

    /**
     * @inheritDoc
     */
    public function setKundeNr($kundeNr)
    {
        return $this->setData(self::KUNDENR, $kundeNr);
    }

    /**
     * @inheritDoc
     */
    public function getArtikelNr()
    {
        return $this->getData(self::ARTIKELNR);
    }

    /**
     * @inheritDoc
     */
    public function setArtikelNr($artikelNr)
    {
        return $this->setData(self::ARTIKELNR, $artikelNr);
    }

    /**
     * @inheritDoc
     */
    public function getCharge()
    {
        return $this->getData(self::CHARGE);
    }

    /**
     * @inheritDoc
     */
    public function setCharge($charge)
    {
        return $this->setData(self::CHARGE, $charge);
    }

    /**
     * @inheritDoc
     */
    public function getKundeName()
    {
        return $this->getData(self::KUNDENAME);
    }

    /**
     * @inheritDoc
     */
    public function setKundeName($kundeName)
    {
        return $this->setData(self::KUNDENAME, $kundeName);
    }

    /**
     * @inheritDoc
     */
    public function getLeihTyp()
    {
        return $this->getData(self::LEIH_TYP);
    }

    /**
     * @inheritDoc
     */
    public function setLeihTyp($leihTyp)
    {
        return $this->setData(self::LEIH_TYP, $leihTyp);
    }

    /**
     * @inheritDoc
     */
    public function getArtikelBez()
    {
        return $this->getData(self::ARTIKELBEZ);
    }

    /**
     * @inheritDoc
     */
    public function setArtikelBez($artikelBez)
    {
        return $this->setData(self::ARTIKELBEZ, $artikelBez);
    }

    /**
     * @inheritDoc
     */
    public function getChargeInfo()
    {
        return $this->getData(self::CHARGEINFO);
    }

    /**
     * @inheritDoc
     */
    public function setChargeInfo($chargeInfo)
    {
        return $this->setData(self::CHARGEINFO, $chargeInfo);
    }

    /**
     * @inheritDoc
     */
    public function getArtikelPreis()
    {
        return $this->getData(self::ARTIKELPREIS);
    }

    /**
     * @inheritDoc
     */
    public function setArtikelPreis($artikelPreis)
    {
        return $this->setData(self::ARTIKELPREIS, $artikelPreis);
    }

    /**
     * @inheritDoc
     */
    public function getArtikelMenge()
    {
        return $this->getData(self::ARTIKELMENGE);
    }

    /**
     * @inheritDoc
     */
    public function setArtikelMenge($artikelMenge)
    {
        return $this->setData(self::ARTIKELMENGE, $artikelMenge);
    }

    /**
     * @inheritDoc
     */
    public function getArtikelEinheit()
    {
        return $this->getData(self::ARTIKELEINHEIT);
    }

    /**
     * @inheritDoc
     */
    public function setArtikelEinheit($artikelEinheit)
    {
        return $this->setData(self::ARTIKELEINHEIT, $artikelEinheit);
    }

    /**
     * @inheritDoc
     */
    public function getDtZugang()
    {
        return $this->getData(self::DTZUGANG);
    }

    /**
     * @inheritDoc
     */
    public function setDtZugang($dtZugang)
    {
        return $this->setData(self::DTZUGANG, $dtZugang);
    }

    /**
     * @inheritDoc
     */
    public function getDtAbgang()
    {
        return $this->getData(self::DTABGANG);
    }

    /**
     * @inheritDoc
     */
    public function setDtAbgang($dtAbgang)
    {
        return $this->setData(self::DTABGANG, $dtAbgang);
    }

    /**
     * @inheritDoc
     */
    public function getDtImport()
    {
        return $this->getData(self::DTIMPORT);
    }

    /**
     * @inheritDoc
     */
    public function setDtImport($dtImport)
    {
        return $this->setData(self::DTIMPORT, $dtImport);
    }

    /**
     * @inheritDoc
     */
    public function getDtImportFirst()
    {
        return $this->getData(self::DTIMPORTFIRST);
    }

    /**
     * @inheritDoc
     */
    public function setDtImportFirst($dtImportFirst)
    {
        return $this->setData(self::DTIMPORTFIRST, $dtImportFirst);
    }

    /**
     * @inheritDoc
     */
    public function getNStatus()
    {
        return $this->getData(self::NSTATUS);
    }

    /**
     * @inheritDoc
     */
    public function setNStatus($nStatus)
    {
        return $this->setData(self::NSTATUS, $nStatus);
    }
}


<?php

namespace CopeX\ElasticSuiteRelatedParentProducts\Model\Product\Indexer\Fulltext\Datasource;

use Smile\ElasticsuiteCore\Api\Index\DatasourceInterface;
use CopeX\ElasticSuiteRelatedParentProducts\Model\ResourceModel\Product\Search\ParentProducts as Resource;

class ParentProducts implements DatasourceInterface
{

    private Resource $resourceModel;

    public function __construct( Resource $resourceModel)
    {
        $this->resourceModel = $resourceModel;
    }

    public function addData($storeId, array $indexData)
    {
        $productIds = array_keys($indexData);
        $linkedProducts = $this->resourceModel->getByProductIds($productIds, $storeId);
        foreach($linkedProducts as $product){
            $indexData[(int) $product['linked_product_id']]['related'][] = array_filter($product);
        }

        return $indexData;
    }
}
<?php

namespace CopeX\AssignStaff\Helper;

use MageB2B\Staff\Model\ResourceModel\Staff\CollectionFactory;

class Staff
{

    protected $staffCache;
    /**
     * @var CollectionFactory
     */
    private $collectionFactory;
    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $resourceConnection;

    public function __construct(
        CollectionFactory $collectionFactory,
        \Magento\Framework\App\ResourceConnection $resourceConnection
    ) {
        $this->collectionFactory = $collectionFactory;
        $this->resourceConnection = $resourceConnection;
    }

    public function assignStaffIds($staffId, $customerIds = [])
    {
        $connection = $this->resourceConnection->getConnection();
        $tableCustomerSalesStaffCustomer = $this->resourceConnection->getTableName('customer_salesstaff_customer'); //gives table name with prefix
        if (!is_array($customerIds)) {
            $customerIds = [$customerIds];
        }
        $insert = [];
        $staffEntityId = $this->getStaffEntityId($staffId);
        if ($staffEntityId) {
            foreach ($customerIds as $customerId) {
                $insert[] = $customerId . ',' . $staffEntityId;
            }
        }

        if ($customerIds) {
            $connection->query("DELETE FROM " . $tableCustomerSalesStaffCustomer . " WHERE customer_id in (" .
                               implode(',', $customerIds) . ")");
            if ($insert) {
                $query = 'INSERT INTO ' . $tableCustomerSalesStaffCustomer . ' (customer_id, staff_id) VALUES (' .
                         implode('),(', $insert) . ') ON DUPLICATE KEY UPDATE
                customer_id=VALUES(customer_id),
                staff_id=VALUES(staff_id)';
                $connection->query($query);
            }
        }
    }

    public function getStaffEntityId($staffId)
    {
        if (!$this->staffCache) {
            $staffCollection = $this->collectionFactory->create();
            foreach ($staffCollection as $staffMember) {
                $this->staffCache[$staffMember->getOwnStaffId()] = $staffMember->getId();
            }
        }
        return isset($this->staffCache[$staffId]) ? $this->staffCache[$staffId] : null;
    }
}

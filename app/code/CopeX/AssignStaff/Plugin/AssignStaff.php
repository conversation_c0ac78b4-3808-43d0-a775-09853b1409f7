<?php

namespace CopeX\AssignStaff\Plugin;

use CopeX\AssignStaff\Helper\Staff;

class AssignStaff
{

    /**
     * @var Staff
     */
    private $staff;

    public function __construct(Staff $staff)
    {
        $this->staff = $staff;
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $subject
     * @param                                        $result
     */
    public function afterSave(\Magento\Framework\Model\AbstractModel $subject, $result)
    {
        $this->staff->assignStaffIds($subject->getSalesRep(), $subject->getId());
        return $result;
    }
}

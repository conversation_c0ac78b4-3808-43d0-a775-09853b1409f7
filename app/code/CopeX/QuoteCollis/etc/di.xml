<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Quote\Model\Quote">
        <plugin name="collis_add_to_quote_item" type="CopeX\QuoteCollis\Plugin\Quote" />
    </type>
    <type name="Magento\Quote\Model\Quote\Item">
        <plugin name="combine_parcel_unit_options_possibility" type="CopeX\QuoteCollis\Plugin\QuoteItem" />
    </type>
    <type name="Magento\Sales\Model\Order\Item">
        <plugin name="format_parcel_unit_label" type="CopeX\QuoteCollis\Plugin\OrderItem" />
    </type>

    <type name="Magento\Checkout\CustomerData\AbstractItem">
        <plugin name="AddAttPlug" type="CopeX\QuoteCollis\Plugin\DefaultItem" disabled="false" sortOrder="10"/>
    </type>

    <type name="Magento\Catalog\Helper\Product\Configuration">
        <plugin name="format_parcel_unit_label" type="CopeX\QuoteCollis\Plugin\CartItemRenderer" disabled="false" sortOrder="10"/>
    </type>
    <type name="Magento\Catalog\Model\Product\Option">
        <arguments>
            <argument name="optionGroups" xsi:type="array">
                <item name="parcel_unit" xsi:type="string">CopeX\QuoteCollis\Model\Product\Option\Type\ParcelUnit</item>
            </argument>
            <argument name="optionTypesToGroups" xsi:type="array">
               <item name="parcel_unit" xsi:type="const">CopeX\QuoteCollis\Model\Product\Option\Type\ParcelUnit::PARCEL_UNIT</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Catalog\Block\Product\View">
        <plugin name="dont_show_qty" type="CopeX\QuoteCollis\Plugin\ProductView" disabled="false" sortOrder="10"/>
    </type>
    <preference for="Magento\Catalog\Model\Product\Option\Type\Parcel_Unit" type="CopeX\QuoteCollis\Model\Product\Option\Type\ParcelUnit" />
</config>

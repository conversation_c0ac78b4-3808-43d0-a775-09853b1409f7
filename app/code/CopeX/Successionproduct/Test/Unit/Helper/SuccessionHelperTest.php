<?php

namespace CopeX\Successionproduct\Test\Unit\Helper;


use CopeX\Successionproduct\Helper\SuccessionHelper;
use Magento\CatalogUrlRewrite\Model\ResourceModel\Category\ProductCollection;

class SuccessionHelperTest extends \PHPUnit_Framework_TestCase
{

    protected $helper, $productMock, $productMock2, $collectionMock, $collectionFactory, $context, $productMock3;

    public function setUp()
    {
        $objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);

        //region products
        $this->productMock = $this->getMockBuilder(\Magento\Catalog\Model\Product::class)
            ->disableOriginalConstructor()
            ->setMethods(["getSuccessionSku", "getSku", "getProductUrl"])
            ->getMock();

        $this->productMock2 = $this->getMockBuilder(\Magento\Catalog\Model\Product::class)
            ->disableOriginalConstructor()
            ->setMethods(["getSuccessionSku", "getSku", "getProductUrl"])
            ->getMock();

        $this->productMock3 = $this->getMockBuilder(\Magento\Catalog\Model\Product::class)
            ->disableOriginalConstructor()
            ->setMethods(["getSuccessionSku", "getSku", "getProductUrl"])
            ->getMock();
        //endregion

        $this->context = $this->getMockBuilder('Magento\Framework\App\Helper\Context')
            ->disableOriginalConstructor()
            ->getMock();

        $this->collectionFactory = $this->getMock(\Magento\Catalog\Model\ResourceModel\Product\CollectionFactory::class, $methods = ["create"],
            [],
            '',
            false);

        $this->collectionMock = $this->getMockBuilder('Magento\Catalog\Model\ResourceModel\Product\Collection')
            ->setMethods(['addAttributeToFilter', 'addAttributeToSelect',
                          'addUrlRewrite', 'getFirstItem'])->disableOriginalConstructor()->getMock();

        $this->collectionFactory->expects($this->any())->method('create')->willReturn($this->collectionMock);


        $this->helper = new SuccessionHelper(
            $this->context,
            $this->collectionFactory
        );
    }

    public function testProductHasSuccession()
    {
        $this->productMock->method('getSuccessionSku')->willReturn("sku123");

        $this->productMock2->method('getProductUrl')->willReturn('test-url');
        $this->productMock2->method("getStatus")->willReturn("2");

        $this->collectionMock
            ->method('getFirstItem')
            ->willReturn($this->productMock2);


        $helperReturnValue = $this->helper->getSuccessionUrl($this->productMock);
        $this->assertEquals($helperReturnValue, "test-url");
    }

    public function testProductHasNoSuccession()
    {
        $this->productMock->method('getSuccessionSku')->willReturn(null);

        $helperReturnValue = $this->helper->getSuccessionUrl($this->productMock);
        $this->assertEquals($helperReturnValue, null);
    }


    public function testProductHasTwoSuccessions()
    {
        $this->productMock->method('getSuccessionSku')->willReturn("sku123");

        $this->productMock2->method('getProductUrl')->willReturn("hallo-url");
        $this->productMock2->method('getSuccessionSku')->willReturn("sku1234");
        $this->productMock2->method("getStatus")->willReturn("2");

        $this->productMock3->method("getStatus")->willReturn("1");
        $this->productMock3->method('getProductUrl')->willReturn("two-url");

        $this->collectionMock
            ->method('getFirstItem')
            ->willReturnCallback([$this, "getFirstItemAlternative"]);


        $helperReturnValue = $this->helper->getSuccessionUrl($this->productMock);
        $this->assertEquals($helperReturnValue, "two-url");
    }

    static $counter = 0;

    public function getFirstItemAlternative()
    {
        if (self::$counter == 0) {
            self::$counter++;
            return $this->productMock2;
        } else {
            return $this->productMock3;
        }
    }

}
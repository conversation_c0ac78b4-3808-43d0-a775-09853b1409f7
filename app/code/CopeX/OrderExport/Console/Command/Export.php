<?php


namespace CopeX\OrderExport\Console\Command;

use Magento\Framework\App\State;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class Export extends Command
{

    const NAME_ARGUMENT = "name";
    const NAME_OPTION = "option";
    private $export;
    private $state;

    /**
     * Export constructor.
     * @param \CopeX\OrderExport\Model\Export $export
     * @param State    $state
     */
    public function __construct(
        \CopeX\OrderExport\Model\Export $export,
        State $state
    )
    {
        $this->export = $export;
        $this->state = $state;
        parent::__construct();

    }


    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        $this->export->export();
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("copex:orderexport");
        $this->setDescription("Export orders for winline");
        parent::configure();
    }
}

.exit-intent-popup {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 10002;
    background: rgba(33, 33, 33, 0.8);
    transform: translateY(60%) scale(0);
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    justify-content: center;
    align-items: center;
}

.exit-intent-popup.visible {
    transform: translateY(0) scale(1);
}

.exit-intent-popup .content {
    position: absolute;
    background: #FFF;
    border-radius: 3px;
    max-height: 100%;
    overflow-y: auto;
}


.exit-intent-popup .content > .close {
    position: absolute;
    right: 24px;
    top: 10px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    z-index: 3;
    text-shadow: none;
    opacity: 1;
}

.exit-intent-popup .content > .close:before, .exit-intent-popup .content > .close:after {
    position: absolute;
    left: 15px;
    content: ' ';
    height: 24px;
    width: 2px;
    background-color: #fff;
}
.exit-intent-popup .content > .close:before {
    transform: rotate(45deg);
}
.exit-intent-popup .content > .close:after {
    transform: rotate(-45deg);
}

.exit-intent-popup .overflow-hidden {
    overflow: hidden;
}

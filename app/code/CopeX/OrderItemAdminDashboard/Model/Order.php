<?php

namespace CopeX\OrderItemAdminDashboard\Model;

class Order extends \Magento\Sales\Model\Order
{
    private $expiryDate;
    private $eventStartDate;

    /**
     * @return mixed
     */
    public function getExpiryDate()
    {
        return $this->expiryDate;
    }

    /**
     * @param mixed $expiryDate
     */
    public function setExpiryDate($expiryDate): void
    {
        $this->expiryDate = $expiryDate;
    }

    /**
     * @return mixed
     */
    public function getEventStartDate()
    {
        return $this->eventStartDate;
    }

    /**
     * @param mixed $eventStartDate
     */
    public function setEventStartDate($eventStartDate): void
    {
        $this->eventStartDate = $eventStartDate;
    }
}
<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_dashboard_address">
            <arguments>
                <argument name="view_helper" xsi:type="object">CopeX\AddressLogic\Model\Config</argument>
            </arguments>
            <action method="setTemplate">
                <argument name="template" xsi:type="string">CopeX_AddressLogic::account/dashboard/address.phtml</argument>
            </action>
        </referenceBlock>
    </body>
</page>

<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;

/** @var $block MagePal\GoogleTagManager\Block\DataLayer **/
/** @var ViewModelRegistry $viewModels */

$storeConfig = $viewModels->require(\Hyva\Theme\ViewModel\StoreConfig::class);
$dataLayerName = $block->getDataLayerName();
$accountId = $block->getAccountId();
$containerCode = $block->getEmbeddedCode();
$scriptId = md5($block->getNameInLayout());
$partyTown = (bool) $storeConfig->getStoreConfig('perspective/settings/status');
$gtmDebug = $block->getRequest()->getParam('gtm_debug');
?>
<?php if ($partyTown) : ?>
    <script type="text/partytown">
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','<?= $dataLayerName ?>','<?= $accountId ?>');
    </script>
    <?php if($gtmDebug) : ?>
    <script type="text/javascript">
        const gtmDebugLog = (msg, data) => {
            console.debug(
                `%cGTM Main%c${msg}`,
                `background: #c47ed1; color: white; padding: 2px 3px; border-radius: 2px; font-size: 0.8em;margin-right:5px`,
                `background: #999999; color: white; padding: 2px 3px; border-radius: 2px; font-size: 0.8em;`,
                data && data.length ? Array.from(data) : data
            )
        }

        __partytown_gtm_debug = {
            // Used to populate dataLayer back into main thread
            dataLayerPush: function() {
                [].push.apply(dataLayer, arguments)
            },
            // Called when receiver has been set inside partytown, calls __TAG_ASSISTANT_API.setReceiver
            activate: function () {
                gtmDebugLog('activate')
                window.__TAG_ASSISTANT_API.setReceiver(function() {
                    gtmDebugLog('send data', arguments)
                    window.__tag_assistant_forwarder.apply(null, arguments);
                })
            },
            // Forwards calls from bootstrap
            sendMessage: function() {
                gtmDebugLog('send message', arguments)
                window.__TAG_ASSISTANT_API.sendMessage.apply(window.__TAG_ASSISTANT_API, arguments)
            },
            // Forwards calls from bootstrap
            disconnect: function() {
                gtmDebugLog('disconnect', arguments)
                window.__TAG_ASSISTANT_API.disconnect.apply(window.__TAG_ASSISTANT_API, arguments)
            },
        }
    </script>
    <?php endif; ?>
<?php else: ?>
<!-- Google Tag Manager by MagePal -->
<?php if ($block->isAdvancedSettingsEnabled()): ?>
    <script type="text/javascript">
        window.<?= /* @noEscape */ $dataLayerName ?> = window.<?= /* @noEscape */ $dataLayerName ?> || [];
        <?= /* @noEscape */ $block->getDataLayerJs() ?>
    </script>
    <?= /* @noEscape */ $block->getAdvancedSettingsJsCode() ?>
<?php elseif (!$block->isGdprEnabled() && $block->addJsInHead() && !$block->isAdvancedSettingsEnabled()): ?>
    <?php if ($partyTown): ?>
    <script type="text/partytown">
    <?php else: ?>
    <script type="text/javascript">
    <?php endif; ?>
        window.<?= /* @noEscape */ $dataLayerName ?> = window.<?= /* @noEscape */ $dataLayerName ?> || [];
        <?= /* @noEscape */ $block->getDataLayerJs() ?>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='<?= /* @noEscape */ $dataLayerName ?>'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl<?= $containerCode ? "+'{$containerCode}'" : '' ?>;f.parentNode.insertBefore(j,f);
        })(window,document,'script','<?= /* @noEscape */ $dataLayerName ?>','<?= /* @noEscape */ $accountId ?>');
    </script>
<?php endif; ?>

<?php if ($partyTown): ?>
<script type="text/partytown">
<?php else: ?>
<script type="text/javascript">
<?php endif; ?>
    function initMagepalGtmDatalayer_<?= $scriptId ?>() {
        'use strict';
        <?php if ($partyTown): ?>
        console.debug("[INIT] GTM in partytown");
        <?php else: ?>
        console.debug("[INIT] GTM in main thread");
        <?php endif; ?>

        let lastPushedCart = {};
        let lastPushedCustomer = {};

        function objectKeyExist(object)
        {
            const items = Array.isArray(object) ? object : Object.values(object);
            return items.reduce((acc, v) => (acc || v.customer || v.cart), false);
        }

        const isObject = x => x === Object(x);
        const has = (x, p) => isObject(x) && x.hasOwnProperty(p);
        const isEqual = (x, y) => JSON.stringify(x) === JSON.stringify(y);

        //Update datalayer
        function updateDataLayer(_gtmDataLayer, _dataObject, _forceUpdate)
        {
            let customer = {isLoggedIn : false},
                cart = {hasItems: false};

            if (_gtmDataLayer !== undefined && (!objectKeyExist(_gtmDataLayer) || _forceUpdate)) {
                if (isObject(_dataObject) && has(_dataObject, 'customer')) {
                    customer = _dataObject.customer;
                }

                if (isObject(_dataObject) && has(_dataObject, 'cart')) {
                    cart = _dataObject.cart;
                }

                if (!isEqual(lastPushedCart, cart) || !isEqual(lastPushedCustomer, customer)) {
                    window.dispatchEvent(new CustomEvent('mpCustomerSession', {detail: [customer, cart, _gtmDataLayer]}));
                    _gtmDataLayer.push({'event': 'mpCustomerSession', 'customer': customer, 'cart': cart});
                    lastPushedCustomer = customer;
                    lastPushedCart = cart;
                }
            }
        }

        function isTrackingAllowed(config)
        {
            let allowServices = false,
                allowedCookies,
                allowedWebsites;

            if (!config.isGdprEnabled || (!config.isGdprEnabled && !config.addJsInHeader)) {
                allowServices = true;
            } else if (config.isCookieRestrictionModeEnabled && config.gdprOption === 1) {
                allowedCookies = hyva.getCookie(config.cookieName);

                if (allowedCookies !== null) {
                    allowedWebsites = JSON.parse(allowedCookies);

                    if (allowedWebsites[config.currentWebsite] === 1) {
                        allowServices = true;
                    }
                }
            } else if (config.gdprOption === 2) {
                allowServices = hyva.getCookie(config.cookieName) !== null;
            } else if (config.gdprOption === 3) {
                allowServices = hyva.getCookie(config.cookieName) === null;
            }

            return allowServices;
        }

        //load gtm
        function initTracking(dataLayerName, accountId, containerCode)
        {
            window.dispatchEvent(new CustomEvent('gtm:beforeInitialize'));

            (function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start':
                        new Date().getTime(), event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s), dl = l != dataLayerName ? '&l=' + l : '';
                j.async = true;
                j.src = '//www.googletagmanager.com/gtm.js?id=' + i + dl + containerCode;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', dataLayerName, accountId);
            window.dispatchEvent(new CustomEvent('gtm:afterInitialize'));
        }

        function pushData(dataLayerName, dataLayer)
        {
            if (Array.isArray(dataLayer)) {
                dataLayer.forEach(function (data) {
                    window[dataLayerName].push(data);
                });
            }
        }

        const config = {
            dataLayer: "<?= /* @noEscape */ $block->getDataLayerName() ?>",
            gdprOption: <?= /* @noEscape */ $block->getGdprOption() ?>,
            isGdprEnabled: <?= /* @noEscape */ $block->isGdprEnabled() ?>,
            isCookieRestrictionModeEnabled: <?= /* @noEscape */ $block->isCookieRestrictionModeEnabled() ?>,
        };

        <?php if (($block->isGdprEnabled() || !$block->addJsInHead()) && !$block->isAdvancedSettingsEnabled()): ?>
        config.currentWebsite = <?= /* @noEscape */ $block->getCurrentWebsiteId() ?>;
        config.cookieName = "<?= /* @noEscape */ $block->getCookieRestrictionName() ?>";
        config.accountId = "<?= /* @noEscape */ $block->getAccountId() ?>";
        config.data = <?= /* @noEscape */ $block->getDataLayerJson() ?>;
        config.addJsInHeader = <?= /* @noEscape */ $block->addJsInHead() ?>;
        config.containerCode = "<?= /* @noEscape */ $block->getEmbeddedCode() ?>";
        <?php endif; ?>

        window[config.dataLayer] = window[config.dataLayer] || [];

        if (has(config, 'accountId') && isTrackingAllowed(config)) {
            pushData(config.dataLayer, config.data);
            initTracking(config.dataLayer, config.accountId, config.containerCode);
        }

        window.addEventListener("private-content-loaded", event => {
            const dataObject = event.detail.data['magepal-gtm-jsdatalayer'] || {};
            const gtmDataLayer = window[config.dataLayer];
            updateDataLayer(gtmDataLayer, dataObject, true)
        });
    }
    <?php if ($partyTown): ?>
    initMagepalGtmDatalayer_<?= $scriptId ?>();
    <?php else: ?>
    window.addEventListener('DOMContentLoaded', initMagepalGtmDatalayer_<?= $scriptId ?>);
    <?php endif; ?>
</script>
<!-- End Google Tag Manager by MagePal -->
<?php endif; ?>
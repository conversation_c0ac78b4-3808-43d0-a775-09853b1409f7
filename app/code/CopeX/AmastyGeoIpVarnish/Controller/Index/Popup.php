<?php
/**
 * Copyright © Andreas Pointner All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\AmastyGeoIpVarnish\Controller\Index;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\View\LayoutInterface;
use Magento\Framework\View\Result\PageFactory;

class Popup implements ActionInterface, HttpGetActionInterface
{

    private JsonFactory $jsonFactory;
    private LayoutInterface $layout;
    private PageFactory $pageFactory;

    public function __construct(
        JsonFactory $jsonFactory,
        PageFactory $pageFactory,
        LayoutInterface $layout
    ) {
        $this->jsonFactory = $jsonFactory;
        $this->layout = $layout;
        $this->pageFactory = $pageFactory;
    }

    /**
     * Execute view action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        try {
            return $this->jsonResponse(['content' => $this->getContent() ?? false ]);
        } catch (\Exception $e) {
            return $this->jsonResponse($e->getMessage());
        }
    }

    public function getContent(){
        $page = $this->pageFactory->create();
        return $page->getLayout()->getBlock('amgeoipredirect_popup')->toHtml();
    }

    /**
     * Create json response
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function jsonResponse($response = '')
    {
        return $this->jsonFactory->create()->setData($response);
    }
}

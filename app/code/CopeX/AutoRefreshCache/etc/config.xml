<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <system>
            <cache>
                <auto_flush>0</auto_flush>
                <hide_notification>0</hide_notification>
                <auto_flush_types>config,layout,block_html,collections,reflection,db_ddl,compiled_config,eav,customer_notification,config_integration,config_integration_api,config_webservice,translate</auto_flush_types>
                <auto_flush_cron>0 * * * *</auto_flush_cron>
                <auto_flush_types2>full_page</auto_flush_types2>
                <auto_flush_cron2>0 0 * * *</auto_flush_cron2>
            </cache>
        </system>
    </default>
</config>

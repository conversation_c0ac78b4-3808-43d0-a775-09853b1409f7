<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 04.07.18
 * Time: 08:07
 */

namespace CopeX\Winline\Plugin;

use CopeX\Winline\Helper\PriceHelper;

class Quote
{
    protected $priceHelper;
    protected $customerRegistry;
    /**
     * Serializer interface instance.
     * @var \Magento\Framework\Serialize\Serializer\Json
     */
    private $serializer;

    public function __construct(
        PriceHelper $priceHelper,
        \Magento\Customer\Model\CustomerRegistry $customerRegistry,
        \Magento\Framework\Serialize\Serializer\Json $serializer
    ) {
        $this->priceHelper = $priceHelper;
        $this->customerRegistry = $customerRegistry;
        $this->serializer = $serializer;
    }

    /**
     * @param \Magento\Quote\Model\Quote $subject
     * @param                            $quoteItem
     * @return \Magento\Quote\Model\Quote\Item
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function afterAddProduct(
        \Magento\Quote\Model\Quote $subject,
        $quoteItem
    ) {
        /** @var  \Magento\Quote\Model\Quote\Item $quoteItem */
        if ($quoteItem instanceof \Magento\Quote\Model\Quote\Item) {
            $this->addPriceInfoToQuoteItem($quoteItem);
        }
        return $quoteItem;
    }

    /**
     * @param $quoteItem \Magento\Quote\Model\Quote\Item
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function addPriceInfoToQuoteItem($quoteItem)
    {
        $product = $quoteItem->getProduct();
        $finalPrice = $product->getFinalPrice($quoteItem->getQty());
        if ($finalPrice == $product->getPrice()) {
            $quoteItem->setListPrice($product->getListPrice());
            $quoteItem->setData('discount1', $product->getData('discount1') ?? 0);
            $quoteItem->setData('discount2', $product->getData('discount2') ?? 0);
        }
        else {
            $customer = $this->customerRegistry->retrieve($quoteItem->getQuote()->getCustomerId());
            $possibleValues = $this->priceHelper->getPriceCollection($customer, $product->getSku());
            foreach ($possibleValues as $item) {
                if ($this->priceHelper->checkPriceType($item, $customer)) {
                    $itemPrice = $this->priceHelper->calculatePrice($item);
                    if ($itemPrice == $finalPrice) {
                        $quoteItem->setListPrice(floatval($item->getPrice()) ? $item->getPrice() : $product->getListPrice());
                        $quoteItem->setData('discount1', $item->getDiscount());
                        $quoteItem->setData('discount2', $item->getData('discount2'));
                        break;
                    }
                }
            }
        }
    }
}

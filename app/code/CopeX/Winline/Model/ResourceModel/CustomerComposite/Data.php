<?php

namespace CopeX\Winline\Model\ResourceModel\CustomerComposite;

use Magento\CustomerImportExport\Model\Import\CustomerComposite;


class Data extends \Magento\CustomerImportExport\Model\ResourceModel\Import\CustomerComposite\Data {
    /**
     * Prepare data row for address entity validation or import
     *
     * @param array $rowData
     * @return array
     */
    protected function _prepareAddressRowData(array $rowData)
    {
        $excludedAttributes = [
            CustomerComposite::COLUMN_DEFAULT_BILLING,
            CustomerComposite::COLUMN_DEFAULT_SHIPPING,
        ];
        $prefix = CustomerComposite::COLUMN_ADDRESS_PREFIX;

        $result = [];
        foreach ($rowData as $key => $value) {
            if (!in_array($key, $this->_customerAttributes)) {
                if (!in_array($key, $excludedAttributes)) {
                    $key = str_replace($prefix, '', $key);
                }
                if (!isset($result[$key])) {
                    $result[$key] = $value;
                }
            }
        }

        return $result;
    }
}
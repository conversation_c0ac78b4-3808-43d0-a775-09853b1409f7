<?php
namespace CopeX\Winline\Observer;

class TotalsAfter implements \Magento\Framework\Event\ObserverInterface
{
    protected $quotePlugin;

    public function __construct(
        \CopeX\Winline\Plugin\Quote $quotePlugin

    ) {
        $this->quotePlugin = $quotePlugin;
    }

    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $quote = $observer->getQuote();
        if(!$quote->getItems()){
            return;
        }
        foreach ($quote->getItems() as $quoteItem) {
            /** @var  \Magento\Quote\Model\Quote\Item $quoteItem */
            $this->quotePlugin->addPriceInfoToQuoteItem($quoteItem);
        }
    }
}
<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <move element="breadcrumbs" destination="columns.top" before="category.view.container" />
        <referenceContainer name="columns.top">
            <block class="Magento\Catalog\Block\Category\View"  name="category.custom.top" template="CopeX_CategoryImprovements::category/top.phtml"  before="breadcrumbs"/>
        </referenceContainer>
        <referenceContainer name="sidebar.main">
            <block class="Magento\Catalog\Block\Category\View"  name="category.custom.sidebar" template="CopeX_CategoryImprovements::category/sidebar.phtml" after="-" />
        </referenceContainer>
        <move element="category_banner" destination="columns.top" after="breadcrumbs" />
        <referenceBlock name="category.description" remove="true"/>
    </body>
</page>

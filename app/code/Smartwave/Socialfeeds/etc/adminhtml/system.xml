<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../Magento/Backend/etc/system_file.xsd">
    <system>
        <section id="sw_socialfeeds" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Social Feeds</label>
            <tab>sw_extensions</tab>
            <resource>Smartwave_Socialfeeds::config_socialfeeds</resource>
            <group id="facebook_fanbox" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Facebook Fanbox</label>
                <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="facebook_name" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Facebook Name</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="showing_counts" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Showing Counts</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
            <group id="flickr_photos" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Flickr Photos</label>
                <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="api_key" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>API Key</label>
                    <comment><![CDATA[Go to <a href="http://www.flickr.com/services/api/key.gne" target="_blank">http://www.flickr.com/services/api/key.gne</a> to get API Key]]></comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="photo_set_id" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Photo Set Id</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="showing_counts" translate="label comment" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Showing Counts</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
            <group id="instagram_photos" translate="label" type="text" sortOrder="45" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Instagram Photos</label>
                <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="accesstocken" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>AccessTocken</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="user_id" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>User Id</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="showing_counts" translate="label comment" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Showing Counts</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>

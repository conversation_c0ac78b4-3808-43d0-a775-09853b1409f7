<?xml version="1.0"?>
<!--
/**
 * <PERSON>gezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  BlueFormBuilder
 * @package   BlueFormBuilder_Core
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../Config/etc/system_file.xsd">
	<system>
		<section id="blueformbuilder" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
			<class>separator-top</class>
			<label>Blue Form Builder</label>
			<tab>magezon</tab>
			<resource>BlueFormBuilder_Core::settings</resource>
			<group id="general" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>General Settings</label>
				<field id="version" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Current Version</label>
					<frontend_model>BlueFormBuilder\Core\Block\Adminhtml\Renderer\Config\Version</frontend_model>
				</field>
				<field id="enabled" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="route" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Form Route</label>
					<comment><![CDATA[https://domain.com/<strong>FORM_ROUTE</strong>]]></comment>
				</field>
			</group>
			<group id="recaptcha" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>reCaptcha</label>
				<field id="public_key" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Site key</label>
				</field>
				<field id="secret_key" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Secret key</label>
				</field>
			</group>
			<group id="recaptcha3" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>reCaptcha3</label>
				<field id="public_key" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Site key</label>
				</field>
				<field id="secret_key" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Secret key</label>
				</field>
			</group>
			<group id="form_summary" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Form Summary</label>
				<field id="show_ip" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show IP Address</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
		</section>
	</system>
</config>
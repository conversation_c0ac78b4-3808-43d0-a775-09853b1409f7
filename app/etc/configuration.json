{"magento-version": "2.3.7", "pid-filename": "/tmp/b2b-importer.pid", "operations": {"ce": {"catalog_product": {"add-update": {"plugins": {"subject": {"id": "import.plugin.subject", "subjects": [{"id": "import_product.subject.bunch", "file-resolver": {"prefix": "product-import"}, "params": {"copy-images": false, "clean-up-category-product-relations": false, "clean-up-empty-columns": ["special_price", "delivery_time", "delivery_date", "weight", "ean", "manufacturer", "abc", "kollektion", "product_series", "country_of_manufacture", "outer_dimension_height", "outer_dimension_width", "outer_dimension_depth", "outer_dimension", "inner_dimension_height", "inner_dimension_width", "inner_dimension_depth", "inner_dimension", "weight_filter", "volume", "volume_filter", "usage", "classification", "security_level_filter", "certification_detail", "insurance_privat_at", "insurance_commercial_at", "insurance_privat_de", "insurance_commercial_de", "fire_protection_detail", "fire_protection", "lock_type", "number_hooks", "hooks_changeable", "door_open_angle", "number_of_shelves", "shelves_changeable", "number_bolts", "number_folders", "number_of_drawers", "number_drawers", "floor_drilling", "number_drill_base", "back_wall_drilling", "number_drill_back_wall", "back_wall_drilling_diameter", "floor_drilling_diameter", "testing_institute", "height_door_opening", "width_door_opening", "height_interior_safe", "anchoring_material_included", "interior_safe", "color", "color_filter", "ral_color", "emergency_opening", "battery_necessary", "battery_included", "number_battery", "battery_type", "outer_dimension_extension", "handle", "number_weapons", "number_of_handguns", "material", "slot_height", "slot_width", "amazon_title", "external_attachments"]}, "observers": [{"import": ["import_product.observer.composite.base.add_update"]}]}]}}}}}}, "loggers": {"mail": {"id": "import.logger.factory.monolog", "channel-name": "logger/mail", "handlers": [{"id": "import.logger.factory.handler.native.mail.log", "formatter": {"id": "import.logger.factory.formatter.line", "params": {"format": "[%datetime%] %channel%.%level_name%: %message% %context% %extra%", "date-format": "Y-m-d H:i:s", "allow-inline-line-breaks": true, "ignore-empty-context-and-extra": true}}, "params": {"to": ["<EMAIL>"], "subject": "Import Failed", "from": "<EMAIL>", "level": "error", "bubble": true, "maxColumnWidth": 500}}]}}}
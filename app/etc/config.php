<?php
return [
    'modules' => [
        'Magento_AdminAnalytics' => 1,
        'Magento_Store' => 1,
        'Magento_Directory' => 1,
        'Magento_Config' => 1,
        'Magento_Theme' => 1,
        'Magento_Variable' => 1,
        'Magento_Backend' => 1,
        'Magento_Backup' => 1,
        'Magento_Eav' => 1,
        'Magento_CacheInvalidate' => 1,
        'Magento_Customer' => 1,
        'Magento_AdminNotification' => 1,
        'Magento_Authorization' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_Indexer' => 1,
        'Magento_Rule' => 1,
        'Magento_Cms' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_Catalog' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_Widget' => 1,
        'Magento_Payment' => 1,
        'Magento_Quote' => 1,
        'Magento_Bundle' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_GraphQl' => 1,
        'Magento_User' => 1,
        'Magento_Msrp' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_Contact' => 1,
        'Magento_Cookie' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_Sales' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_CustomerImportExport' => 0,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_Downloadable' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GoogleAnalytics' => 0,
        'Magento_Checkout' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_PageCache' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_ImportExport' => 1,
        'Magento_Captcha' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_Security' => 1,
        'Magento_Inventory' => 1,
        'Magento_InventoryAdminUi' => 1,
        'Magento_InventoryAdvancedCheckout' => 1,
        'Magento_InventoryApi' => 1,
        'Magento_InventoryBundleImportExport' => 1,
        'Magento_InventoryBundleProduct' => 0,
        'Magento_InventoryBundleProductAdminUi' => 0,
        'Magento_InventoryBundleProductIndexer' => 1,
        'Magento_InventoryCatalog' => 1,
        'Magento_InventorySales' => 1,
        'Magento_InventoryCatalogAdminUi' => 1,
        'Magento_InventoryCatalogApi' => 1,
        'Magento_InventoryCatalogFrontendUi' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_InventoryConfigurableProductIndexer' => 1,
        'Magento_InventoryConfiguration' => 1,
        'Magento_InventoryConfigurationApi' => 1,
        'Magento_InventoryDistanceBasedSourceSelection' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 1,
        'Magento_InventoryElasticsearch' => 1,
        'Magento_InventoryExportStockApi' => 1,
        'Magento_InventoryIndexer' => 1,
        'Magento_InventorySalesApi' => 1,
        'Magento_InventoryGroupedProduct' => 1,
        'Magento_InventoryGroupedProductAdminUi' => 1,
        'Magento_InventoryGroupedProductIndexer' => 1,
        'Magento_InventoryImportExport' => 1,
        'Magento_InventoryInStorePickupApi' => 1,
        'Magento_Ui' => 1,
        'Magento_InventorySourceSelectionApi' => 1,
        'Magento_InventoryInStorePickup' => 1,
        'Magento_InventoryInStorePickupGraphQl' => 1,
        'Magento_Shipping' => 1,
        'Magento_InventoryInStorePickupShippingApi' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_InventoryInStorePickupSales' => 1,
        'Magento_InventoryInStorePickupSalesApi' => 1,
        'Magento_InventoryInStorePickupQuote' => 1,
        'Magento_InventoryInStorePickupShipping' => 1,
        'Magento_InventoryInStorePickupShippingAdminUi' => 1,
        'Magento_Multishipping' => 1,
        'Magento_Integration' => 1,
        'Magento_InventoryCache' => 1,
        'Magento_InventoryLowQuantityNotification' => 1,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 1,
        'Magento_InventoryMultiDimensionalIndexerApi' => 1,
        'Magento_InventoryProductAlert' => 1,
        'Magento_InventoryQuoteGraphQl' => 1,
        'Magento_InventoryRequisitionList' => 1,
        'Magento_InventoryReservations' => 1,
        'Magento_InventoryReservationCli' => 1,
        'Magento_InventoryReservationsApi' => 1,
        'Magento_InventoryExportStock' => 1,
        'Magento_InventorySalesAdminUi' => 1,
        'Magento_InventoryGraphQl' => 1,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 1,
        'Magento_InventorySetupFixtureGenerator' => 1,
        'Magento_InventoryShipping' => 1,
        'Magento_InventoryShippingAdminUi' => 1,
        'Magento_InventorySourceDeductionApi' => 1,
        'Magento_InventorySourceSelection' => 1,
        'Magento_InventoryInStorePickupFrontend' => 1,
        'Magento_InventorySwatchesFrontendUi' => 1,
        'Magento_InventoryVisualMerchandiser' => 1,
        'Magento_InventoryWishlist' => 1,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 0,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_Robots' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_InventoryInStorePickupMultishipping' => 1,
        'Magento_MysqlMq' => 1,
        'Magento_NewRelicReporting' => 0,
        'Magento_Newsletter' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_SalesRule' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_Vault' => 1,
        'Magento_Paypal' => 1,
        'Magento_Persistent' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ProductVideo' => 0,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_InventoryInStorePickupQuoteGraphQl' => 1,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaSendFriend' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_Sitemap' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 1,
        'Magento_RequireJs' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_Rss' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_GoogleGtag' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_Webapi' => 1,
        'Magento_Securitytxt' => 1,
        'Magento_SendFriend' => 1,
        'Magento_InventoryInStorePickupSalesAdminUi' => 1,
        'Magento_AwsS3' => 1,
        'Magento_InventoryCatalogSearch' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_Tax' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_InventoryInStorePickupAdminUi' => 1,
        'Magento_UrlRewrite' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_Elasticsearch7' => 0,
        'Magento_PaypalCaptcha' => 1,
        'Magento_InventoryInStorePickupWebapiExtension' => 1,
        'Magento_WebapiAsync' => 0,
        'Magento_WebapiSecurity' => 1,
        'Magento_Weee' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_Wishlist' => 1,
        'Aitoc_Core' => 1,
        'Aitoc_GoogleReviews' => 1,
        'Amasty_BannersLite' => 1,
        'Amasty_Base' => 1,
        'Amasty_Conditions' => 1,
        'Amasty_Geoip' => 1,
        'Amasty_Promo' => 1,
        'Amasty_Rgrid' => 1,
        'Amasty_Rolepermissions' => 1,
        'Amasty_SalesRuleWizard' => 1,
        'Amasty_Storelocator' => 1,
        'Amasty_StorelocatorIndexer' => 1,
        'Amazon_Pay' => 1,
        'Ampersand_DisableStockReservation' => 1,
        'AvS_ScopeHint' => 1,
        'Magezon_Core' => 1,
        'Catgento_AdminActivity' => 1,
        'CopeX_AlpineJs' => 1,
        'Smartwave_Porto' => 1,
        'CopeX_BlockCaches' => 1,
        'CopeX_CartDiscountPercentage' => 1,
        'CopeX_CatalogReadOnlyByDomain' => 1,
        'CopeX_CategoryImprovements' => 1,
        'CopeX_CategorySEO' => 1,
        'CopeX_CategoryValuesInCategoryFilterAttribute' => 1,
        'CopeX_CheckoutCaches' => 1,
        'CopeX_CheckoutOnlyleft' => 1,
        'CopeX_Cleanup' => 1,
        'MDS_Collivery' => 1,
        'CopeX_CookieNotification' => 1,
        'CopeX_CopeXEstimateShippingOnProductMageplazaCallForPrice' => 1,
        'CopeX_CopyrightHtml' => 1,
        'CopeX_Core' => 1,
        'CopeX_CrosssellProducts' => 1,
        'CopeX_CustomerSectionCache' => 1,
        'CopeX_DecimalProductFetchFix' => 1,
        'CopeX_DiscountCodeUrl' => 1,
        'CopeX_DuplicateCategory' => 1,
        'Smile_ElasticsuiteCore' => 1,
        'CopeX_ElasticSuiteCatalogInventory' => 1,
        'CopeX_ElasticSuiteSorting' => 1,
        'CopeX_EmailImages' => 1,
        'CopeX_EmailPaymentAndShippingInstructions' => 1,
        'CopeX_EstimateShippingOnProduct' => 1,
        'Smile_ElasticsuiteCatalog' => 1,
        'CopeX_GoToLastViewedProduct' => 1,
        'CopeX_HeurekaIntegration' => 1,
        'CopeX_HideProductAttributes' => 1,
        'Smile_ElasticsuiteCatalogRule' => 1,
        'CopeX_HyvaVideo' => 1,
        'CopeX_Import' => 1,
        'CopeX_InlineFontLoader' => 1,
        'CopeX_LandingJs' => 1,
        'Hyva_Theme' => 1,
        'Ess_M2ePro' => 0,
        'Swissup_Firecheckout' => 1,
        'MagePal_GoogleTagManager' => 1,
        'Magezon_Builder' => 1,
        'CopeX_MassAssignCategory' => 1,
        'Mobilpay_Credit' => 1,
        'CopeX_OrderDateFilterFix' => 1,
        'CopeX_PRG' => 1,
        'CopeX_Performance' => 1,
        'CopeX_Pictograms' => 1,
        'CopeX_Pimcore' => 1,
        'CopeX_PimcoreRottnerProfile' => 1,
        'CopeX_ProductAttributeGrouping' => 1,
        'CopeX_ProductDetailSeoLink' => 1,
        'CopeX_ProductListProductAttributes' => 1,
        'CopeX_ProductSeries' => 1,
        'CopeX_ProductView360' => 1,
        'Yireo_CspUtilities' => 1,
        'CopeX_ProductViewCmsBlock' => 1,
        'CopeX_RequireReplace' => 1,
        'CopeX_RottnerAttachments' => 1,
        'CopeX_RuntimeConfigSetter' => 1,
        'Swissup_CheckoutRegistration' => 1,
        'Swissup_Gdpr' => 1,
        'Swissup_Orderattachment' => 1,
        'CopeX_TrustedShopsStars' => 1,
        'CopeX_WorkDays' => 1,
        'CopeX_WyomindDataFeedManagerDefaultStock' => 1,
        'Wyomind_PaymentMethodRules' => 1,
        'Yireo_NextGenImages' => 1,
        'CustomGento_AdminPayment' => 1,
        'Divante_ReviewApi' => 1,
        'CopeX_M2eAllowdCountries' => 1,
        'EthanYehuda_CronjobManager' => 1,
        'Experius_EmailCatcher' => 1,
        'Experius_PageNotFound' => 1,
        'Experius_WysiwygDownloads' => 1,
        'FireGento_MageSetup' => 1,
        'Fooman_EmailAttachments' => 1,
        'Hyva_CompatModuleFallback' => 1,
        'Hyva_AmastyStorelocator' => 1,
        'Hyva_AmazonPay' => 1,
        'Hyva_CmsTailwindJit' => 1,
        'Hyva_AmastyPromo' => 1,
        'Hyva_Email' => 1,
        'Hyva_GraphqlTokens' => 1,
        'Hyva_GraphqlViewModel' => 1,
        'Hyva_ThemeFallback' => 1,
        'MagePal_Core' => 1,
        'Hyva_MagePalGoogleTagManager' => 1,
        'MageWorx_SeoAll' => 1,
        'MageWorx_SeoMarkup' => 1,
        'Magezon_PageBuilder' => 1,
        'Hyva_MollieThemeBundle' => 1,
        'Hyva_OrderCancellationWebapi' => 1,
        'Hyva_PaymentIcons' => 1,
        'Smile_ElasticsuiteSwatches' => 1,
        'Hyva_SwissupGdpr' => 1,
        'CopeX_LandingPageParameter' => 1,
        'Hyva_LumaCheckout' => 1,
        'Interactivated_Customerreview' => 1,
        'M2E_Core' => 0,
        'M2E_Otto' => 0,
        'CopeX_ColliveryFilter' => 1,
        'MagePal_GoogleAnalytics4' => 1,
        'MagePal_CustomerAccountLinksManager' => 1,
        'MagePal_GmailSmtpApp' => 1,
        'Hyva_MagePalGoogleAnalytics4' => 1,
        'CopeX_MagepalGTMAdditionalFields' => 1,
        'MagePal_PreviewCheckoutSuccessPage' => 1,
        'MageSuite_Magepack' => 1,
        'MageWorx_OpenAI' => 0,
        'MageWorx_HtmlSitemap' => 1,
        'MageWorx_Info' => 0,
        'MageWorx_GoogleAI' => 0,
        'MageWorx_SeoAI' => 0,
        'Hyva_MageWorxHtmlSitemap' => 1,
        'MageWorx_SeoBase' => 1,
        'MageWorx_SeoBaseCustom' => 1,
        'MageWorx_SeoBreadcrumbs' => 1,
        'MageWorx_SeoCategoryGrid' => 1,
        'MageWorx_SeoCrossLinks' => 1,
        'MageWorx_SeoExtended' => 1,
        'Hyva_MageWorxSeoMarkup' => 1,
        'MageWorx_SeoRedirects' => 1,
        'MageWorx_SeoReports' => 1,
        'MageWorx_SeoUrls' => 1,
        'MageWorx_SeoXTemplates' => 1,
        'MageWorx_XmlSitemap' => 1,
        'MageWorx_XmlSitemapCustom' => 1,
        'Magefan_Community' => 1,
        'Magefan_Translation' => 1,
        'Magefan_WysiwygAdvanced' => 1,
        'Magenest_Barclaycard' => 1,
        'Magenest_Core' => 1,
        'Magenuts_CustomOrderNumber' => 1,
        'Mageplaza_Core' => 1,
        'Mageplaza_BackendReindex' => 1,
        'Mageplaza_CallForPrice' => 1,
        'Mageplaza_AjaxLayer' => 0,
        'Mageplaza_ExtraFee' => 1,
        'Mollie_Payment' => 1,
        'Mageplaza_LayeredNavigation' => 0,
        'Mageplaza_Search' => 0,
        'Magezon_UiBuilder' => 1,
        'CopeX_MagezonNativeLazyLoading' => 1,
        'Magezon_Newsletter' => 1,
        'Hyva_MagezonPageBuilder' => 0,
        'Magezon_PageBuilderIconBox' => 1,
        'Magezon_PageBuilderPageableContainer' => 1,
        'Magezon_PageBuilderPreview' => 1,
        'BlueFormBuilder_Core' => 1,
        'Maghos_Gopay' => 1,
        'Magmodules_AlternateHreflang' => 1,
        'MasterCard_SimplifyCommerce' => 1,
        'Mirasvit_CacheWarmer' => 1,
        'Mirasvit_Core' => 1,
        'Mirasvit_Report' => 1,
        'CopeX_MobilPayRedirectSuccessPage' => 1,
        'Mollie_HyvaCompatibility' => 1,
        'Mageplaza_ExtraFeeMollie' => 1,
        'Netopia_Netcard' => 1,
        'OlegKoval_RegenerateUrlRewrites' => 1,
        'Owebia_SharedPhpConfig' => 1,
        'Owebia_AdvancedShipping' => 1,
        'Packetery_Checkout' => 1,
        'Payfast_Payfast' => 1,
        'Phoenix_BankPayment' => 1,
        'Phoenix_CashOnDelivery' => 1,
        'RedChamps_CleanMenu' => 1,
        'RedChamps_Core' => 1,
        'RedChamps_ExtraSortingOptions' => 1,
        'RedChamps_ExtraSortingOptionsElasticSuite' => 1,
        'ReesSolutions_DBOverride' => 1,
        'Rottner_AdminPayment2' => 1,
        'Smartwave_Megamenu' => 1,
        'Rottner_StockFix' => 1,
        'Salecto_MediaStorageSync' => 1,
        'Sehrling_ElasticsuiteGhostCleaner' => 1,
        'Smartwave_Core' => 1,
        'Smartwave_Dailydeals' => 0,
        'Smartwave_Filterproducts' => 1,
        'Rottner_Customization' => 1,
        'CopeX_B2BLoginLink' => 1,
        'Smartwave_Socialfeeds' => 0,
        'Smile_DebugToolbar' => 0,
        'Smile_ElasticsuiteAdminNotification' => 1,
        'Smile_ElasticsuiteTracker' => 1,
        'CopeX_ElasticSuiteAddOptionIds' => 1,
        'Smile_ElasticsuiteCatalogGraphQl' => 1,
        'Smile_ElasticsuiteCatalogOptimizer' => 1,
        'Smile_ElasticsuiteVirtualCategory' => 1,
        'Smile_ElasticsuiteCms' => 0,
        'CopeX_FilterGrouping' => 1,
        'Smile_ElasticsuiteThesaurus' => 1,
        'Hyva_SmileElasticsuite' => 1,
        'Smile_ElasticsuiteIndices' => 1,
        'Smile_ElasticsuiteAnalytics' => 1,
        'CopeX_HideVirtualCategoryInLayeredNavigation' => 1,
        'Strategery_Infinitescroll' => 1,
        'Swissup_AddressAutocomplete' => 1,
        'Swissup_AddressFieldManager' => 1,
        'Swissup_AddressValidation' => 1,
        'Swissup_Checkout' => 1,
        'Swissup_CheckoutCart' => 1,
        'Swissup_CheckoutFields' => 1,
        'CopeX_SwissupCheckoutRegistrationPerformance' => 1,
        'Swissup_CheckoutSuccess' => 1,
        'Swissup_Codemirror' => 1,
        'Swissup_Core' => 1,
        'Swissup_CustomerFieldManager' => 1,
        'Swissup_DeliveryDate' => 1,
        'Swissup_FieldManager' => 1,
        'CopeX_Magepack' => 1,
        'Swissup_FirecheckoutIntegrations' => 1,
        'CopeX_SwissupGDPRMagepalGTM' => 1,
        'Swissup_Geoip' => 1,
        'Swissup_Image' => 1,
        'CopeX_SwissupOrderAttachmentApiInfo' => 1,
        'Swissup_Pagespeed' => 1,
        'Swissup_Rtl' => 1,
        'Swissup_Stickyfill' => 1,
        'Swissup_SubscribeAtCheckout' => 1,
        'Swissup_Taxvat' => 1,
        'Swissup_Tippyjs' => 1,
        'Tigren_Ajaxsuite' => 1,
        'Tigren_Ajaxcart' => 1,
        'Tigren_Core' => 1,
        'Trustedshops_Trustedshops' => 1,
        'Wagento_FixSalesSequence' => 1,
        'Wyomind_Framework' => 1,
        'Wyomind_DataFeedManager' => 1,
        'CopeX_WyomindPaymentMethodRulsOwebiaAdvancedShipping' => 1,
        'Xtento_XtCore' => 1,
        'Xtento_ProductExport' => 1,
        'CopeX_ProductView360WebP' => 1,
        'Yireo_DisableCsp' => 1,
        'CopeX_YireoNextGenErrorOnEmpty' => 1,
        'Yireo_Webp2' => 1,
        'Yireo_Webp2ForHyva' => 1,
        'Yireo_Whoops' => 1
    ]
];

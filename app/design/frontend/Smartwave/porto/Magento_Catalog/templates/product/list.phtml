<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
use Magento\Framework\App\Action\Action;

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Product list template
 *
 * @var $block \Magento\Catalog\Block\Product\ListProduct
 */
?>
<?php
$_productCollection = $block->getLoadedProductCollection();
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$_imagehelper = $this->helper('Magento\Catalog\Helper\Image');

// Daily deal Helper 
$_dailydealhelper =$this->helper('Smartwave\Dailydeals\Helper\Data');

$_portohelper = $this->helper('Smartwave\Porto\Helper\Data');

$review_model = $_portohelper->getModel('\Magento\Review\Model\Review');
$_category_config = $_portohelper->getConfig('porto_settings/category');
$_category_grid_config = $_portohelper->getConfig('porto_settings/category_grid');
$_product_label_config = $_portohelper->getConfig('porto_settings/product_label');
$_lazyload = $_portohelper->getConfig('porto_settings/optimization/lazyload');
?>
<?php $iterator = 1; ?>
<?php if (!$_productCollection->count()): ?>
    <div class="message info empty"><div><?php echo __('We can\'t find products matching the selection.') ?></div></div>
<?php else: ?>
    <?php echo $block->getToolbarHtml() ?>
    <?php echo $block->getAdditionalHtml() ?>
    <?php
    if ($block->getMode() == 'grid') {
        $viewMode = 'grid';
        $image = 'category_page_grid';
        $hover_image = 'category_page_grid-hover';
        $showDescription = false;
        $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
        $columns = 'columns'.$_category_grid_config['columns'];
        $product_type = $_category_grid_config['product_type'];
        if($product_type == null) {
            $product_type = 1;
        }
    } else {
        $viewMode = 'list';
        $image = 'category_page_list';
        $hover_image = 'category_page_list-hover';
        $showDescription = true;
        $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::FULL_VIEW;
        $columns = '';
        $product_type = 0;
    }
    $image_width = ($_category_config['ratio_width'])?$_category_config['ratio_width']:300;
    $image_height = ($_category_config['ratio_height'])?$_category_config['ratio_height']:300;
    $aspect_ratio = $_category_config['aspect_ratio'];
    if($aspect_ratio)
        $image_height = $image_width;
    ?>
    <div class="products wrapper <?php echo $viewMode; ?> <?php echo $columns; ?> products-<?php echo $viewMode; ?> <?php if($product_type == 2): ?>no-padding divider-line<?php endif; ?> <?php if($product_type == 5 || $product_type == 6 || $product_type == 7 || $product_type == 8): ?>no-padding<?php endif; ?> <?php if($product_type == 6): ?>divider-line<?php endif; ?>">
        <?php $iterator = 1; ?>
        <ol class="filterproducts products list items product-items <?php if(isset($_category_config['qty_field']) && $_category_config['qty_field']):?>has-qty<?php endif;?>">
            <?php /** @var $_product \Magento\Catalog\Model\Product */ ?>
            <?php foreach ($_productCollection as $_product): ?>
                <?php echo($iterator++ == 1) ? '<li class="item product product-item">' : '</li><li class="item product product-item">' ?>
                <div class="product-item-info type<?php echo $product_type; ?>" data-container="product-grid">
                    <?php // Product Image ?>
                    <div class="product photo product-item-photo">
                        <a href="<?php echo $_product->getProductUrl() ?>" tabindex="-1">
                        <?php
                            if($aspect_ratio)
                                $productImage = $_imagehelper->init($_product, $image)->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($image_width);
                            else
                                $productImage = $_imagehelper->init($_product, $image)->resize($image_width, $image_height);
                            $productImageUrl = $productImage->getUrl();
                        ?>
                            <img class="product-image-photo default_image <?php if(!$_lazyload): ?>porto-lazyload<?php endif;?>" <?php if(!$_lazyload): ?>data-<?php endif; ?>src="<?php echo $productImageUrl; ?>" width="<?php echo $image_width; ?>" height="<?php echo $image_height; ?>" alt=""/>
                        <?php if($_category_config['alternative_image']): ?>
                        <?php
                            if($aspect_ratio)
                                $productHoverImage = $_imagehelper->init($_product, $hover_image)->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($image_width);
                            else
                                $productHoverImage = $_imagehelper->init($_product, $hover_image)->resize($image_width, $image_height);
                            $productHoverImageUrl = $productHoverImage->getUrl();
                        ?>
                            <?php if($productImageUrl != str_replace("/thumbnail/","/small_image/",$productHoverImageUrl)): ?>
                            <img class="product-image-photo hover_image" src="<?php echo $productHoverImageUrl; ?>" alt=""/>
                            <?php endif; ?>
                        <?php endif; ?>
                        </a>
                        <?php
                            $product_label = "";
                            if($_product_label_config['sale_label']) {
                                $orgprice = $_product->getPrice();
                                $specialprice = $_product->getSpecialPrice();
                                $specialfromdate = $_product->getSpecialFromDate();
                                $specialtodate = $_product->getSpecialToDate();
                                $today = time();
                                if(!$specialprice)
                                    $specialprice = $orgprice;
                                if($specialprice < $orgprice) {
                                    if((is_null($specialfromdate) && is_null($specialtodate)) || ($today >= strtotime($specialfromdate) && is_null($specialtodate)) || ($today <= strtotime($specialtodate) && is_null($specialfromdate)) || ($today >= strtotime($specialfromdate) && $today <= strtotime($specialtodate))){
                                        if($_product_label_config['sale_label_percent']) {
                                            $save_percent = 100-round(($specialprice/$orgprice)*100);
                                            $product_label .= '<div class="product-label sale-label">'.'-'.$save_percent.'%'.'</div>';
                                        } else {
                                            $product_label .= '<div class="product-label sale-label">'.$_product_label_config['sale_label_text'].'</div>';
                                        }
                                    }
                                }
                            }
                            if($_product_label_config['new_label']) {
                                $now = date("Y-m-d");
                                $newsFrom= substr($_product->getData('news_from_date'),0,10);
                                $newsTo=  substr($_product->getData('news_to_date'),0,10);
                                
                                if ($newsTo != '' || $newsFrom != ''){
                                    if (($newsTo != '' && $newsFrom != '' && $now>=$newsFrom && $now<=$newsTo) || ($newsTo == '' && $now >=$newsFrom) || ($newsFrom == '' && $now<=$newsTo)) {
                                        $product_label .= '<div class="product-label new-label">'.$_product_label_config['new_label_text'].'</div>';
                                    }
                                }
                            }
                            if($product_label)
                                echo '<div class="product-labels">'.$product_label.'</div>';
                        ?>
                        <?php if($product_type == 3 || $product_type == 5 || $product_type == 6 || $product_type == 7 || $product_type == 8 || $product_type == 9): ?>
                        <div class="product-item-inner">
                            <div class="product actions product-item-actions">
                                <div class="actions-primary">
                                    <?php if ($_product->isSaleable()): ?>
                                        <?php $postParams = $block->getAddToCartPostParams($_product); ?>
                                        <form data-role="tocart-form" action="<?php echo $postParams['action']; ?>" method="post">
                                            <input type="hidden" name="product" value="<?php echo $postParams['data']['product']; ?>">
                                            <input type="hidden" name="<?php echo Action::PARAM_NAME_URL_ENCODED; ?>" value="<?php echo $postParams['data'][Action::PARAM_NAME_URL_ENCODED]; ?>">
                                            <?php if(isset($_category_config['qty_field']) && $_category_config['qty_field']):?>
                                                <div class="qty-box">
                                                    <a href="javascript:void(0)" class="qtyminus"><i class="porto-icon-minus"></i></a>
                                                    <input type="text" name="qty" id="qty" maxlength="12" value="<?php /* @escapeNotVerified */ echo $block->getProductDefaultQty() * 1 ?>" title="<?php /* @escapeNotVerified */ echo __('Qty') ?>" class="input-text qty" data-validate="<?php echo $block->escapeHtml(json_encode($block->getQuantityValidators())) ?>"/>
                                                    <a href="javascript:void(0)" class="qtyplus"><i class="porto-icon-plus"></i></a>
                                                </div>
                                            <?php endif;?>
                                            <?php echo $block->getBlockHtml('formkey')?>
                                            <button type="submit"
                                                    title="<?php echo $block->escapeHtml(__('Add to Cart')); ?>"
                                                    class="action tocart primary">
                                                <span><?php echo __('Add to Cart') ?></span>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <?php if ($_product->getIsSalable()): ?>
                                            <div class="stock available"><span><?php echo __('In stock') ?></span></div>
                                        <?php else: ?>
                                            <div class="stock unavailable"><span><?php echo __('Out of stock') ?></span></div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                                <?php if ($block->getMode() == 'grid'): ?>
                                <?php if ($this->helper('Magento\Wishlist\Helper\Data')->isAllow()): ?>
                                    <a href="#"
                                       class="action towishlist actions-secondary"
                                       title="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       aria-label="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       data-post='<?php echo $block->getAddToWishlistParams($_product); ?>'
                                       data-action="add-to-wishlist"
                                       role="button">
                                        <span><?php echo __('Add to Wish List') ?></span>
                                    </a>
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php if($_category_config['addtocompare']): ?>
                                <?php
                                $compareHelper = $this->helper('Magento\Catalog\Helper\Product\Compare');
                                ?>
                                <a href="#"
                                   class="action tocompare actions-secondary"
                                   title="<?php echo $block->escapeHtml(__('Add to Compare')); ?>"
                                   aria-label="<?php echo $block->escapeHtml(__('Add to Compare')); ?>"
                                   data-post='<?php echo $compareHelper->getPostDataParams($_product); ?>'
                                   role="button">
                                    <span><?php echo __('Add to Compare') ?></span>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <!-- Dailydeal Product data -->
                        <?php if($_dailydealhelper->isDealProduct($_product->getId())) : ?>
                        <?php $unique_id = rtrim(base64_encode(md5(microtime())),"="); ?>
                        <input type="hidden" id="todate_<?php echo $unique_id; ?>" value="<?php echo $_dailydealhelper->getDailydealToDate($_product->getSku()); ?>" >
                        <input type="hidden" id="fromdate_<?php echo $unique_id; ?>" value="<?php echo $_dailydealhelper->getDailydealFromDate($_product->getSku()); ?>">
                        <div class="sw-dailydeal-wrapper" style="display:none;" data-unique-id="<?php echo $unique_id; ?>">
                            <div class="sw-dailydeal">
                                <p id="expired_<?php echo $unique_id; ?>"></p> 
                                <div class="countdowncontainer countdowncontainer_<?php echo $unique_id; ?>" style="display:none;">
                                    <span class="dailydeal-label">
                                        <?php echo __('Ends In:'); ?>
                                    </span>
                                    <span class="number-wrapper">
                                        <div class="line"></div>
                                        <span class="number day"><p id="countdown_days_<?php echo $unique_id; ?>">00</p></span>
                                        <div class="caption"><?php echo __('Day(s), '); ?></div>
                                    </span>

                                    <span class="number-wrapper">
                                        <div class="line"></div>
                                        <span class="number hour"><p id="countdown_hours_<?php echo $unique_id; ?>">00</p></span>
                                        <div class="caption">:</div>
                                    </span>

                                    <span class="number-wrapper">
                                        <div class="line"></div>
                                        <span class="number min"><p id="countdown_minutes_<?php echo $unique_id; ?>">00</p></span>
                                        <div class="caption">:</div>
                                    </span>

                                    <span class="number-wrapper">
                                        <div class="line"></div>
                                        <span class="number sec"><p id="countdown_seconds_<?php echo $unique_id; ?>">00</p></span>
                                        <div class="caption"></div>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?> 
                        <!-- Dailydeal Product End -->
                    </div>
                    <div class="product details product-item-details">
                        <?php
                            $_productNameStripped = $block->stripTags($_product->getName(), null, true);
                        ?>
                        <strong class="product name product-item-name">
                            <a class="product-item-link"
                               href="<?php echo $_product->getProductUrl() ?>">
                                <?php echo $_helper->productAttribute($_product, $_product->getName(), 'name'); ?>
                            </a>
                        </strong>
                        <?php if($_category_config['rating_star']): ?>
                        <?php
                            $review_model->getEntitySummary($_product);
                            $review_html = $block->getReviewsSummaryHtml($_product, $templateType);
                        ?>
                        <?php if($review_html): ?>
                            <?php echo $block->getReviewsSummaryHtml($_product, $templateType); ?>
                        <?php else: ?>
                        <div class="product-reviews-summary short">
                            <div class="rating-summary">
                                <span class="label"><span>Rating:</span></span>
                                <div class="rating-result" title="0%">
                                    <span style="width:0"><span>0%</span></span>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php if ($showDescription):?>
                            <div class="product description product-item-description">
                                <?php echo $_helper->productAttribute($_product, $_product->getShortDescription(), 'short_description') ?>
                                <a href="<?php echo $_product->getProductUrl() ?>" title="<?php echo $_productNameStripped ?>"
                                   class="action more"><?php echo __('Learn More') ?></a>
                            </div>
                        <?php endif; ?>
                        <?php if($_category_config['product_price']): ?>
                        <?php echo $block->getProductPrice($_product) ?>
                        <?php endif; ?>
                        <?php echo $block->getProductDetailsHtml($_product); ?>
                        
                        <?php if($product_type != 3 && $product_type != 5 && $product_type != 6 && $product_type != 7 && $product_type != 8 && $product_type != 9): ?>
                        <div class="product-item-inner">
                            <div class="product actions product-item-actions">
                                <?php if ($block->getMode() == 'grid'): ?>
                                <?php if($_category_config['addtowishlist'] && $product_type != 4): ?>
                                <?php if ($this->helper('Magento\Wishlist\Helper\Data')->isAllow()): ?>
                                    <a href="#"
                                       class="action towishlist actions-secondary <?php if(isset($_category_config['qty_field']) && $_category_config['qty_field']):?>has-qty<?php endif;?>"
                                       title="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       aria-label="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       data-post='<?php echo $block->getAddToWishlistParams($_product); ?>'
                                       data-action="add-to-wishlist"
                                       role="button">
                                        <span><?php echo __('Add to Wish List') ?></span>
                                    </a>
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php endif; ?>
                                <div class="actions-primary">
                                    <?php if ($_product->isSaleable()): ?>
                                        <?php $postParams = $block->getAddToCartPostParams($_product); ?>
                                        <form data-role="tocart-form" action="<?php echo $postParams['action']; ?>" method="post">
                                            <input type="hidden" name="product" value="<?php echo $postParams['data']['product']; ?>">
                                            <input type="hidden" name="<?php echo Action::PARAM_NAME_URL_ENCODED; ?>" value="<?php echo $postParams['data'][Action::PARAM_NAME_URL_ENCODED]; ?>">
                                            <?php if(isset($_category_config['qty_field']) && $_category_config['qty_field']):?>
                                                <div class="qty-box">
                                                    <a href="javascript:void(0)" class="qtyminus"><i class="porto-icon-minus"></i></a>
                                                    <input type="text" name="qty" id="qty" maxlength="12" value="<?php /* @escapeNotVerified */ echo $block->getProductDefaultQty() * 1 ?>" title="<?php /* @escapeNotVerified */ echo __('Qty') ?>" class="input-text qty" data-validate="<?php echo $block->escapeHtml(json_encode($block->getQuantityValidators())) ?>"/>
                                                    <a href="javascript:void(0)" class="qtyplus"><i class="porto-icon-plus"></i></a>
                                                </div>
                                            <?php endif;?>
                                            <?php echo $block->getBlockHtml('formkey')?>
                                            <button type="submit"
                                                    title="<?php echo $block->escapeHtml(__('Add to Cart')); ?>"
                                                    class="action tocart primary">
                                                <span><?php echo __('Add to Cart') ?></span>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <?php if ($_product->getIsSalable()): ?>
                                            <div class="stock available"><span><?php echo __('In stock') ?></span></div>
                                        <?php else: ?>
                                            <div class="stock unavailable"><span><?php echo __('Out of stock') ?></span></div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                                <?php if ($block->getMode() == 'grid'): ?>
                                <?php if($_category_config['addtowishlist'] && $product_type == 4): ?>
                                <?php if ($this->helper('Magento\Wishlist\Helper\Data')->isAllow()): ?>
                                    <a href="#"
                                       class="action towishlist actions-secondary <?php if(isset($_category_config['qty_field']) && $_category_config['qty_field']):?>has-qty<?php endif;?>"
                                       title="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       aria-label="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       data-post='<?php echo $block->getAddToWishlistParams($_product); ?>'
                                       data-action="add-to-wishlist"
                                       role="button">
                                        <span><?php echo __('Add to Wish List') ?></span>
                                    </a>
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php if ($block->getMode() == 'list'): ?>
                                <?php if($_category_config['addtowishlist']): ?>
                                <?php if ($this->helper('Magento\Wishlist\Helper\Data')->isAllow()): ?>
                                    <a href="#"
                                       class="action towishlist actions-secondary <?php if(isset($_category_config['qty_field']) && $_category_config['qty_field']):?>has-qty<?php endif;?>"
                                       title="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       aria-label="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       data-post='<?php echo $block->getAddToWishlistParams($_product); ?>'
                                       data-action="add-to-wishlist"
                                       role="button">
                                        <span><?php echo __('Add to Wish List') ?></span>
                                    </a>
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php if($_category_config['addtocompare']): ?>
                                <?php
                                $compareHelper = $this->helper('Magento\Catalog\Helper\Product\Compare');
                                ?>
                                <a href="#"
                                   class="action tocompare actions-secondary <?php if(isset($_category_config['qty_field']) && $_category_config['qty_field']):?>has-qty<?php endif;?>"
                                   title="<?php echo $block->escapeHtml(__('Add to Compare')); ?>"
                                   aria-label="<?php echo $block->escapeHtml(__('Add to Compare')); ?>"
                                   data-post='<?php echo $compareHelper->getPostDataParams($_product); ?>'
                                   role="button">
                                    <span><?php echo __('Add to Compare') ?></span>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php echo($iterator == count($_productCollection)+1) ? '</li>' : '' ?>
            <?php endforeach; ?>
        </ol>
    </div>
    <?php if(isset($_category_grid_config['infinite_scroller']) && $_category_grid_config['infinite_scroller']):?>
    <div class="infinite-loader"><div class="loading"><?php echo __("Loading ..."); ?></div><a href="javascript:void(0)" class="btn-load-more"><?php echo __("Load More ..."); ?></a></div>
    <?php endif;?>
    <?php echo $block->getToolbarHtml() ?>
    <?php if (!$block->isRedirectToCartEnabled()) : ?>
        <script type="text/x-magento-init">
        {
            "[data-role=tocart-form], .form.map.checkout": {
                "catalogAddToCart": {}
            }
        }
        </script>
    <?php endif; ?>
    <?php if ($block->getMode() == 'grid'): ?>
        <script type="text/javascript">
            require([
                'jquery'        
            ], function ($) {
                $('.main .products.grid .product-items li.product-item:nth-child(2n)').addClass('nth-child-2n');
                $('.main .products.grid .product-items li.product-item:nth-child(2n+1)').addClass('nth-child-2np1');
                $('.main .products.grid .product-items li.product-item:nth-child(3n)').addClass('nth-child-3n');
                $('.main .products.grid .product-items li.product-item:nth-child(3n+1)').addClass('nth-child-3np1');
                $('.main .products.grid .product-items li.product-item:nth-child(4n)').addClass('nth-child-4n');
                $('.main .products.grid .product-items li.product-item:nth-child(4n+1)').addClass('nth-child-4np1');
                $('.main .products.grid .product-items li.product-item:nth-child(5n)').addClass('nth-child-5n');
                $('.main .products.grid .product-items li.product-item:nth-child(5n+1)').addClass('nth-child-5np1');
                $('.main .products.grid .product-items li.product-item:nth-child(6n)').addClass('nth-child-6n');
                $('.main .products.grid .product-items li.product-item:nth-child(6n+1)').addClass('nth-child-6np1');
                $('.main .products.grid .product-items li.product-item:nth-child(7n)').addClass('nth-child-7n');
                $('.main .products.grid .product-items li.product-item:nth-child(7n+1)').addClass('nth-child-7np1');
                $('.main .products.grid .product-items li.product-item:nth-child(8n)').addClass('nth-child-8n');
                $('.main .products.grid .product-items li.product-item:nth-child(8n+1)').addClass('nth-child-8np1');
            });
        </script>
    <?php endif; ?>
<?php endif; ?>
<script type="text/javascript">
    require([
        'jquery'
    ], function ($) {
      var app = {
          isAppleDevice: function() {
            if (navigator.userAgent.match(/(iPhone|iPod|iPad|Safari)/) != null) {
              return true;
            }
            return false;
          }
      }
    // Timer for LEFT time for Dailydeal product
        var _second = 1000;
        var _minute = _second * 60;
        var _hour = _minute * 60;
        var _day = _hour * 24;
        var timer;

        //Set date as magentodatetime
        var date = new Date('<?php echo $_dailydealhelper->getcurrentDate() ?>');
        if (app.isAppleDevice()) {
          var mdate = '<?php echo $_dailydealhelper->getcurrentDate() ?>';
          var dateParts = mdate.substring(0,10).split('-');
          var timePart = mdate.substr(11);
          date = dateParts[1] + '/' + dateParts[2] + '/' + dateParts[0] + ' ' + timePart;
          date = new Date(date);
        }
        var l_date = new Date();
        var offset_date = l_date - date;

        function showRemaining()
        {
            $(".sw-dailydeal-wrapper").each(function(){
                var unique_id = $(this).attr("data-unique-id");
                // get Value of dailydeal product
                var cid='countdown_'+unique_id;
                var daysid='countdown_days_'+unique_id;
                var hoursid='countdown_hours_'+unique_id;
                var minutesid='countdown_minutes_'+unique_id;
                var secondsid='countdown_seconds_'+unique_id;

                var startdateid='fromdate_'+unique_id;
                var id='todate_'+unique_id;

                var enddate = new Date($('#'+id).val());
                var dealstartdate=new Date($('#'+startdateid).val());
                if (app.isAppleDevice() && $('#'+id).val() && $('#'+startdateid).val()) {
                  var ledate = $('#'+id).val();
                  var ledateParts = ledate.substring(0,10).split('-');
                  var letimePart = ledate.substr(11);
                  enddate = ledateParts[1] + '/' + ledateParts[2] + '/' + ledateParts[0] + ' ' + letimePart;
                  enddate = new Date(enddate).getTime();

                  var lsdate = $('#'+startdateid).val();
                  var lsdateParts = lsdate.substring(0,10).split('-');
                  var lstimePart = lsdate.substr(11);
                  dealstartdate = lsdateParts[1] + '/' + lsdateParts[2] + '/' + lsdateParts[0] + ' ' + lstimePart;
                  dealstartdate = new Date(dealstartdate).getTime();
                }
                var currentdate=new Date();

                //Get Difference between Two dates
                var distance = enddate - (currentdate - offset_date);

                $('.sw-dailydeal-wrapper').show();

                if (distance < 0) {
                   // clearInterval(timer);
                    $('#expired_'+unique_id).html("<span style='font-size:25px; color:#000;'>EXPIRED!<span>");

                } else if(dealstartdate > currentdate) {
                   $('.countdowncontainer_'+unique_id).hide();
                   var msg="<span style='font-size:15px; color:#000;'> Coming Soon..<br>Deal Start at:<br>"+$('#'+startdateid).val()+"<span>";
                   $('#expired_'+unique_id).html(msg);
                } else {
                    var days = Math.floor(distance / _day);
                    var hours = Math.floor((distance % _day) / _hour);
                    var minutes = Math.floor((distance % _hour) / _minute);
                    var seconds = Math.floor((distance % _minute) / _second);

                    if(hours < 10)
                        hours = "0" + hours;
                    if(minutes < 10)
                        minutes = "0" + minutes;
                    if(seconds < 10)
                        seconds = "0" + seconds;
                    $('.countdowncontainer_'+unique_id).show();
                    $('#'+daysid).html(days);
                    $('#'+hoursid).html(hours);
                    $('#'+minutesid).html(minutes);
                    $('#'+secondsid).html(seconds);
                }
            });
        }

        // Set Interval
        timer = setInterval(function()
        {
            showRemaining();
        }, 1000);
    });
    </script>
<?php
    echo $block->getChildHtml('filter_toggle');
?>
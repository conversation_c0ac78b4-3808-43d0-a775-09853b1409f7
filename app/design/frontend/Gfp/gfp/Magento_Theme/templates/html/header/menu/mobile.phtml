<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;
use CopeX\HyvaTheme\ViewModel\Cms;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class, $block);
/** @var Cms $blockViewModel */
$blockViewModel = $viewModels->require(Cms::class);

$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

?>
<nav
    x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
    @load.window="setActiveMenu($root)"
    @keydown.window.escape="closeMenu()"
    class="z-20 order-2 sm:order-1 xl:order-2 navigation xl:hidden w-12 h-12 sm:h-0"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation')) ?>"
    role="navigation"
>
    <!-- mobile -->
    <button
        id="mobile-header-menu"
        x-ref="mobileMenuTrigger"
        @click="openMenu()"
        class="hidden"
    >
        <?= $heroicons->menuHtml('p-3', 48, 48) ?>
    </button>
    <div
        x-ref="mobileMenuNavLinks"
        class="
            fixed top-9 right-0 w-full h-full p-1 hidden
            flex-col bg-container-lighter
            overflow-y-auto overflow-x-hidden px-4
        "
        :class="{ 'flex': open, 'hidden': !open }"
        :aria-hidden="open ? 'false' : 'true'"
        role="dialog"
        aria-modal="true"
    >
        <div x-data="{xsVisible: false}" x-defer="intersect" x-init="hyva.updateContentFromTemplates($el)">
            <div class="mt-28 text-base font-bold text-primary uppercase py-2 px-8 w-full bg-gfpgreen-lighter rounded-full flex flex-row items-center place-content-between" x-on:click="xsVisible = ! xsVisible">
                <?= $escaper->escapeHtmlAttr(__('Products')) ?>
                <div class="">
                    <svg :class="{'rotate-180' : xsVisible}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6 transform ease-linear duration-300">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                </div>
            </div>
            <ul
                class="flex flex-col gap-y-1 border-b border-b-2 border-gfpgreen-lighter py-2"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation links')) ?>"
                :class="{'hidden' : xsVisible}"
            >
                <?php foreach ($menuItems as $index => $menuItem): ?>
                    <li
                        data-child-id="<?= $escaper->escapeHtmLAttr($index) ?>-main"
                        class="level-0"
                    >
                        <div
                            class="flex items-center transition-transform duration-150 ease-in-out transform"
                            :class="{
                                '-translate-x-full' : mobilePanelActiveId,
                                'translate-x-0' : !mobilePanelActiveId
                            }"
                        >
                            <a
                                class="flex items-center w-full px-8 py-4 cursor-pointer
                                    bg-container-lighter level-0
                                "
                                href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                            >
                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                            </a>
                            <?php if (!empty($menuItem['childData'])): ?>
                                <button
                                    @click="openSubcategory('<?= /* @noEscape */ $index ?>')"
                                    class="absolute right-0 flex items-center justify-center w-11 h-11 mr-8 cursor-pointer
                                    bg-container-lighter border-container"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Open %1 subcategories', $menuItem['name'])) ?>"
                                    aria-haspopup="true"
                                    :aria-expanded="mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>'"
                                >
                                    <div class="w-8 h-8 border rounded">
                                        <?= $heroicons->chevronRightHtml('w-full h-full p-1', 24, 24, ["aria-hidden" => "true"]) ?>
                                    </div>
                                </button>
                            <?php endif; ?>
                        </div>
                        <?php if (!empty($menuItem['childData'])): ?>
                            <div
                                data-child-id="<?= $escaper->escapeHtmLAttr($index) ?>"
                                class="absolute top-0 right-0 z-10 flex flex-col gap-1 w-full h-full p-1 bg-container-lighter"
                                :class="{
                                    'hidden': mobilePanelActiveId !== '<?= /* @noEscape */ (string) $index ?>'
                                }"
                            >
                                <ul
                                    class="mt-16 transition-transform duration-200 ease-in-out translate-x-full transform"
                                    :class="{
                                        'translate-x-full' : mobilePanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                                        'translate-x-0' : mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>',
                                    }"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Subcategories')) ?>"
                                >
                                    <li>
                                        <button
                                            type="button"
                                            class="flex items-center px-8 py-4 cursor-pointer bg-container border-container w-full"
                                            @click="backToMainCategories('<?= /* @noEscape */ $index ?>-main')"
                                            aria-label="<?= $escaper->escapeHtmlAttr(__('Back to main categories')) ?>"
                                        >
                                            <?= $heroicons->chevronLeftHtml('', 24, 24, ["aria-hidden" => "true"]); ?>
                                            <span class="ml-4">
                                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                                            </span>
                                        </button>
                                    </li>
                                    <li>
                                        <a
                                            href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                            title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                                            class="flex items-center w-full px-8 py-4 cursor-pointer
                                                bg-container-lighter
                                            "
                                        >
                                            <span class="ml-10">
                                                <?= $escaper->escapeHtml(__('View All')) ?>
                                            </span>
                                        </a>
                                    </li>
                                    <?php foreach ($menuItem['childData'] as $subMenuItem): ?>
                                        <li>
                                            <a
                                                href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                                title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                                class="flex items-center w-full px-8 py-4 cursor-pointer
                                                    bg-container-lighter
                                                "
                                            >
                                                <span class="ml-10 text-base text-gray-700">
                                                    <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                                </span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                                <button
                                    @click="closeMenu()"
                                    class="absolute flex justify-end w-16 self-end mb-1 transition-none"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
                                >
                                    <?= $heroicons->xHtml('hidden p-4', 64, 64, [":class" => "{ 'hidden' : !open, 'block': open }", "aria-hidden" => "true"]) ?>
                                </button>
                            </div>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        <!--Header Service Menu Mobile -->
        <div class="flex flex-col w-full pt-5">
            <?= $blockViewModel->renderBlockByIdentifier('header-service-menu'); ?>
        </div>
        <div class="flex flex-row absolute w-full items-center pr-9 pt-6">
            <button
                @click="closeMenu()"
                class="flex justify-start w-16 self-start mb-1"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
                type="button"
            >
                <?= $heroicons->xHtml('hidden p-2 w-12 h-12', 24, 24, [":class" => "{ 'hidden' : !open, 'block': open }", "aria-hidden" => "true"]) ?>
            </button>
            <div class="w-full flex justify-center">
                <img class="h-8"
                     src="<?= $this->getViewFileUrl('images/gfplogo.png'); ?>"
                     alt="<?= $escaper->escapeHtmlAttr($block->getLogoAlt() ? $block->getLogoAlt() : __('Store logo')) ?>"
                />
            </div>

        </div>
    </div>
</nav>
<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            mobilePanelActiveId: null,
            open: false,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('underline');
                    item.closest('li.level-0') &&
                    item.closest('li.level-0').querySelector('a.level-0').classList.add('underline');
                });
            },
            openMenu() {
                this.open = true
                this.$nextTick(() => hyva.trapFocus(this.$refs['mobileMenuNavLinks']));
                // Prevent from body scrolling while mobile menu opened
                document.body.style.overflow = 'hidden';
            },
            closeMenu() {
                document.body.style.overflow = '';

                if (this.open) {
                    this.$nextTick(() => this.$refs['mobileMenuTrigger'].focus() || hyva.releaseFocus());
                }

                this.open = false
                this.mobilePanelActiveId = null
            },
            openSubcategory(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']')
                this.mobilePanelActiveId = this.mobilePanelActiveId === index ? 0 : index
                this.$nextTick(() => hyva.trapFocus(menuNodeRef))
            },
            backToMainCategories(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']')
                this.mobilePanelActiveId = 0
                this.$nextTick(() => {
                    hyva.trapFocus(this.$refs['mobileMenuNavLinks'])
                    menuNodeRef.querySelector('a').focus()
                })
            }
        }
    }
</script>

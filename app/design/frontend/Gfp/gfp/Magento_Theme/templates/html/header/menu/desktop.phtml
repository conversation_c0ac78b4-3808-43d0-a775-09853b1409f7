<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class, $block);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

?>
<div x-data="initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?>()"
     class="z-20 order-2 sm:order-1 xl:order-2 navigation hidden xl:flex bg-green-lighter rounded-full w-full mx-auto"
>
    <!-- desktop -->
    <div x-ref="nav-desktop"
         @load.window="setActiveMenu($root)"
         class="hidden xl:block xl:relative xl:min-h-0 xl:w-auto xl:pt-0">
        <nav
            class="duration-150 ease-in-out transform w-auto relative min-h-0 transition-display"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Main menu')) ?>"
        >
            <ul class="flex flex-col xl:flex-row flex-wrap justify-left py-3">
                <?php foreach ($menuItems as $index => $menuItem): ?>
                    <li class="relative level-0 group"
                        @mouseenter="hoverPanelActiveId = '<?= /* @noEscape */ (string) $index ?>'"
                        @mouseleave="hoverPanelActiveId = 0"
                        @keyup.escape="hoverPanelActiveId = 0"
                    >
                        <span class="flex items-center px-3 text-md bg-opacity-95">
                            <a class="w-full py-1 px-3 text-lg font-medium leading-normal level-0 group-hover:outline group-hover:outline-1 group-hover:outline-primary rounded-full whitespace-nowrap"
                               href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                               title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                               @focus="hoverPanelActiveId = 0"
                            >
                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                            </a>
                            <?php if (!empty($menuItem['childData'])): ?>
                                <button
                                    type="button"
                                    data-sr-button-id="<?= $escaper->escapeHtmlAttr($index) ?>"
                                    :aria-expanded="hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>' ? 'true' : 'false'"
                                    @click="openMenuOnClick('<?= /* @noEscape */ (string) $index ?>')"
                                >
                                    <?= $heroiconsSolid->chevronDownHtml("flex self-center h-5 w-5 hidden", 25, 25, ['aria-hidden' => 'true']) ?>
                                    <span class="sr-only">
                                        <?= $escaper->escapeHtml(__('Show submenu for %1 category', $menuItem['name'])) ?>
                                    </span>
                                </button>
                            <?php endif; ?>
                        </span>
                        <?php if (!empty($menuItem['childData'])): ?>
                            <ul
                                class="absolute z-10 hidden px-6 py-4 -ml-6 shadow-lg bg-container-lighter/95"
                                :class="{
                                    'hidden' : hoverPanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                                    'block' : hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>'
                                }"
                            >
                                <?php foreach ($menuItem['childData'] as $subMenuItem): ?>
                                    <li>
                                        <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                           title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                           class="block w-full px-3 py-1 my-1 whitespace-nowrap first:mt-0 hover:underline"
                                           @keyup.escape="$nextTick(() => document.querySelector('[data-sr-button-id=<?= $escaper->escapeJs($index) ?>]').focus())"
                                        >
                                            <span class="text-base text-gray-700">
                                                <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                            </span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </nav>
    </div>
</div>
<script>
    'use strict';

    const initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            hoverPanelActiveId: null,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.setAttribute('aria-current', 'page');
                    item.closest('li.level-0').setAttribute('data-active', 'true');
                });
            },
            openMenuOnClick(menuNode) {
                if (menuNode === this.hoverPanelActiveId) {
                    this.hoverPanelActiveId = 0;
                } else {
                    this.hoverPanelActiveId = menuNode
                }
            }
        }
    }
</script>

<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Framework\Escaper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var \CopeX\HyvaTheme\ViewModel\Cms $heroiconsSolid */
$cmsViewModel = $viewModels->require(\CopeX\HyvaTheme\ViewModel\Cms::class);

/** @var Magento\Catalog\Model\Product $product */
$product = $block->getData('product');
$imageDisplayArea = $block->getData('image_display_area');
$templateType = $block->getData('template_type');
$viewMode = $block->getData('view_mode');
$showDescription = $block->getData('show_description');
$showAddToWishlist = false;//$wishlistViewModel->isEnabled();
$showAddToCompare = false;//$compareViewModel->showInProductList();
$viewIsGrid = $viewMode === 'grid';


if (!$product) {
    return '';
}
$productId = $product->getId();
$uniqueId = '_' . uniqid();

$hideDetails       = $block->getData('hide_details') ?: false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?: false;
?>
<div class="item product product-item product_addtocart_form border rounded-md px-0 flex flex-col h-full w-full <?= $viewIsGrid ? '' : 'md:flex-row' ?>">
    <div class="related-product-item-info break-words">
        <?php // Product Image ?>
        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
           class="product photo product-item-photo block mx-auto text-center mb-3 <?= $viewIsGrid ? '' : 'md:w-2/6 md:mb-0 md:mr-5' ?>"
           tabindex="-1"
        >
            <?= $block->getImage($product, $imageDisplayArea)
                ->setTemplate('CopeX_Swiper::slider/related-image.phtml')
                ->setProductId($productId)
                ->toHtml(); ?>
        </a>
        <?php $productNameStripped = $block->stripTags($product->getName(), null, true); ?>
        <div class="mt-2 mb-1 items-center justify-center text-primary font-semibold text-lg text-center <?= $viewIsGrid ? '' : 'md:text-left' ?>">
            <a class="product-item-link"
               href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>">
                <?= /* @noEscape */ $catalogOutputHelper->productAttribute($product, $product->getName(), 'name') ?>
            </a>
        </div>

        <?php if (!$hideRatingSummary): ?>
            <div class="py-1 mx-auto <?= $viewIsGrid ? '' : 'md:mx-0 md:w-auto' ?>">
                <?= $block->getReviewsSummaryHtml($product, 'short') ?>
            </div>
        <?php endif; ?>

        <?php if ($product->isAvailable() && !$hideDetails): ?>
            <?= $block->getProductDetailsHtml($product) ?>
        <?php endif; ?>

        <?php if ($showDescription && $product->getShortDescription()): ?>
            <div class="mt-2 mb-1 items-center justify-center text-primary text-center <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                <?= /* @noEscape */ $cmsViewModel->renderBlockContent($product->getShortDescription() ?? '') ?>
            </div>
        <?php endif; ?>
    </div>
    <div class="mt-auto related-product-info flex flex-col">


        <script>
            function initPriceBox_<?= /** @noEscape */ $uniqueId ?>()
            {
                return {
                    updatePrice(priceData) {
                        const regularPriceLabel = this.$el.querySelector('.normal-price .price-label');
                        const regularPriceElement = this.$el.querySelector('.normal-price .price-wrapper .price');
                        if (priceData.finalPrice.amount < priceData.oldPrice.amount) {
                            regularPriceLabel.classList.add('hidden');
                        } else {
                            regularPriceLabel.classList.remove('hidden');
                        }
                        regularPriceElement.innerText = hyva.formatPrice(priceData.finalPrice.amount);
                    }
                }
            }
        </script>
        <div class="pt-1 pr-1 text-gray-900"
             x-data="initPriceBox_<?= /** @noEscape */ $uniqueId ?>()"
             @update-prices-<?= (int)$productId ?>.window="updatePrice($event.detail);"
        >
            <?= /* @noEscape */ $productListItemViewModel->getProductPriceHtml($product) ?>
        </div>

        <div class="pt-3">
            <a class="btn btn-primary w-full rounded-t-none rounded-b-md flex justify-center pr-0 pt-0 h-8" href="<?= $escaper->escapeUrl($product->getUrl()) ?>">
                <span><?= $escaper->escapeHtml(__('to the product')) ?></span>
            </a>
            <?php if ($showAddToWishlist): ?>
                <button x-data="initWishlist()"
                        @click.prevent="addToWishlist(<?= (int)$productId ?>)"
                        aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Wish List')) ?>"
                        type="button"
                        class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex flex-shrink-0 items-center justify-center text-gray-500 hover:text-red-600 ml-2">
                    <?= $heroiconsSolid->heartHtml("w-5 h-5", 25, 25) ?>
                </button>
            <?php endif; ?>
            <?php if ($showAddToCompare): ?>
                <button x-data="initCompareOnProductList()"
                        @click.prevent="addToCompare(<?= (int)$productId ?>)"
                        aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Compare')) ?>"
                        type="button"
                        class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex flex-shrink-0 items-center justify-center text-gray-500 hover:text-yellow-500 ml-2">
                    <?= $heroicons->scaleHtml("w-5 h-5", 25, 25) ?>
                </button>
            <?php endif; ?>
            <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                <?= $addToBlock->setProduct($product)->getChildHtml() ?>
            <?php endif; ?>
        </div>
    </div>
</div>

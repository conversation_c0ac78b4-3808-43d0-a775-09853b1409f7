<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\ConfigurableProduct\Pricing\Render\FinalPriceBox $block */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $priceModel */
/** @var \Magento\Framework\Escaper $escaper */
$priceModel = $block->getPriceType('regular_price');
/** @var \Magento\Framework\Pricing\Price\PriceInterface $finalPriceModel */
$finalPriceModel = $block->getPriceType('final_price');
$idSuffix = $block->getIdSuffix() ? $block->getIdSuffix() : '';
$schema = ($block->getZone() == 'item_view') ? true : false;

/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
$request = $viewModels->require(\CopeX\HyvaTheme\ViewModel\Request::class);
$isListView = $request->isListView();

/** @var Xortex\App\Catalog\Helper\Price $priceHelper */
$priceHelper = $this->helper('Xortex\App\Catalog\Helper\Price');

?>
<?php if ($finalPriceModel->getAmount()->getValue() !== null): ?>
    <?php if ($block->hasSpecialPrice()): ?>
        <span class="old-price sly-old-price <?= ($isListView ? "text-left" : "text-right") ?>">
            <?php /* @escapeNotVerified */ echo $block->renderAmount($priceModel->getAmount(), [
                'display_label'     => __('Regular Price'),
                'price_id'          => $block->getPriceId('old-price-' . $idSuffix),
                'price_type'        => 'oldPrice',
                'include_container' => true,
                'skip_adjustments'  => true
            ]); ?>
        </span>
    <?php endif; ?>

    <span class="normal-price product-price-decorated <?= ($isListView ? "text-left" : "text-right") ?>">
        <?php
            $arguments = [
                'display_label' => __(''),
                'price_id' => $block->getPriceId('product-price-' . $idSuffix),
                'price_type' => 'finalPrice',
                'include_container' => true,
                'schema' => $schema
            ];

            /* @noEscape */ echo $block->renderAmount($finalPriceModel->getAmount(), $arguments);
        ?>
    </span>

    <?php if (!$isListView && $block->hasSpecialPrice()) : ?>
    <div class="price-container">
        <span class="save-info-block text-gfpgreen-lighter" data-role="save-info-block">
            <?php echo $priceHelper->getSaveInfo($finalPriceModel->getAmount()->getValue(), $priceModel->getAmount()->getValue()); ?>
        </span>
    </div>
    <?php endif; ?>
    <?php if ($block->hasSpecialPrice()) : ?>
        <span>
                <span class=" <?= ($isListView ? "text-left" : "text-center") ?> flex flex-row w-full">
                    <span class="whitespace-nowrap text-xs"><?= __("30 days best price: ") ?></span>
                    <span id="product-price-<?= (int)$block->getIdSuffix() ?>" class="price-wrapper text-xs ml-0.5">
                        <?php /* @escapeNotVerified */ echo $block->renderAmount($finalPriceModel->getAmount(), [
                            'display_label'     => false,
                            'price_id'          => $block->getPriceId('old-price-' . $idSuffix),
                            'price_type'        => '',
                            'include_container' => false,
                            'skip_adjustments'  => true
                        ]); ?>
                    </span>
                </span>
            </span>
    <?php endif; ?>
    <?php $taxInfoBlock = $block->getLayout()->createBlock('Magento\Cms\Block\Block')->setBlockId('pricetaxinfo')->toHtml(); ?>
    <?php if($taxInfoBlock && false) {
        echo "<div class='price-tax-info'>" . $taxInfoBlock . "</div>";
    }
endif; ?>

<?php if ($block->showMinimalPrice()): ?>
    <?php if ($block->getUseLinkForAsLowAs()):?>
        <a href="<?= $escaper->escapeUrl($block->getSaleableItem()->getProductUrl()) ?>" class="minimal-price-link">
            <?= /* @noEscape */ $block->renderAmountMinimal() ?>
        </a>
    <?php else:?>
        <span class="minimal-price-link">
            <?= /* @noEscape */ $block->renderAmountMinimal() ?>
        </span>
    <?php endif?>
<?php endif; ?>

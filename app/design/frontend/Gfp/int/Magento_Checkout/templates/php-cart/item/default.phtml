<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Modal;
/**
 * @var $block \Magento\Checkout\Block\Cart\Item\Renderer
 * @var $escaper \Magento\Framework\Escaper
 */

/** @var ViewModelRegistry $viewModels */
/** @var \Mstage\CWPicker\ViewModel\Renderer $renderer */
/** @var Modal $heroicons */
$heroicons = $viewModels->require(HeroiconsSolid::class);

$renderer    = $viewModels->require(\Mstage\CWPicker\ViewModel\Renderer::class);

/** @var \Hyva\Theme\ViewModel\StoreConfig $storeConfig */
$storeConfig = $viewModels->require(\Hyva\Theme\ViewModel\StoreConfig::class);
$showDatePicker = $storeConfig->getStoreConfig("checkout/cwpicker/show");
$_item = $block->getItem();
$product = $_item->getProduct();

$isVisibleProduct = $product->isVisibleInSiteVisibility();
/** @var \Magento\Msrp\Helper\Data $helper */
$helper = $this->helper('Magento\Msrp\Helper\Data');

$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$priceHelper = $objectManager->create('Magento\Framework\Pricing\Helper\Data');

$canApplyMsrp = $helper->isShowBeforeOrderConfirm($product) && $helper->isMinimalPriceLessMsrp($product);

$isZubehoer = $renderer->isZubehoer($_item);
$isLastItemOfGroup = $renderer->isLastItemOfGroup($_item);
$isFirstAttachmentItemOfGroup = $renderer->isFirstAttachmentItemOfGroup($_item);
$lastWeekOfYear = $renderer->getLastWeekOfYear();
$currentDeliveryDay = $_item->getDeliveryDay();
$formattedCurrentDeliveryDay = $renderer->formatDeliveryDate($currentDeliveryDay);
$currentDeliveryWeek = $renderer->getDeliveryWeek($currentDeliveryDay);
$productGroup = $renderer->getProductGroup($_item);

$showCWPicker = $_item->getIsFirstItemOfGroup();

//get the main item ID - for Zubehoer, that are in the same group as at least one normal product, this is the ID of that normal product, that has the earliest delivery day
$mainItemId = $renderer->getMainItemId($_item);
$mainItem = $renderer->getMainItem($_item);
$firstPossibleDeliveryDay = $renderer->getFirstPossibleDeliveryDay($_item);
$lastPossibleDeliveryDay = $renderer->getLastPossibleDeliveryDate($firstPossibleDeliveryDay);
?>

<?php if ($isFirstAttachmentItemOfGroup): ?>
    <div class="col-start-1 col-end-2"></div>
    <div class="col-start-1 col-end-7 font-medium text-lg md:text-base text-gfpgray pt-5 md:pt-12 md:pt-2.5 border-gray-300 border-solid border-collapse box-border pl-3">
        <?= $escaper->escapeHtml(__('You have chosen the following accessories')); ?>
    </div>
<?php else: ?>
    <?php if ($showCWPicker === true ): ?>
            <div id="artikel-start-1" class="col-start-1 col-end-7 border-b-2 md:border-b-0 md:border-t border-l border-r gap-x-3 md:gap-x-0 relative py-5 px-3 md:px-5 grid md:grid-flow-row-dense overflow-hidden cart items data table w-full cwpicker-group-wrapper bg-white md:rounded-t-xl">
                <div class="col-start-1 col-end-3 text-black font-semibold hidden md:block"><span><?= $escaper->escapeHtml(__('Item')) ?></span></div>
                <div class="col-start-3 col-end-4 text-black font-semibold col qty text-right hidden md:block pl-2" scope="col"><span><?= $escaper->escapeHtml(__('Qty')) ?></span></div>
                <div class="col-start-4 col-end-5 text-black font-semibold col price text-right hidden md:block pl-2" scope="col"><span><?= $escaper->escapeHtml(__('Price')) ?></span></div>
                <div class="col-start-5 col-end-6 text-black font-semibold col subtotal text-right hidden md:block pl-2" scope="col"><span><?= $escaper->escapeHtml(__('Total')) ?></span></div>
                <div class="col-start-6 col-end-7 hidden md:block"></div>
    <?php endif; ?>
<?php endif; ?>

    <?php if (!$isZubehoer): ?>
        <div class="row-span-4 md:row-span-2 col-start-1 pt-2  <?= ($showCWPicker === true) ? 'md:border-t': 'border-t' ?>" data-th="<?= $escaper->escapeHtml(__('Item')); ?>">
        <?php if ($block->hasProductUrl()): ?>
            <a href="<?= $block->getProductUrl() ?>"
                title="<?= $escaper->escapeHtml($block->getProductName()) ?>"
                tabindex="-1"
                class="product-item-photo">
        <?php else: ?>
            <span class="product-item-photo">
        <?php endif; ?>
        <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail')->toHtml(); ?>
        <?php if ($block->hasProductUrl()): ?>
            </a>
        <?php else: ?>
            </span>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    <div class="flex flex-row justify-between relative md:block mt-1 pt-1 md:mt-0 md:pt-0 title-mob col-start-1 col-end-7 md:col-end-3 md:row-span-2 <?php if ($isZubehoer === false) { echo 'col-start-2 pt-2'; } ?> md:pl-6 <?= ($showCWPicker === true) ? 'md:border-t': 'border-t' ?>">
        <?php if (!$isZubehoer): ?>
            <div class="product-item-details">
                <strong class="product-item-name text-base font-medium md:font-semibold">
                <?php if ($block->hasProductUrl()): ?>
                    <a href="<?= $block->getProductUrl() ?>"><?= $escaper->escapeHtml($block->getProductName()) ?></a>
                <?php else: ?>
                    <?= $escaper->escapeHtml($block->getProductName()) ?>
                <?php endif; ?>
                </strong>
                <?php if ($_options = $block->getOptionList()): ?>
                    <dl class="item-options leading-snug text-xs text-gfpgray md:text-base md:leading-normal pb-2.5">
                        <?php foreach ($_options as $_option) : ?>
                            <?php $_formatedOptionValue = $block->getFormatedOptionValue($_option) ?>
                            <dd>
                                <?php if (isset($_formatedOptionValue['full_view'])): ?>
                                    <?= $_formatedOptionValue['full_view'] ?>
                                <?php else: ?>
                                    <?= $_formatedOptionValue['value'] ?>
                                <?php endif; ?>
                            </dd>
                        <?php endforeach; ?>
                        <?php if ($_item->getProduct()->getData('length')): ?>
                            <dd>
                                <?= $_item->getProduct()->getResource()->getAttribute('length')->getFrontendLabel(); ?>
                                :
                                <?= $_item->getProduct()->getData('length'); ?>
                            </dd>
                        <?php endif; ?>
                            </dl>
                <?php endif; ?>
                <?php if ($messages = $block->getMessages()): ?>
                    <?php foreach ($messages as $message): ?>
                        <div class="cart item message <?= $message['type'] ?>">
                            <div><?= $escaper->escapeHtml($message['text']) ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
                <?php if ($addInfoBlock): ?>
                    <?= $addInfoBlock->setItem($_item)->toHtml() ?>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <span class="text-base font-medium text-black"><?= $escaper->escapeHtml($block->getProductName()) ?></span>
        <?php endif ?>
        <?php if ($isZubehoer): ?>
            <div class="md:hidden absolute item-actions align-top self-center" style="right: 5px; top: 25px;" data-th="<?= $escaper->escapeHtml(__('Aktion')); ?>">
                <div class="cart-actions-toolbar text-left md:text-right text-gfpgray">
                    <?= $block->getActions($_item) ?>
                </div>
            </div>
        <?php else: ?>
            <div class="md:hidden absolute item-actions align-top self-center" style="right: 4px; bottom: -84px;" data-th="<?= $escaper->escapeHtml(__('Aktion')); ?>">
                <div class="cart-actions-toolbar text-left md:text-right text-gfpgray">
                    <?= $block->getActions($_item) ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?php if (!$isZubehoer): ?>
        <div class="md:hidden mb-4 <?= $mainItemId . ' ' . 'group-' . $productGroup; ?> col-start-2 col-end-7 text-xs " x-data="initDeliveryDateChooser({'formattedDate':'<?= $formattedCurrentDeliveryDay; ?>', 'productGroup': '<?= $productGroup ?>', 'itemId': '<?= $mainItemId ?>'})">
            <div class="text_begin mb-2 text-xs" style="color:#85b84b;" x-on:click.prevent="toggleDeliveryDateChooser()"><?= __('Delivery expected on'); ?><?= __(' ');?><?= $formattedCurrentDeliveryDay ?></div>
            <span class="<?= 'item-' . $mainItemId; ?>">
                <a id="<?= "later-delivery-week-" . $mainItemId; ?>-link" x-on:click.prevent="toggleDeliveryDateChooser()"
                   class="cwpicker-delivery-text text_end later-delivery-week whitespace-nowrap py-2.5"><i class="fas mgz-fa-calendar-day text-xs"> </i><span class="underline pl-2 text-xs"><?= __('Click here to change the date') ?></span></a>
                <div class="delivery-date-container block md:inline sm:pl-2 sm:whitespace-nowrap" id="<?= "delivery-date-" . $mainItemId; ?>" x-show="showDeliveryDateChooser" x-cloak>
                    <input type="date" id="<?= "delivery-date-item-" . $mainItemId; ?>-mobile"
                           class="cwpicker-input delivery-date text-lg text-gfpgray py-0 px-1 text-center" min="<?= $firstPossibleDeliveryDay; ?>" max="<?= $lastPossibleDeliveryDay; ?>"
                           value="<?= $currentDeliveryDay; ?>" @change="processChange($event)" onclick="this.showPicker()">
                    <label class='later-delivery-week inline md:pl-2 text-black text-xs whitespace-nowrap'><?= __('Please click on the date field') ?></label>
                </div>
            </span>
        </div>
    <?php endif; ?>

    <?php if ($canApplyMsrp): ?>
        <div class="col-start-2 col-end-7 md:col-start-4 md:col-end-5 md:row-span-2 col msrp text-right hidden" data-th="<?= $escaper->escapeHtml(__('Price')); ?>">
            <span class="pricing msrp">
                <span class="msrp notice"><?= __('See price before order confirmation.'); ?></span>
                <?php $helpLinkId = 'cart-msrp-help-' . $_item->getId(); ?>
                <a href="#" class="action help map" id="<?= ($helpLinkId); ?>"
                       data-mage-init='{"addToCart":{"helpLinkId": "#<?= $helpLinkId; ?>","productName": "<?= $product->getName(); ?>","showAddToCart": false}}'>
                    <span><?= __("What's this?"); ?></span>
                </a>
            </span>
        </div>
    <?php else: ?>
        <div class="flex flex-row hidden md:block justify-items-start md:justify-items-end col-start-2 col-end-7 md:col-start-4 md:col-end-5 md:row-span-2 col price text-right text-sm md:text-base md:border-t pl-3 md:pl-5" data-th="<?= $escaper->escapeHtml(__('Price')); ?>">
            <div class="md:hidden pr-6"><?= $escaper->escapeHtml(__('Unit Price')) ?> (<?= $escaper->escapeHtml(__('incl. TAX')) ?>)</div>
            <div class="w-fit md:w-full text-right leading-snug md:leading-normal">
                <span class="md:text-lg text-black">
                    <?= $block->getUnitPriceHtml($_item); ?>
                </span>
                <?php if ($_item->getProduct()->getFinalPrice() && $_item->getProduct()->getPrice() && $_item->getProduct()->getFinalPrice() < $_item->getProduct()->getPrice()) {
                    $oldPrice = $_item->getProduct()->getPrice();
                    ?>
                    <span class="save-info flex flex-row justify-end text-sm text-gfpgray">
                        <span class="price line-through ml-0.5"><?= $priceHelper->currency($oldPrice, true, false) ?></span>
                    </span>
                    <?php
                } ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="hidden md:block basket-mob col col-start-1 md:col-start-6 md:col-end-7 md:row-span-2 item-actions align-top py-1.5 pl-2 md:pl-5 md:border-t self-center md:self-start" data-th="<?= $escaper->escapeHtml(__('Aktion')); ?>">
        <div class="cart-actions-toolbar text-left md:text-right text-gfpgray">
            <?= $block->getActions($_item) ?>
        </div>
    </div>

    <div class="mobile-item-qty col-qty col-start-2 col-end-7 md:col-start-3 md:col-end-4 md:row-span-2 col qty md:text-right md:border-t md:pl-5" style="margin-left: 0px;" data-th="<?= $escaper->escapeHtml(__('Qty')); ?>">
        <div class="field qty"
             x-data="{ qty_counter: <?= $escaper->escapeHtmlAttr($block->getQty()) ?>, item_id: '<?= $escaper->escapeHtmlAttr($_item->getId()) ?>',
                       callChange: ()=>{ document.getElementById('cart-<?= $escaper->escapeHtmlAttr($_item->getId()) ?>-qty').dispatchEvent(new Event('change')) }}">
            <div class="control qty whitespace-nowrap">
                <label for="cart-<?= $escaper->escapeHtmlAttr($_item->getId()) ?>-qty" class="flex justify-end mb-0 mr-10 md:mr-0">
                    <span class="label text-sm text-black font-normal w-full md:hidden mr-7"><?= $escaper->escapeHtml(__('Qty')) ?></span>
                    <button @click.prevent="if (qty_counter > 0) { qty_counter--; callChange(); }"
                            class="md:hidden text-black font-bold text-2xl leading-none border rounded px-2 h-6 relative">−</button>
                    <input id="cart-<?= $escaper->escapeHtmlAttr($_item->getId()) ?>-qty"
                           name="cart[<?= $escaper->escapeHtmlAttr($_item->getId()) ?>][qty]"
                           x-model.number="qty_counter"
                           type="number"
                           size="4"
                           step="any"
                           title="<?= $escaper->escapeHtmlAttr(__('Qty')) ?>"
                           class="qty form-input px-2 py-2 w-8 h-6 md:w-16 md:h-10 text-center text-sm md:text-lg text-black rounded relative"
                           style="border: 1px solid #c2c2c2;"
                           required="required"
                           min="0"
                           @change.once="$dispatch('showupdatecart', { item_id: item_id })"
                           @change.debounce.250ms="window.updateQty($event)"
                           data-role="cart-item-qty"/>
                    <button @click.prevent="qty_counter++; callChange();"
                            class="md:hidden text-black font-bold text-2xl leading-none border rounded px-2 h-6 relative">+</button>
                </label>
            </div>
        </div>
    </div>

    <div class="mobile-item-price flex md:flex-col md:place-content-start col-start-2 col-end-7 md:col-start-5 md:col-end-6 md:row-span-2 col subtotal md:border-t md:pl-5 md:relative" data-th="<?= $escaper->escapeHtml(__('Total')); ?>">
        <div class="md:hidden w-full text-sm">
            <span class="md:font-semibold price-dsk">
                <?= $escaper->escapeHtml(__('Price')) ?>
            </span>
        </div>
        <div class="w-full text-right leading-snug md:leading-normal">
            <?php if ($canApplyMsrp): ?>
                <span class="cart msrp subtotal">--</span>
            <?php else: ?>
                <span class="text-sm md:text-lg md:font-semibold text-black">
                    <?= $block->getRowTotalHtml($_item); ?>
                </span>
                <?php if ($_item->getProduct()->getFinalPrice() && $_item->getProduct()->getPrice() && $_item->getProduct()->getFinalPrice() < $_item->getProduct()->getPrice()) {
                    $oldPrice = $_item->getProduct()->getPrice() * $_item->getQty();
                    ?>
                    <span class="save-info flex flex-row justify-end text-sm text-gfpgray">
                        <span class="price line-through ml-0.5"><?= $priceHelper->currency($oldPrice, true, false) ?></span>
                    </span>
                    <?php
                }
                ?>
            <?php endif; ?>
        </div>
    </div>
    <div class="col-span-6 flex flex-col flex-row items-center bg-gfpyellow bg-opacity-30" x-data="{ show_update: false, item_id: '<?= $escaper->escapeHtmlAttr($_item->getId()) ?>' }">
        <button type="submit"
                x-cloak
                x-show=show_update
                x-on:showupdatecart.window="if (item_id == $event.detail.item_id) { show_update = true; }"
                name="update_cart_action"
                data-cart-item-update=""
                value="update_qty"
                title="<?= $escaper->escapeHtmlAttr(__('Update Shopping Cart')) ?>"
                class="update flex gap-2"
                style="color: #ffffff"
        >
            <?= $heroicons->refreshHtml("h-5 w-5"); ?>
            <?= $escaper->escapeHtml(__('Update Shopping Cart')) ?>
        </button>
    </div>

<?php if ($isLastItemOfGroup === true):?>
    </div>

    <div id="artikel-start-2" class="<?= 'col-span-6 cwpicker cwpicker-row item-' . $mainItemId . ' ' . 'group-' . $productGroup; ?> hidden md:block border-l border-r text-center px-2 sm:text-left sm:px-6 py-2 md:mb-8 shadow-lg rounded-b-xl" x-data="initDeliveryDateChooser({'formattedDate':'<?= $formattedCurrentDeliveryDay; ?>', 'productGroup': '<?= $productGroup ?>', 'itemId': '<?= $mainItemId ?>', 'label': '<?= __('Please click on the date field') ?>'})">
        <span class="text-sm" x-on:click.prevent="toggleDeliveryDateChooser()">
            <?= __('Delivery expected on'); ?>
            <label class="text-base font-semibold inline" x-text="formattedDate"><?= $formattedCurrentDeliveryDay ?></label>
        </span>
        <span class="<?= 'cwpicker item-' . $mainItemId; ?>">
            <a id="<?= "later-delivery-week-" . $mainItemId; ?>" x-on:click.prevent="toggleDeliveryDateChooser()"
               class="cwpicker-delivery-text text_end later-delivery-week pl-4 sm:pl-8 whitespace-nowrap">
                <i class="fas mgz-fa-calendar-day text-xl"> </i>
                <span class="underline pl-1"><?= __('Click here to change the date') ?></span>
            </a>
            <div class="delivery-date-container block md:inline sm:pl-2 sm:whitespace-nowrap" id="<?= "delivery-date-" . $mainItemId; ?>" x-show="showDeliveryDateChooser" x-cloak>
                <input type="date" id="<?= "delivery-date-item-" . $mainItemId; ?>"
                       class="cwpicker-input delivery-date text-lg text-gfpgray py-0 px-1 text-center" min="<?= $firstPossibleDeliveryDay; ?>" max="<?= $lastPossibleDeliveryDay; ?>"
                       value="<?= $currentDeliveryDay; ?>" @change="processChange($event)" onclick="this.showPicker()">
                <template x-if="status == 'success'">
                         <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gfpgreen-lighter inline">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                </template>
                <template x-if="status == 'error'">
                         <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-red-600 inline">
                             <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                        </svg>
                </template>
                <label class='later-delivery-week inline md:pl-2 text-black text-xs whitespace-nowrap' x-text="label" :class="{'text-red-600' : status == 'error'}"></label>
            </div>
        </span>
    </div>
<?php endif; ?>

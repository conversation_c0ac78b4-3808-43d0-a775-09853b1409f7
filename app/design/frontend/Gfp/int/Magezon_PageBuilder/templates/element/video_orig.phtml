<?php
$coreHelper       = $this->helper('\Magezon\Core\Helper\Data');
$builderHelper    = $this->helper('\Magezon\Builder\Helper\Data');
$element          = $this->getElement();
$link             = $block->getVideoLink();
$aspectRatio      = $element->getData('aspect_ratio');
$videoTitle       = $element->getData('video_title');
$videoDescription = $element->getData('video_description');
$youtubeId = $block->getVideoId();
?>
<?php

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */
$videoInitId = time() . uniqid();
?>

<script>
    function initYoutube<?=$videoInitId?>(){
        return  {
            active: false,
            init() {
                lazyLoadCss('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_HyvaVideo::css/youtube.css")) ?>');
                lazyLoadCss('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_HyvaVideo::css/ta-youtube.css")) ?>');
                var element = this.$el;
                lazyLoadJs('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_HyvaVideo::js/ta-youtube.js")); ?>').then( () => {
                    element.setAttribute('x-data','taYoutube()' );
                    if(Alpine.initializeComponent) Alpine.initializeComponent(element) // Alpine v2
                });
            }
        }
    }
</script>
<div class="py-2 max-w-<?= $block->getData('max_width') ?? "3xl" ?>">
    <div
        class="ta-youtube ta-youtube-aspect-<?= $block->getData('aspect_ratio') ?? "hd" ?> overflow-hidden"
        x-data="initYoutube<?=$videoInitId?>()"
            x-init="$nextTick(() => {init()})"
            data-id="<?= $youtubeId ?>" >
        <button
                type="button"
                href="play"
                class="absolute inset-0 w-full h-full flex items-center justify-center cursor-pointer"
                x-on:click.prevent="show()"
                x-show="!active"
        >
            <img
                    class="ta-youtube-background<?= $block->getData('anim') ? (" ta-youtube-anim ta-youtube-anim-".$block->getData('anim') ) : "" ?>"
                    src="https://img.youtube.com/vi/<?= $youtubeId ?>/hqdefault.jpg"
                <?php if($block->getData('lazy') ?? true ): ?> loading="lazy" <?php endif; ?>
                    alt="<?= $videoTitle ?? "" ?>"
                    title="<?= $videoTitle ?? "" ?>"
            />
            <div class="ta-youtube-gradient ta-youtube-gradient-dark"></div>
            <div class="ta-youtube-button flex items-center justify-center text-white transform duration-200 hover:scale-110"
                 x-ref="button"
                 alt="<?= $videoTitle ?? __("Show video"); ?>">
                <svg xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 260 180"
                     class="fill-current w-full">
                    <path d="M220,2H40C19.01,2,2,19.01,2,40v100c0,20.99,17.01,38,38,38h180c20.99,0,38-17.01,38-38V40C258,19.01,240.99,2,220,2z
	 M102,130V50l68,40L102,130z"></path>
                </svg>
            </div>
            <div class="ta-youtube-title text-white text-5xl leading-tight font-medium px-2 sm:px-8">
                <?= $escaper->escapeHtml($videoTitle ?? "") ?>
            </div>
            <div class="ta-youtube-description text-white font-semibold text-xl text-center px-2 sm:px-8">
                <?= $escaper->escapeHtml($videoDescription ?? "") ?>
            </div>
        </button>
        <template x-if="active">
            <iframe
                    class="absolute inset-0 w-full h-full"
                    :src="url"
                    frameborder="0"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
            ></iframe>
        </template>
    </div>
</div>
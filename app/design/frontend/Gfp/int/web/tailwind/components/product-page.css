.final-price {
    .price-wrapper {
        @apply background-gradient;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}
.save-info-block{
    @apply text-base font-semibold leading-5 text-right text-gfpgreen-lighter block;
}

.ab-preis {
    @apply text-gfpyellow text-2xl text-center;
}

.lieferung-tooltip[role=tooltip]{
    @apply fixed md:absolute;
    width: 500px; right:0; left:unset;
}

.lieferung-kostenlos {
    ul {
        @apply mt-0 mb-2 ml-16 leading-7;
        list-style: disc;
    }
    .lieferung-kostenlos-wunschlieferwochen {
        @apply mx-0 mt-0 mb-2 leading-4 text-red-700;
    }
    .lieferung-kostenlos-heading2 {
        @apply mx-0 mt-0 mb-5 text-3xl font-medium text-right underline uppercase;
    }

    .lieferung-kostenlos-heading4 {
        @apply my-2 mx-0 text-lg font-bold text-left;
    }
}

.home-header-inner{
    max-width: 100% !important;
    .home-header-info-top {
        display: table;
        width: 100%;
        @apply text-primary;
        min-height: 180px;
    }
    .home-header-info-portrait{
        position: static;
        width: 50%;
        float: left;
        padding-left: 15px;
        padding-right: 15px;
        img {
            position: absolute;
            top: -40px;
            left: -15px;
            pointer-events: none;
            z-index: 1;
        }
    }
    .home-header-info-quote{
        @apply md:pl-0 md:pr-0;
        text-align: left;
        padding-left: 10px;
        padding-right: 10px;
        width: 50%;
        float: right;
        padding-top: 20px;
        span {
            font-size: 15px;
            display: block;
            font-style: italic;
            line-height: normal;
            margin-bottom: 10px;
        }
        label {
            font-size: 14px;
            line-height: 1.2;
            font-weight: 400;
            strong {
                display: block;
                font-weight: 500;
            }
        }
    }

    .home-header-info-bottom {
        background: #667751;
        position: relative;
        text-align: center;
        z-index: 0;
        padding: 0 15px 15px;
        > img {
            display: none;
            position: absolute;
            left: 0;
            pointer-events: none;
            z-index: -1;
            top: -40px;
        }
        .home-header-info-headline {
            color: #fff;
            padding-top: 15px;
            padding-bottom: 10px;
            > label {
                font-size: 22px;
            }
            .home-header-info-link {
                margin-top: 10px;
            }
        }
    }
}

.catalog-product-view {
    .flexsidebar{
        flex: auto;
    }
}

.product-tabs-container .product.info.detailed {
    h1, h2, h3 {
        @apply font-semibold mt-2 mb-5;
        font-size: 20px;
    }
}

@media(min-width: 1024px){
    .catalog-product-view {
        .flexsidebar{
            flex: 0 0 16rem;
        }
        .home-header-inner {
            .home-header-info-top {
                padding-left: 70px;
                img {
                    left: -15px;
                    top: -15px;
                }
            }
            .home-header-info-bottom {
                @apply w-1/2;
                border-top: none;
                margin-top: 0;
                > img {
                    display: block;
                }
            }
        }
    }
}

@media(max-width: 768px){
    .catalog-product-view {
        .addtocart-container {
            box-shadow: 0 0 15px 0 rgb(0 0 0 / 10%);
        }
    }
}

@media(max-width: 768px){
    .swiper-lightbox.lightbox { z-index: 100000000001; }
}

.catalog-product-view {
    .columns{
        padding: 0;
        .product-info-main {
            button#product-addtocart-button{
                min-width: 50%;
            }
            .price-tax-info {
                @apply md:static leading-none;
                .pricetaxinfo-text {
                    line-height: 1;
                    font-size: 11px;
                    font-weight: 300;
                    opacity: 0.7;
                    a { text-decoration: underline; }
                }
            }
        }
        .product-image-container img {width:auto;}
    }
    #gallery {
        .swiper-slide-thumb-active {
            @apply ring-2 ring-gfpgreen;
        }
    }
}

.products-related .product-item-actions .product-item-button .action.btn-main:before {
    content: "";
    background: url(../images/assets/icon_cart_invert.svg) no-repeat;
    width: 25px;
    height: 25px;
    display: inline-block;
    vertical-align: middle;
    background-size: 20px 20px;
    background-position: center 0;
}

.product-setchooser-items {
    .col-xs-6 {
        width: 50%;
        float: left;
    }
    .product-setchooser-pricebox {
        strong {
            @apply background-gradient block text-2xl font-semibold text-center;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        span {
            @apply block text-base font-semibold text-center text-gfpgreen;
        }
    }
    ul.checklist li:before {
        padding-right: 0;
        transform: scale(1.5);
    }
}

.product.data.items .item.title>.switch {
    height: 45px;
}

.anchors-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    position: sticky;
    top: 0;
    position: -webkit-sticky;
    z-index: 4;
    background: #6f7d81;
    overflow: hidden;
    a > .scroll-progress-bar {
        display: block;
        position: absolute;
        background: #cccccc4d;
        top: 0;
        bottom: 0;
        left: 0;
        width: 0;
        opacity: 1;
    }
    a > .scroll-progress-bar[data-state="processing"] + span, .data.item.title:last-child a > .scroll-progress-bar[data-state="completed"] + span {
        font-weight: 500;
        font-size: 1.2em;
    }
}

.page-product-bundle {
    .price-final_price {
        .price-from,
        .price-to {

            .price-container {
                @apply text-gray-700 block mb-4;

                .price-label {
                    @apply block text-base font-medium;
                }

                .price {
                    @apply block font-semibold text-xl text-gray-800 leading-tight;
                }
            }

            .old-price {
                .price-container {
                    .price,
                    .price-label {
                        @apply text-gray-500 font-light inline text-sm;
                    }
                }
            }
        }
    }
}
.review-form {
    .review-field-image {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 1.5em;
        gap: 1em;
        .label {
            margin-bottom: 0;
        }
    }
    .amreview-recommend-wrap{
        display: none;
    }
}

.catalog-product-view.not-valid {
    .page-header,
    .top-container,
    #gallery,
    .product-info > *:not(.product-info-form),
    .product-info-main > *:not(:first-child)
    {
        opacity: 0.5;
    }
}

.product-options-wrapper .field {
    @apply my-1;
}

#customer-review-list {
    .swiper-slide.customer_review_slide {
        min-height: 220px;
        max-height: 220px;
    }
    .swiper-slide.swiper-slide-visible{
        max-height: 100%;
        height: auto;
    }
}

.swiper-slide.transition-all {
    transition-property: all;
}

.swiper-main-container.swiper-container.related-products-swiper-container {
    --swiper-pagination-bullet-size: 12px;
    --swiper-pagination-bullet-horizontal-gap: 6px;
}

.exit-intent-popup {
    h4 {font-size: 15px !important;}
   .mgz-child:not(:last-child) > .mgz-element-inner {
        margin-bottom: 0;
    }
    .mgz-element-column>.mgz-element-inner {
        padding: 0;
    }
    .full_width_row { padding: 0;}
}

.product-tabs-container table {
    width: 100%;
}

.amrev-input[type='file']{
    width: 100%;
}

.swiper-thumbnail-container {
    .w-1\/2{ width: 50%;}
    .w-1\/3{ width: 33%;}
    .w-1\/4{ width: 25%;}
    .w-1\/5{ width: 20%;}
}

.catalog-product-view-ppc {
    .mgz-hidden-sm.mgz-hidden-xs{
        display: block !important;
    }
    .product-description {
        margin-bottom: 0;
    }
    .product-info-form {
        padding-top: 0;
    }
    .swiper-main {

        .price-tax-info {
            font-size: 0.9em;
        }

        .old-price span {
            font-size: 0.9rem;
        }
        .final-price span {
            font-size: 1.6rem;
            line-height: 2rem;
        }
        .old-price.sly-old-price.text-right,
        .normal-price.product-price-decorated.text-right,
        .final-price.text-center.block {
            display: flex;
        }

        .old-price.sly-old-price.flex.justify-center,
        .justify-center.flex.flex-row.w-full{
            justify-content: flex-start;
        }
        .save-info-block.text-gfpgreen-lighter {
            display: none;
        }
        .product-info.order-2 {

            h1 {
                font-size: 1.5rem;
                line-height: 1.75rem;
                font-weight: 700;
            }
            .final-price .price-wrapper {
                font-size: 2.25rem;
            }
        }
    }
}
.product-info-details-tab-details-linepicker .ob-notice {
    @apply justify-end;
}
@media(max-width: 1023px) {
    .catalog-product-view-ppc {
        #gallery {
            .swiper-main-container.swiper-container {
                margin-bottom: 1.5rem;
            }
        }

        .product-info.order-2 {
            .addtocart-container {
                padding-top: 12px;
                padding-left: 0;
                margin-left: -8px;
            }
        }
        .product-info-topusp-container {
            li {
                padding-right: 0.5em;
            }
            li:nth-child(n+3) {
               display: none;
           }
        }
        .mb-4.leading-relaxed.product-description.prose {
            .mgz-icon-list-item:nth-child(n+4){
                display: none;
            }
            .magezon-builder .inner-content.mgz-container{

                > div.mgz-element:nth-child(n+2){
                    display: none;
                }
                > div.mgz-element.mgz-col-md-6 {
                    width: 100%
                }
            }
        }

    }
}

@media(max-width: 768px) {
    .catalog-product-view-ppc {
        .addtocart-container.fixed {
            position: relative;
            box-shadow: none;
            background: transparent;
            width: auto;

            > .flex.flex-row {
                flex-direction: column;
                .inline-block {
                    width: 100%;
                }
            }
        }
    }
}

@media(max-width: 484px) {
    .catalog-product-view-ppc {
        .product-info {
            .save-info-block.text-gfpgreen-lighter {
                display: none;
            }
        }

        .product-info.order-2 {
            /* hide  */
            .mb-4.leading-relaxed.product-description.prose, .addtocart-container {
                display: none;
            }
        }

        .product-info.order-3.hidden {
            /* show content for smallest screens*/
            display: block;

            .mb-4.leading-relaxed.product-description.prose div.mgz-element.mgz-child.mgz-element-icon_list {
                padding-top: 0;
                margin-top: -4px;
            }

            .addtocart-container .inline-block.text-sm.ml-2.leading-4.align-middle {
                display: flex;
                flex-direction: row;
                justify-content: flex-end;
                padding-right: 0.5rem;

                span.block.text-base.text-gfpgreen-lighter {
                    order: 2;
                    font-size: 12px;
                    line-height: 1rem;
                    padding-left: 4px;
                    border-left: solid 1px;
                    text-align: right;
                }
                p#delivery-time-infoblock {
                    padding-right: 4px;
                }
            }
        }
        .product-info-main .magezon-builder {
            h1, h2 {
                margin-top: 1.5rem;
                margin-bottom: 1.25rem;
            }
        }
    }
}
@media(max-width: 639px) {
    .catalog-product-view-ppc {
        .product-info {
            h1.text-base {
                font-weight: 700 !important;
                font-size: 1rem !important;
                padding-bottom: 0;
            }
            .final-price .price-wrapper {
                font-size: 1.65rem;
            }
            .addtocart-container {
                .btn span {
                    font-size: 1rem;
                    line-height: normal;
                }
            }
        }

        .mb-4.leading-relaxed.product-description.prose {
            .magezon-builder .inner-content.mgz-container .mgz-icon-list .mgz-icon-list-item {
                padding-left: 0;
                text-indent: 0;
                .mgz-icon-list-item-icon, .mgz-icon-list-item-text {
                    font-size: 14px;
                }
            }
        }

    }
}

<?php

use Hyva\Theme\Model\ViewModelRegistry;
use CopeX\HyvaTheme\ViewModel\Cms;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use CopeX\TaxInfo\ViewModel\TaxInfo;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Cms $cmsBlock */
$cmsBlock = $viewModels->require(Cms::class);
/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var TaxInfo $taxInfo */
$taxInfoViewModel = $viewModels->require(TaxInfo::class);
$product = $productViewModel->getProduct();
/** @var \Magento\Catalog\Helper\Image $imageHelper */
$imageHelper = $this->helper('Magento\Catalog\Helper\Image');

$relatedProducts = $product->getRelatedProductCollection();

?>

<div id="addtocartpopup" x-data="{...hyva.modal({duration: 100, dialog:'addtocartpopup'}), ...initAddToCartPopup()}"
    x-bind="eventListeners">
    <div x-cloak x-bind="overlay()" class="fixed inset-0 flex items-center justify-center text-left bg-black bg-opacity-50 z-30" x-show="open" style="z-index: 10001;" >
        <div x-ref="addtocartpopup" role="dialog" aria-labelledby="the-label"
             class="inline-block bg-white max-w-screen-xl md:w-5/6 shadow-xl rounded-lg relative flex flex-col overflow-hidden m-4 md:m-10" style="max-height: calc( 100vh - 30px);">
            <div class="flex justify-between bg-gfpgreen-lighter bg-opacity-25 p-2 flex-col md:flex-row">
                <span class="text-lg md:text-xl sm:px-4 flex items-center">
                    <i class="fas mgz-fa-check text-gfpgreen-lighter pr-2"></i>
                    <?= $cmsBlock->renderBlockByIdentifier('addtocarttopmessage'); ?>
                </span>
                <div @click="open = false"  class="flex justify-end items-center z-10 cursor-pointer hidden md:inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <title><?= __("continue shopping"); ?></title>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            </div>
            <div class="flex flex-row justify-between border-b mx-2 md:mx-6 md:mr-10 py-2 md:py-4">
                <div class="flex md:flex-row flex-col flex-grow">
                    <div class="w-40">
                        <img :src="response.added.image || response.added.origImage"
                             alt="<?= $escaper->escapeHtml($product->getName()) ?>"
                             title="<?= $escaper->escapeHtml($product->getName()) ?>"loading="lazy">
                    </div>
                    <div class="flex flex-col px-2 md:px-4">
                        <div class="font-semibold text-base"><?= $escaper->escapeHtml($product->getName()) ?></div>
                        <!--Ausblenden der Optionen bei konfigurierbaren Produkten

                            <template x-for="option in response.added.options">
                            <div class="text-sm font-light" x-text="option"></div>
                        </template>-->
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="flex flex-row justify-end">
                        <div class="px-3"><span x-text="response.added.qty"></span>x</div>
                        <div class="text-right font-semibold" x-text="response.added.price"></div>
                    </div>
                    <span class="flex flex-row text-xs md:text-sm justify-end">
                         <span><?= __("Regular Price") ?>:</span>
                        <div class="text-right line-through ml-0.5" x-text="response.added.origin_price"></div>
                    </span>
                    <div class="text-right text-gfpyellow text-sm md:text-base"><span class="md:whitespace-nowrap"><?= __('You save:') ?></span><span class="font-semibold pl-2" x-text="response.added.save"></span></div>
                    <span class="flex flex-row text-xs justify-end">
                        <span class="text-xs"><?= $block->escapeHtml($taxInfoViewModel->getTaxText()) ?></span>
                        <span class="hidden sm:block mr-1">,</span>
                        <span><?= __("30 days best price: ") ?></span>
                        <div class="ml-0.5" x-text="response.added.price"></div>
                    </span>
                </div>
            </div>

            <?php if($relatedProducts->count()) : ?>
            <section class="p-0 overflow-y-auto" id="related-products-swiper-popup-container">
                <div class="w-full pt-2 pl-6 pb-0">
                    <span><?= $cmsBlock->renderBlockByIdentifier("product-related-info"); ?></span>
                </div>
            </section>
            <?php endif; ?>

            <div class="flex flex-row flex-nowrap justify-center md:justify-end gap-4 md:gap-2 items-center py-3 px-2 md:px-6 bg-gfpgreen-lighter bg-opacity-25">
                <a @click="open = false" class="rounded px-2 sm:px-4 py-2 bg-white border-gfpgreen border-2 text-black text-center text-base cursor-pointer"><?= __("continue shopping"); ?></a>
                <a href="<?= $block->getUrl("checkout/cart")?>" class="rounded px-2 sm:px-4 py-2 text-white text-center border-2 border-gfpgreen text-base bg-gfpgreen"><?= __("View Cart"); ?></a>

            </div>
        </div>
    </div>
</div>

<script>
    function initAddToCartPopup(){
        return {
            open: false,
            response: {added:{qty: 1, sku: "", options:[], values: {}, image: "", origImage:"<?= $productViewModel->getImage($product,'add_to_cart_popup_head')->getImageUrl() ?>"}},
            priceBox: document.querySelector('.product-info-main .addtocart-container .price-box'),
            eventListeners: {
                ['@product-added-to-cart.window'](event) {
                    cloneSwiper();
                    Object.assign(this.response.added,event.detail.added);
                    this.response.added.price = this.currentPrice('.final-price .price');
                    this.response.added.origin_price = this.currentPrice('.old-price .price');
                    this.response.added.save = this.currentPrice('.save-info-block .price');
                    this.response.added.options = [];
                    for(const option of document.querySelectorAll('[x-ref="product_options"] input:checked')){
                        this.response.added.options.push(option.parentElement.querySelector('.custom-swatch-item-label').firstElementChild.textContent);
                    }
                    for(const option of document.querySelectorAll('.product-options-wrapper .child-alltop input:checked')){
                        this.response.added.options.push(
                            option.parentElement.parentElement.parentElement.parentElement.firstElementChild.textContent.trim() + ": "
                            + option.parentElement.querySelector('label').firstElementChild.textContent
                        );
                    }
                    this.open = true;
                },
                ['@update-gallery.window'](event) {
                    const images = event.detail;
                    if(images.length === 0) return;
                    const currentImage = images[0];
                    this.response.added.image = currentImage["thumb"] || currentImage["img"] || currentImage["url"];
                }
            },
            currentPrice: function(selector){
                const values = this.priceBox.querySelectorAll(selector);
                return values[values.length-1]?.textContent;
            }
        };
    }

    function getAdditionalInitParams(itemsCount) {
        switch(itemsCount) {
            case 1:
                return '768: { slidesPerView: 2}, 1200: {slidesPerView: 3} }, slidesPerView: 1';
            case 2:
                return '768: { slidesPerView: 2.1}, 1200: {slidesPerView: 3} }, slidesPerView: 1.6';
            case 3:
                return '768: { slidesPerView: 2.6}, 1200: {slidesPerView: 3.2} }, slidesPerView: 1.6';
            case 4:
                return '768: { slidesPerView: 2.6}, 1200: {slidesPerView: 4.2} }, slidesPerView: 1.6';
            default:
                return '768: { slidesPerView: 2.6}, 1200: {slidesPerView: 4.6} }, slidesPerView: 1.6';
        }
    }

    function cloneSwiper(){
        let relatedSwiper = document.querySelector('#related-products-swiper');

        if (relatedSwiper) {
            let relatedPopupSwiper = document.querySelector('#related-products-popup-swiper');
            if (relatedPopupSwiper) {
                relatedPopupSwiper.parentNode.removeChild(relatedPopupSwiper);
            }
            let popupContainer = document.querySelector('#related-products-swiper-popup-container');
            let popupSwiper = relatedSwiper.cloneNode(true);
            relatedSwiper.setAttribute('id', ''); // ensure swiper is cloned only once

            popupSwiper.setAttribute('id', 'related-products-popup-swiper');

            let itemsCount = relatedSwiper.querySelectorAll('.related_products_btns').length;

            const xData = popupSwiper.getAttribute('x-data');
            popupSwiper.setAttribute('x-data','Object.assign('+ xData +',{additionalInit: { loop: false, loopedSlides: 0, rewind: true, breakpoints: { '+ getAdditionalInitParams(itemsCount) + ' }})');
            popupSwiper.querySelectorAll('.related_products_more_info_btn').forEach(btn => btn.remove());
            popupContainer.appendChild(popupSwiper);
            document.querySelectorAll('form[action*="\%25uenc\%25"]').forEach(function(form) {
                // Replace "%uenc%" in the action attribute with the result of hyva.getUence()
                var originalAction = form.getAttribute('action');
                var newAction = originalAction.replace("\%25uenc\%25", hyva.getUenc());
                form.setAttribute('action', newAction);
            });

        }
    }
</script>

<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Product $product */
$product = $productViewModel->getProduct();

?>
<div class="product-info order-2 w-full md:pl-5 lg:pl-2 lg:w-1/2 md:mb-0">
    <h1 class="pb-2 m-0 text-4xl font-medium text-left text-primary uppercase md:mt-0 title-font" itemprop="name">
        <?= $escaper->escapeHtml($product->getName()) ?>
    </h1>
    <div itemprop="brand" itemscope itemtype="http://schema.org/Brand">
        <meta itemprop="name" content="GFP" />
    </div>
    <div class="mb-4 text-base font-medium">
        <?= $escaper->escapeHtml($product->getSubname()) ?>
    </div>

    <div class="my-2 cursor-pointer" onclick="scrollToReview()">
        <?= $block->getChildHtml('product.info.review') ?>
    </div>

    <?= $block->getChildHtml("product.info.topusp.container"); ?>

    <?php if ($shortDescription = $productViewModel->getProduct()->getShortDescription()) : ?>
        <div class="mb-4 leading-relaxed product-description prose"><?= /* @noEscape */ $shortDescription ?></div>
    <?php endif; ?>

    <div class="pt-8 product-info-form"><?= $block->getChildHtml('product.info.form') ?></div>

    <div class="addtocart-container w-screen  bg-white fixed left-0 pb-2 pt-0 bottom-0 px-4 w-auto z-20 md:bg-transparent md:relative md:px-0 md:pb-0 md:w-auto" >
        <div class="flex flex-row flex-nowrap items-end mb-2 sm:mb-4 mt-0 justify-between" >
            <?= $block->getChildHtml("product.info.shippinginfo") ?>
            <div class="flex-grow text-right">
                <?= $block->getChildHtml("product.info.price") ?>
            </div>
        </div>
        <div class="flex ml-auto justify-between md:justify-end">
            <?php if ($product->isSaleable()): ?>
                <?= $block->getChildHtml("product.info.quantity") ?>
                <?= $block->getChildHtml("product.info.addtocart") ?>
            <?php else: ?>
                <div class="stock unavailable">
                    <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($product->isSaleable()): ?>
        <div class="flex mt-4 justify-end">
            <?= $block->getChildHtml('addtocart.shortcut.buttons') ?>
        </div>
    <?php endif; ?>

    <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
        <div class="py-4 my-2 tier-price-container">
            <?= /** @noEscape */ $tierPriceBlock ?>
        </div>
    <?php endif; ?>
    <?= $block->getChildHtml("seolink"); ?>
    <?= $block->getChildHtml("product.info.additional") ?>
</div>
<script>
    function scrollToReview(){
        if(document.getElementById('customer-review-list')){
            let reviewTab = document.querySelector('.product-info-details-tab-review');
            if( reviewTab && reviewTab.scrollHeight === 0){
                window.dispatchEvent(new CustomEvent("openproduct-info-details-tab-review"));
            }
            setTimeout(() => {
                document.getElementById('customer-review-list').scrollIntoView({behavior: 'smooth'});
            }, 100);
        }
    }
</script>

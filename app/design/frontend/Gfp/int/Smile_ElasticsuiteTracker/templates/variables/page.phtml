<?php
/**
 * Page variables tracking template
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Smile ElasticSuite to newer
 * versions in the future.
 *
 * @category  Smile
 * @package   Smile\ElasticsuiteTracker
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Smile
 * @license   Open Software License ("OSL") v. 3.0
 */

/** @var \Magento\Framework\Escaper $escaper */
?>
<?php /** @var $block Smile\ElasticsuiteTracker\Block\Variables\AbstractBlock **/ ?>
<?php $variables = $block->getVariables(); ?>
<?php if (!empty($variables)) : ?>
    <script defer>
        loadOnMove(()=> {
            lazyLoadJs('<?= $escaper->escapeUrl($block->getViewFileUrl("Smile_ElasticsuiteTracker::js/tracking.js")); ?>', () => {
                try {
                    <?php foreach ($variables as $varName => $value) : ?>
                    smileTracker.addPageVar('<?php /* @noEscape */ echo $this->escapeJsQuote($varName)?>', '<?php /* @noEscape */ echo $block->stripTags($this->escapeJsQuote($value),
                        null, true) ?>')
                    <?php endforeach; ?>
                } catch (err) {
                    ;
                }
            },'');
        });
    </script>
<?php endif; ?>

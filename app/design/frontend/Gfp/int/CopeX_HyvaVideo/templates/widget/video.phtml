<?php

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */
$videoInitId = time() . uniqid();
$placeholderUrl = $block->getData('placeholder_url');
$source = $block->getData('source') ?? 'youtube';
if(!$placeholderUrl){
    if($source == 'youtube'){
        $placeholderUrl = "https://img.youtube.com/vi/" . $block->getData('videoid') ."/maxresdefault.jpg";
    }
}
$buttonColor = $block->getData('btn_color');
if (!$buttonColor) {
    $buttonColor = "text-white";
}
?>

<script>
    function initVideo<?=$videoInitId?>(){
        return  {
            active: false,
            loaded: false,
            init() {
                lazyLoadCss('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_HyvaVideo::css/youtube.css")) ?>');
                lazyLoadCss('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_HyvaVideo::css/ta-youtube.css")) ?>');
                <?php if(!$placeholderUrl && $source == 'vimeo') : ?>
                fetch(`https://vimeo.com/api/v2/video/<?= $block->getData('videoid') ?>.json`)
                    .then(response => {
                        return response.text();
                    })
                    .then(data => {
                        const { thumbnail_large } = JSON.parse(data)[0];
                        const element = document.querySelectorAll('div[x-data="initVideo<?=$videoInitId?>()"] .ta-youtube-background');
                        if(element && element.length){
                            element[0].src = thumbnail_large + ".jpg";
                        }
                    })
                    .catch(error => {
                        console.log(error);
                    });
                <?php endif; ?>
            },
            load(){
                if (!this.loaded) {
                    lazyLoadJs('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_HyvaVideo::js/ta-youtube.js")); ?>', () => {
                        const element = this.$el.querySelector('.ta-youtube');
                        element.setAttribute('x-data','taYoutube()' );
                        Alpine.initializeComponent
                            ? Alpine.initializeComponent(element) // Alpine v2
                            : Alpine.initTree(element); // Alpine v3
                    });
                    this.loaded = true;
                }
            }
        }
    }
</script>
<div class="py-2 max-w-<?= $block->getData('max_width') ?? "3xl" ?>" x-data="initVideo<?=$videoInitId?>()"
     x-intersect.once.margin.-100px="load()">
    <div class="ta-youtube ta-youtube-aspect-<?= $block->getData('aspect_ratio') ?? "hd" ?> overflow-hidden"
            data-source="<?= $source ?>"
            data-id="<?= $block->getData('videoid') ?>" >
        <button
                type="button"
                href="play"
                class="absolute w-full h-full flex items-center justify-center cursor-pointer"
                @click.prevent="show()"
                x-show="!active" >
            <img
                    class="ta-youtube-background<?= $block->getData('anim') ? (" ta-youtube-anim ta-youtube-anim-".$block->getData('anim') ) : "" ?>"
                    src="<?= $placeholderUrl ?>"
                <?php if($block->getData('lazy') ?? true ): ?> loading="lazy" <?php endif; ?>
                    alt="<?= $block->getData('title') ?? "" ?>"
                    title="<?= $block->getData('title') ?? "" ?>"
            />
            <?php if($block->getData('dark_gradient')) : ?>
            <div class="ta-youtube-gradient ta-youtube-gradient-dark"></div>
            <?php endif; ?>
            <div class="ta-youtube-button flex items-center justify-center transform duration-200 hover:scale-110 <?= $buttonColor ?> "
                 alt="<?= $block->getData('title') ?? __("Show video"); ?>">
                <svg xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 260 180"
                     class="fill-current w-full">
                    <path d="M220,2H40C19.01,2,2,19.01,2,40v100c0,20.99,17.01,38,38,38h180c20.99,0,38-17.01,38-38V40C258,19.01,240.99,2,220,2z
	 M102,130V50l68,40L102,130z"></path>
                </svg>
            </div>
            <div class="ta-youtube-title text-white text-5xl leading-tight font-medium px-2 sm:px-8">
                <?= $escaper->escapeHtml($block->getData('title') ?? "") ?>
            </div>
            <div class="ta-youtube-description text-white font-semibold text-xl text-center px-2 sm:px-8">
                <?= $escaper->escapeHtml($block->getData('description') ?? "") ?>
            </div>
        </button>
        <template x-if="active">
            <iframe
                    class="absolute inset-0 w-full h-full"
                    :src="url"
                    frameborder="0"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
            ></iframe>
        </template>
    </div>
</div>
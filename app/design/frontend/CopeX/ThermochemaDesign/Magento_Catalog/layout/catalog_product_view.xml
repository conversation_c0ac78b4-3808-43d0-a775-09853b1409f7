<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <link src="CopeX_GroupLimiter/js/groupLimiter.js"/>
    </head>
    <body>
        <referenceBlock name="product.info.delivery" remove="true"/>
        <referenceContainer name="product.info.stock.sku">
            <container name="custom_price_list_c"
                       htmlTag="div"
                       htmlClass="custom-product-info-price"
                       before="product.info.type">

                <block class="Magento\Catalog\Block\Product\View" name="custom.price.view"
                       template="CopeX_ThermochemaDesign::product/view/custom_price_view.phtml">
                </block>
            </container>
            <block class="FireGento\MageSetup\Block\Price\Details" name="magesetup.product.price.details" as="price_details" template="FireGento_MageSetup::product/price/details.phtml"/>
        </referenceContainer>
        <referenceBlock name="product.info.details">
            <block class="Magento\Framework\View\Element\Template" name="product.info.details.new" as="product_info_details_new" template="Magento_Catalog::product/view/details_tab.phtml" group="detailed_info">
                <arguments>
                    <argument name="title" translate="true" xsi:type="string">Details</argument>
                    <argument name="sort_order" xsi:type="string">10</argument>
                </arguments>
            </block>
            <block class="Magento\Catalog\Block\Product\View\Description" name="product.info.safety_data" as="safety_data" template="Magento_Catalog::product/view/attribute.phtml" group="detailed_info">
                <arguments>
                    <argument name="at_call" xsi:type="string">getSafetyData</argument>
                    <argument name="at_code" xsi:type="string">safety_data</argument>
                    <argument name="css_class" xsi:type="string">safety_data</argument>
                    <argument name="at_label" xsi:type="string">none</argument>
                    <argument name="at_type" xsi:type="string">download</argument>
                    <argument name="title" translate="true" xsi:type="string">Datenblatt</argument>
                    <argument name="sort_order" xsi:type="string">20</argument>
                </arguments>
            </block>
            <block class="Magento\Catalog\Block\Product\View\Description" name="product.info.brochure" as="brochure" template="Magento_Catalog::product/view/attribute.phtml" group="detailed_info">
                <arguments>
                    <argument name="at_call" xsi:type="string">getBrochure</argument>
                    <argument name="at_code" xsi:type="string">brochure</argument>
                    <argument name="css_class" xsi:type="string">brochure</argument>
                    <argument name="at_label" xsi:type="string">none</argument>
                    <argument name="at_type" xsi:type="string">download</argument>
                    <argument name="title" translate="true" xsi:type="string">Prospekt</argument>
                    <argument name="sort_order" xsi:type="string">30</argument>
                </arguments>
            </block>
            <block class="Magento\Catalog\Block\Product\View\Description" name="product.info.manual" as="manual" template="Magento_Catalog::product/view/attribute.phtml" group="detailed_info">
                <arguments>
                    <argument name="at_call" xsi:type="string">getManual</argument>
                    <argument name="at_code" xsi:type="string">manual</argument>
                    <argument name="css_class" xsi:type="string">manual</argument>
                    <argument name="at_label" xsi:type="string">none</argument>
                    <argument name="at_type" xsi:type="string">download</argument>
                    <argument name="title" translate="true" xsi:type="string">Bedienungsanleitung</argument>
                    <argument name="sort_order" xsi:type="string">40</argument>
                </arguments>
            </block>
        </referenceBlock>
        <move element="product.info.description" destination="product.info.details.new" before="product.info.technical_description"/>
        <move element="product.attributes" destination="product.info.details.new" after="product.info.technical_description"/>
    </body>
</page>

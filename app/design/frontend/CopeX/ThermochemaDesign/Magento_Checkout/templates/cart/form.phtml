<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**  @var $block \Magento\Checkout\Block\Cart\Grid */

/** @var $priceHelper \CopeX\Winline\Helper\PriceHelper */

$priceHelper = $this->helper('CopeX\Winline\Helper\PriceHelper');
/** @var $priceViewHelper \CopeX\ListPriceViewer\Helper\Data */
$priceViewHelper = $this->helper('CopeX\ListPriceViewer\Helper\Data');

$cart = $this->helper('\Magento\Checkout\Helper\Cart');
?>
<?php $mergedCells = ($this->helper('Magento\Tax\Helper\Data')->displayCartBothPrices() ? 2 : 1); ?>
<?= $block->getChildHtml('form_before') ?>
<form action="<?= /* @escapeNotVerified */
$block->getUrl('checkout/cart/updatePost') ?>"
      method="post"
      id="form-validate"
      data-mage-init='{"validation":{}}'
      class="form form-cart">
    <?= $block->getBlockHtml('formkey') ?>
    <div class="cart table-wrapper<?= $mergedCells == 2 ? ' detailed' : '' ?>">
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-top toolbar"
                 data-attribute="cart-products-toolbar-top"><?= $block->getPagerHtml() ?></div>
        <?php endif ?>
        <table id="shopping-cart-table"
               class="cart items data table"
               data-mage-init='{"shoppingCart":{"emptyCartButton": "action.clear",
               "updateCartActionContainer": "#update_cart_action_container"}}'>
            <caption role="heading" aria-level="2" class="table-caption"><?= /* @escapeNotVerified */
                __('Shopping Cart Items') ?></caption>
            <thead>
            <tr>
                <th class="col item" scope="col"><span><?= /* @escapeNotVerified */
                        __('Item') ?></span></th>

                <?php if ($priceHelper->checkDiscount($cart->getQuote()->getItems()) && !$priceViewHelper->isListPriceEnabled()): ?>

                    <th class="col price">
                        <?= /* @escapeNotVerified */
                        __('Listprice') ?>
                    </th>
                    <th class="col price">
                        <?= /* @escapeNotVerified */
                        __('Discount') ?>
                    </th>


                <?php endif; ?>
                <th class="col price" scope="col"><span><?= /* @escapeNotVerified */
                        __('Price') ?></span></th>
                <th class="col qty" scope="col"><span><?= /* @escapeNotVerified */
                        __('Qty') ?></span></th>
                <th class="col subtotal" scope="col"><span><?= /* @escapeNotVerified */
                        __('Subtotal') ?></span></th>
            </tr>
            </thead>
            <?php foreach ($block->getItems() as $_item): ?>
                <?= $block->getItemHtml($_item) ?>
            <?php endforeach ?>
        </table>
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-bottom toolbar"
                 data-attribute="cart-products-toolbar-bottom"><?= $block->getPagerHtml() ?></div>
        <?php endif ?>
    </div>
    <div class="cart main actions">
        <?php if ($block->getContinueShoppingUrl()): ?>
            <a class="action continue"
               href="<?= $block->escapeUrl($block->getContinueShoppingUrl()) ?>"
               title="<?= $block->escapeHtml(__('Continue Shopping')) ?>">
                <span><?= /* @escapeNotVerified */
                    __('Continue Shopping') ?></span>
            </a>
        <?php endif; ?>
        <button type="submit"
                name="update_cart_action"
                data-cart-empty=""
                value="empty_cart"
                title="<?= $block->escapeHtml(__('Clear Shopping Cart')) ?>"
                class="action clear" id="empty_cart_button">
            <span><?= /* @escapeNotVerified */
                __('Clear Shopping Cart') ?></span>
        </button>
        <button type="submit"
                name="update_cart_action"
                data-cart-item-update=""
                value="update_qty"
                title="<?= $block->escapeHtml(__('Update Shopping Cart')) ?>"
                class="action update">
            <span><?= /* @escapeNotVerified */
                __('Update Shopping Cart') ?></span>
        </button>
        <input type="hidden" value="" id="update_cart_action_container" data-cart-item-update=""/>
    </div>
</form>
<?= $block->getChildHtml('checkout.cart.order.actions') ?>
<?= $block->getChildHtml('shopping.cart.table.after') ?>
<script>
    require(['jquery', 'jquery-ui-modules/widget', 'Magento_Ui/js/modal/confirm'], function ($, confirm) {
        var groupConfirmed = false;

        jQuery('.action-delete').on('click', function (e) {
            var data = JSON.parse(e.target.dataset.post).data;
            var type = data.type;
            if (type && type === "grouped") {
                if (groupConfirmed) {
                    return true;
                }
                else {
                    confirm({
                        title: $.mage.__('Delete'),
                        content: $.mage.__('Are you sure you would like to remove this and all items in the package from the shopping cart?'),
                        actions: {
                            confirm: function () {
                                groupConfirmed = true;
                                jQuery(e.target).trigger("click");
                            },
                            cancel: function () {
                                return false;
                            },
                            always: function () {
                            }
                        }
                    });
                    return false;
                }
            } else {
                return true;
            }
        });
    });
</script>

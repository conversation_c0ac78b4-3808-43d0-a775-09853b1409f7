<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="billing-address-form copex_custom_payment" data-bind="fadeVisible: isAddressFormVisible">
    <!-- ko foreach: getRegion('before-fields') -->
    <!-- ko template: getTemplate() --><!-- /ko -->
    <!--/ko-->
    <form data-hasrequired="* Required Fields">
        <fieldset id="billing-new-address-form" class="fieldset address custom_copex">
            <!-- ko foreach: getRegion('additional-fieldsets') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
            <!-- ko if: (isCustomerLoggedIn && customerHasAddresses) -->
            <div class="choice field">
                <input type="checkbox" class="checkbox" id="billing-save-in-address-book"
                       data-bind="checked: saveInAddressBook"/>
                <label class="label" for="billing-save-in-address-book">
                    <span data-bind="i18n: 'Save in address book'"></span>
                </label>
            </div>
            <!-- /ko -->
        </fieldset>

    </form>

</div>

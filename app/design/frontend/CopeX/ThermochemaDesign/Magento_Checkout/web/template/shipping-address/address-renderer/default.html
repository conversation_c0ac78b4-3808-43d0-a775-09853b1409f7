<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="shipping-address-item" css="'selected-item' : isSelected() , 'not-selected-item':!isSelected()">
    <text args="address().company"></text><br/>
    <text args="_.values(address().street).join(', ')"></text><br/>
    <text args="address().postcode"></text> <text args="address().city "></text><br/>
    <text args="getCountryName(address().countryId)"></text><br/>
    <!-- ko if: (address().telephone) -->
    Tel:<!-- ko text: address().telephone --><!-- /ko -->
    <!-- /ko --><br/>

    <each args="data: address().customAttributes, as: 'element'">
        <if args="element.label">
            <text args="element.label"></text>
        </if>
        <text args="element.value"></text>
    </each>

    <button visible="address().isEditable()" type="button"
            class="action edit-address-link"
            click="editAddress">
        <span translate="'Edit'"></span>
    </button>
    <!-- ko if: (!isSelected()) -->
    <button type="button" data-bind="click: selectAddress" class="action action-select-shipping-item">
        <span data-bind="i18n: 'Ship Here'"></span>
    </button>
    <!-- /ko -->
</div>
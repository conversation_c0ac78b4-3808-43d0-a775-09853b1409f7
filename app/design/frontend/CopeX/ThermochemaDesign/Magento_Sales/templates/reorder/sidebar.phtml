<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 * Last ordered items sidebar
 *
 * @var $block \Magento\Sales\Block\Reorder\Sidebar
 */
$showTheButtons = false;

?>
<div class="block block-reorder" data-bind="scope: 'lastOrderedItems'">
    <div class="block-title no-display"
         data-bind="css: {'no-display': !lastOrderedItems().items || lastOrderedItems().items.length === 0}">
        <strong id="block-reorder-heading" role="heading" aria-level="2"><?= /* @escapeNotVerified */
            __('Recently Ordered') ?></strong>
    </div>
    <div class="block-content no-display"
         data-bind="css: {'no-display': !lastOrderedItems().items || lastOrderedItems().items.length === 0}"
         aria-labelledby="block-reorder-heading">
        <form method="post" class="form reorder"
              action="<?= /* @escapeNotVerified */
              $block->getFormActionUrl() ?>" id="reorder-validate-detail">
            <strong class="subtitle"><?= /* @escapeNotVerified */
                __('Last Ordered Items') ?></strong>
            <ol id="cart-sidebar-reorder" class="product-items product-items-names"
                data-bind="foreach: lastOrderedItems().items">
                <li class="product-item">

                    <div class="product-item-info">
                        <a class="product-item-photo" data-bind="attr: { href: url, title: name }">
                            <img class="photo image"
                                 data-bind="attr: {src: image}, style: {width: width + 'px', height: height + 'px'}">
                        </a>
                        <div class="product-item-details">
                            <strong class="product-item-name">
                                <a data-bind="attr: { href: url }" class="product-item-link">
                                    <span data-bind="text: name"></span>
                                </a>
                            </strong>
                            <div class="product-item-actions">
                                <div class="actions-primary primary">
                                    <a data-bind="attr: { href: url }"
                                       class="action tocart primary"><span><?= $block->escapeHtml(__('Look at Product')) ?></span></a>
                                </div>
                            </div>

                        </div>
                    </div>
                </li>
            </ol>
            <div id="cart-sidebar-reorder-advice-container"></div>
            <div class="actions-toolbar">
                <?php if ($showTheButtons): ?>
                    <div class="primary no-display"
                         data-bind="css: {'no-display': !lastOrderedItems().isShowAddToCart}">
                        <button type="submit" title="<?= /* @escapeNotVerified */
                        __('Add to Cart') ?>" class="action tocart primary">
                            <span><?= /* @escapeNotVerified */
                                __('Add to Cart') ?></span>
                        </button>
                    </div>

                    <div class="secondary">
                        <a class="action view" href="<?= /* @escapeNotVerified */
                        $block->getUrl('customer/account') ?>">
                        <span><?= /* @escapeNotVerified */
                            __('View All') ?></span>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </form>
    </div>
    <script>
        require(["jquery", "mage/mage"], function (jQuery) {
            jQuery('#reorder-validate-detail').mage('validation', {
                errorPlacement: function (error, element) {
                    error.appendTo('#cart-sidebar-reorder-advice-container');
                }
            });
        });
    </script>
</div>
<script type="text/x-magento-init">
{
    "*": {
        "Magento_Ui/js/core/app": {
            "components": {
                "lastOrderedItems": {
                    "component": "Magento_Sales/js/view/last-ordered-items"
                }
            }
        }
    }
}






</script>

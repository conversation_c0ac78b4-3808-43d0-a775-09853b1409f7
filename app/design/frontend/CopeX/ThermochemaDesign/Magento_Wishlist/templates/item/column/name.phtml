<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Wishlist\Block\Customer\Wishlist\Item\Column\Info $block */

/** @var \Magento\Wishlist\Model\Item $item */
$item = $block->getItem();
$product = $item->getProduct();
$options = $item->getOptionsByCode();
$cleanOptions = [];
if ($options > 1) {
    /** @var $colliHelper \CopeX\QuoteCollis\Helper\Data */
    $colliHelper = $this->helper("CopeX\QuoteCollis\Helper\Data");
    foreach ($options as $key => $option) {
        if (!in_array($key, ['info_buyRequest', 'option_ids'])) {
            $key = str_replace("option_", "",$key);
            $optionObject = $product->getOptionById($key);
            if ($optionObject && $optionObject->getType() == 'parcel_unit') {
                $cleanOptions[$key] =  $option->getValue() ." x ".$colliHelper->getIntValueFromParcel($optionObject->getTitle()).$product->getColli();
            }
        }
    }
}
?>
<strong class="product-item-name">
    <a href="<?= $block->escapeUrl($block->getProductUrl($item)) ?>" title="<?= $block->escapeHtmlAttr($product->getName()) ?>" class="product-item-link link-sku">
        <?= $block->escapeHtml($product->getSku()) ?>
    </a><br/>
    <a href="<?= $block->escapeUrl($block->getProductUrl($item)) ?>" title="<?= $block->escapeHtmlAttr($product->getName()) ?>" class="product-item-link">
        <?= $block->escapeHtml($product->getName()) ?>
    </a>
    <?php if($cleanOptions) : ?>
    <br/><span class="wishlist-colli"><?= implode(" + ",$cleanOptions); ?></span>
    <?php endif; ?>
</strong>

<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
/** @var $block \CopeX\QuoteCollis\Block\Product\View\Options\Type\ParcelUnit */

?>
<?php
$_option = $block->getOption();
$class = ($_option->getIsRequire()) ? ' required' : '';
$parcelValue = $block->getParcelValue();
$staffHelper = $this->helper('\MageB2B\Staff\Helper\Common');
$customerSession = $staffHelper->getCustomerSession();
$inParcels = in_array($_option->getTitle(), $block->getProduct()->getParcels()??[]);
$staffMember = $customerSession && $customerSession->getCustomer() && !is_null($customerSession->getStaffId());
if ($inParcels || ($staffMember && $_option->getTitle() == 'F1')) : ?>

    <div class="field parcel-unit <?= /* @escapeNotVerified */ $class ?>">
        <div class="control">
            <input type="number"
                   id="options_<?= /* @escapeNotVerified */
                   $_option->getId() ?>_text"
                   class="input-text qty product-custom-option"
                   data-validate="{'validate-zero-or-greater':true}"
                   name="options[<?= /* @escapeNotVerified */
                   $_option->getId() ?>]"
                   data-selector="options[<?= /* @escapeNotVerified */
                   $_option->getId() ?>]"
                   data-unit="<?= $parcelValue ?>"
                   value="<?= $block->escapeHtml($block->getDefaultValue()) ?>"
                   min="0"/>

            <?php if ($block->getParcelType() != "F"): ?>
                <div>
                    <?= __($block->getParcelLabel()), " ", $block->escapeHtml($block->getParcelValue() .
                        " " . $block->getParcel()) ?></div>
            <?php else: ?>
                <div>
                    <?= __($block->getParcelLabel()) . " " . $block->getParcel() ?></div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
@import 'module/_logged-out';

.cms-home,
.catalog-product-view,
.catalog-category-view {
    .price-container.price-final_price {
        .price {
            &:after {
                content: '*';
                margin-left: 5px;
                display: inline-block;
                font-weight: normal;
                vertical-align: top;
            }
        }
    }
}

.catalog-product-view {
    .old-price span.discount {
        display: inline;
    }
    .price-container.price-final_price {
        .price {
            &:after {
                color: #62cef9;
                font-size: 28px;
            }
        }
    }
    .product-info-delivery {
        > .stock.available, > .availability, > .product.attribute.delivery_time > * {
            display: inline-block;
        }
        > .availability.only:before {
            content: "/";
        }
    }
    .product-info-main {
        .product-add-form .box-tocart {
            padding-top:15px;
            margin-bottom: 15px;
        }
        .product-reviews-summary {
            margin-bottom: 10px;
        }
        .product-info-price {
            display: flex;
            align-items: flex-end;
            .price-box {
                padding-bottom: 0;
                display: inline-block;
            }
            .shipping-price-note {
                display: inline-block;
                margin: 8px 0 20px;
                .shipping-price-container {
                    display: flex;
                    align-items: flex-end;
                    font-size: 0.8em;
                    @media screen and (max-width: 415px) {
                        display: inline-block;
                        padding-left: 6px;
                    }
                    .note {
                        font-weight: 600;
                        margin-right: 6px;
                    }
                    .price-box {
                        margin: 0;
                        .old-price{
                            display: none;
                        }
                        .price-container  {
                            .price-wrapper {
                                line-height: 1.1rem;
                            }
                            .price {
                                font-size: 16px;
                                line-height: inherit;
                            }
                        }
                    }
                }
            }
        }
        .product-shipping,  .pictogram-container {
            border-top: 1px solid #ebebeb;
        }
        .pictogram-container {
            padding-top: 10px;
        }
        .product-shipping {
            h3 {
                margin-top: 10px;
                font-size: 16px;
                display: inline-block;
                text-transform: uppercase;
            }
            .shipping-rates-extra-note {
                margin-top: 10px;
                font-weight: 600;
                color: #9f191f;
                @media screen and (min-width: 992px) {
                    padding-left: 0;
                }
                .shipping-extend-placeholder {
                    display: inline;
                }
                .far {
                    margin-right: 6px;
                }
            }
        }
    }

   .product-info-main  .product-info-price-wrapper {
        display: flex;
        border-bottom: 1px #ebebeb solid;
        margin-bottom: 15px;

        .product-info-price {
            border-bottom: none;
            margin-bottom: 0;
        }

        .product-info-price-right {
            display: inline-flex;
            flex-direction: column;
        }


       .product-info-uvp { font-weight: 600; }

        .product-info-stock-sku, .product-info-uvp {
            min-width: 15rem;
            margin: 0;
            text-align: right;
            display: table-cell;
            vertical-align: bottom;
            padding: 0;
            color: #777777;
        }
    }

}

.cms-home,
.catalog-category-view {
    .price-container.price-final_price {
        .price {
            &:after {
                color: #999;
                font-size: 22px;
            }
        }
    }
}

.old-price {
    .price-container.price-final_price {
        .price {
            &:after {
                content: '';
            }
        }
    }
    span.discount {
        display: none;
        .price {
            font-size: 0.6em;
            color: #777777;
            text-decoration: none;
        }
    }
}

.footnotes {
    padding: 0 0 18px;
}
.product.info.detailed {
    clear: both;
    margin-bottom: 50px;

    .additional-attributes {
        .label {
            font-weight: @font-weight__bold;
        }
        > .row > div {
            @media screen and (min-width: 768px) {
                &:nth-child(4n+1), &:nth-child(4n+2) {
                    background: #f3f3f3;
                }
            }
            @media screen and (max-width: 767px) {
                &:nth-child(2n+1) {
                    background: #f3f3f3;
                }
            }
        }
    }
}


.dot {
    height: 12px;
    width: 12px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    &.in-stock{
        background-color: #006400;
    }
    &.low-stock{
        background-color: #ffa500;
    }
    &.out-stock {
        background-color: #ff0000;
    }
}

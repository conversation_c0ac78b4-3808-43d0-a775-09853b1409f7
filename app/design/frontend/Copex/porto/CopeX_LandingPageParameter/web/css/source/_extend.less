body.catalog-product-view-ppc {
  .product-info-main .page-title-wrapper h1 {
    text-transform: none;
  }
  .product-reviews-summary .reviews-actions {
    display: none;
  }

  .product-info-all-wrapper {
    background-color: #EDF1F9;
    width: 100vw;
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: -15px;
    padding-top: 25px;
    box-shadow: inset 0px 10px 10px -10px #444444;

    .product-info-top {
      max-width: 1430px;
      margin: auto;

      .product-info-additional-column {
        display: none;
      }
    }
  }
  .product.info-more.button.primary.action.w-full, .product-info-main .box-tocart .actions {
    color: #FFF;
    text-transform: none;
    border-radius: 3px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .shipping-info {
    margin-left: 10px;
    padding: 2px 15px;
    border-left: grey 1px solid;
    color: darkgreen;
  }

  .product-info-additional-new {
    margin-left: 10px;
  }

  .product-info-main {
    border-right: rgba(128, 128, 128, 0.1) 2px solid;
    padding-right: 35px;

    .box-tocart {
      margin: 0;

      .fieldset {
        margin: 0;
      }
    }

    .product-info-above.product-info-main {
      margin-bottom: 0;
      border: none;
    }

    .stock {
      display: block;
    }
  }

  .block.upsell{
    margin-top: 40px;

    .block.upsell.title {
      margin-left: -10px;
    }
  }

  .mgz-element.mgz-element-row.position-contact.full_width_row {
    display: none;
  }

  .box-tocart {

    .fieldset:last-child{
      margin-left: 0;
    }
    .fieldset > .field.qty {
      display: none;
    }

    .fieldset > .actions {
      width: 100%;

      .action.tocart {
        width: 100%;
        margin-left: 0;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #142563;
        &:hover {
          color: #fff;
        }
      }
    }
  }
  &.catalog-product-view .clearer {
    border-bottom: none;
  }

  .product-series__selection > div:not(.product-series__section--color) {
    display: none;
  }

  .product-series__section--color {
    padding-bottom: 8px;
    border-bottom: rgba(128, 128, 128, 0.1) 2px solid;
  }

  .box-tocart .action.tocart:before {
    content: "";
  }

  button#product-addtocart-button {
    margin: 0;
  }

  span.product.attribute.delivery-time {
    padding-left: 4px;
  }

  .product-series__selection {
    padding: 0;
  }

  &.catalog-product-view .price-container.price-final_price {

    .price:after {
      display: none;
    }
    .price {
      color: #0C40A5 !important;
    }
  }

  .product-info-price {
    margin-bottom: 0;
    .price-box.price-final_price {
      padding-left: 0;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-end;
      flex-basis: 100%;

      .old-price {
        order: 2;

        .price-wrapper .price {
          font-size: 16px !important;
        }
      }

      .special-price {
        order: 1;
        padding-right: 12px;

        .price {
          font-weight: 700;
        }
      }

      .omnibus-price {
        order: 3;
        align-self: flex-end;
        align-content: end;
        width: 100%;

        .price-container.price-final_price.tax.weee {
          font-size: 0.9rem;
        }
      }

      & > .price-container.price-final_price.tax.weee > .price-wrapper > .price {
        font-weight: 700;
      }
    }
  }

  .product-options-bottom .price-box .price-container>span, .product-info-price .price-box .price-container>span {
    margin-left: 0;
  }

  @media (min-width: 1200px) {
    &.page-layout-1column .product-info-top {
      display: grid;
      grid-template-columns: 20% auto 30%;
      column-gap: 25px;
    }
  }
  @media screen and (max-width: 1199px) {
    &.catalog-product-view.page-layout-1column .product-info-all-wrapper .product-info-top .product-info-main {
      grid-area: ~"1 / 2 / 3 / 2";

    }
    .product-info-additional-new {
      margin-left: 0;
      padding-left: 0;
    }
    .product-info-main {
      margin-bottom: 0;
    }
  }

  @media screen and (min-width: 768px) and (max-width: 991px) {
    &.catalog-product-view.page-layout-1column .product-info-all-wrapper .product-info-top {
      grid-template-columns: 400px auto;
    }
  }
  @media screen and (max-width: 767px) {
    &.catalog-product-view.page-layout-1column .product-info-all-wrapper .product-info-top {
      padding: 0 12px;
    }
    &.catalog-product-view.page-layout-1column .product-info-all-wrapper .product-info-top .product-info-main {
      grid-area: ~"2 / 1 / 2 / 1";
      padding: 0;

    }
    &.catalog-product-view.page-layout-1column .page-main {
      padding: 0 12px;

    }
    &.catalog-product-view.page-layout-1column .page-main .products-upsell, &.catalog-product-view.page-layout-1column .page-main .products-upsell > .products{
      margin: 4px 0;
    }
  }
  @media screen and (max-width: 425px) {
    &.catalog-product-view .shipping-info {
      border-left: none;
      padding: 2px 9px;
    }
  }
}

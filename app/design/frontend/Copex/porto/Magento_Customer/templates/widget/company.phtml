<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Customer\Block\Widget\Company $block */
?>
<div class="field company <?= $block->isRequired() ? 'required' : '' ?>">
    <label for="company" class="label">
        <span>
            <?= $block->escapeHtml(__('Company')) ?>
        </span>
    </label>
    <div class="control">
            <?php
            if ($block->getForceUseCustomerAttributes()) {
                $config = \Magento\Framework\App\ObjectManager::getInstance()->get(\Magento\Customer\Block\Widget\Name::class);
                $config->setForceUseCustomerAttributes($block->getForceUseCustomerAttributes());
                $_validationClass = $block->escapeHtmlAttr(
                    $config->getAttributeValidationClass('company')
                );
            }
            else {
                $_validationClass = $block->escapeHtmlAttr(
                    $this->helper(\Magento\Customer\Helper\Address::class)
                        ->getAttributeValidationClass('company')
                );
            }
            ?>
        <input type="text"
               name="company"
               id="company"
               value="<?= $block->escapeHtmlAttr($block->getCompany()) ?>"
               title="<?= $block->escapeHtmlAttr(__('Company')) ?>"
               class="input-text <?= $_validationClass ?: '' ?>"
         >
    </div>
</div>

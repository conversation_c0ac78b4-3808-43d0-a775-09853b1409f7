<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\BlockJsDependencies;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Catalog\ViewModel\Product\OptionsData as ProductOptionsData;
use Magento\Framework\Escaper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$productOptionsViewmodel = $viewModels->require(ProductOptionsData::class);

/** @var \CopeX\ProductAttributeGrouping\ViewModel\Attribute $groupViewModel */
$groupViewModel = $viewModels->require(\CopeX\ProductAttributeGrouping\ViewModel\Attribute::class);

/** @var Magento\Catalog\Model\Product $product */
if (! ($product = $block->getData('product'))) {
    return;
}
$imageDisplayArea = $block->getData('image_display_area');
$templateType = $block->getData('template_type');
$viewMode = $block->getData('view_mode');
$showDescription = $block->getData('show_description');
$showAddToWishlist = $wishlistViewModel->isEnabled();
$showAddToCompare = $compareViewModel->showInProductList();
$viewIsGrid = $viewMode === 'grid';
$productType = $product->getTypeId();
$isProductGroupedOrBundle = $productType === 'bundle' || $productType === "grouped";
$productId = $product->getId();
$options   = $productOptionsViewmodel->getOptionsData($product);

$hideDetails       = $block->getData('hide_details') ?: false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?: false;

$imageCustomAttributes = $product->getData('image_custom_attributes')
    ?? $block->getData('image_custom_attributes')
    ?? [];
$productName = $catalogOutputHelper->productAttribute($product, $product->getName(), 'name');

// Ensure the required JS is rendered on the page
$viewModels->require(BlockJsDependencies::class)->setBlockTemplateDependency($block, 'Magento_Catalog::product/list/js/price-box.phtml');

?>

<?php if ($product->isSaleable()): ?>
<form method="post"
    action="<?= $escaper->escapeUrl($productViewModel->getAddToCartUrl($product, ['useUencPlaceholder' => true])) ?>"
      class="item product product-item product_addtocart_form card card-interactive flex flex-col w-full flex-grow <?= $viewIsGrid ? '' : 'md:flex-row' ?>"
    <?php if ($product->getOptions()): ?>
        enctype="multipart/form-data"
    <?php endif; ?>
>
    <?= /** @noEscape */ $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="product" value="<?= (int)$productId ?>"/>
    <?php foreach ($options as $optionItem): ?>
        <input type="hidden"
               name="<?= $escaper->escapeHtml($optionItem['name']) ?>"
               value="<?= $escaper->escapeHtml($optionItem['value']) ?>">
    <?php endforeach; ?>
    <?php else: ?>
    <div class="item product product-item card card-interactive flex flex-col w-full flex-grow<?= $viewIsGrid ? '' : 'md:flex-row' ?>">
        <?php endif; ?>
        <?php /* Product Image */ ?>
        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
           title="<?= $escaper->escapeHtmlAttr($product->getName()) ?>"
           class="product photo product-item-photo block mx-auto mb-3 <?= $viewIsGrid ? '' : 'md:w-2/6 md:mb-0 md:mr-5 md:shrink-0' ?>"
           tabindex="-1"
        >
            <?= $block->getImage($product, $imageDisplayArea)
                ->setTemplate('Magento_Catalog::product/list/image.phtml')
                ->setData('custom_attributes', $imageCustomAttributes)
                ->setProductId($productId)
                ->toHtml(); ?>
        </a>
        <div class="product-info flex flex-col grow">
            <div class="mt-2 mb-1 items-center justify-center text-primary font-semibold text-lg text-center <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                <a
                    class="product-item-link"
                    href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                    :id="`slide-desc-<?= $escaper->escapeHtmlAttr($product->getId()) ?>-${$id('slider-id')}`"
                >
                    <?= /* @noEscape */ $productName ?>
                </a>
            </div>

            <?php if (!$hideRatingSummary): ?>
                <div class="py-1 mx-auto <?= $viewIsGrid ? '' : 'md:mx-0 md:w-auto' ?>">
                    <?= $block->getReviewsSummaryHtml($product, 'short') ?>
                </div>
            <?php endif; ?>

            <?php if ($product->isAvailable() && !$hideDetails): ?>
                <?= $block->getProductDetailsHtml($product) ?>
            <?php endif; ?>

            <?php if ($showDescription): ?>
                <div class="mt-2 mb-1 items-center justify-center text-primary text-center <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                    <?= /* @noEscape */ $productViewModel->getShortDescriptionForProduct($product) ?>
                </div>
            <?php endif; ?>

            <?php if ($isProductGroupedOrBundle): ?>
                <span class="sr-only">
                <?= $escaper->escapeHtml(__('The price depends on the options chosen on the product page')) ?>
            </span>
            <?php endif; ?>

            <div class="pt-1 mt-auto"
                 x-data="initPriceBox()"
                 x-defer="intersect"
                 @update-prices-<?= (int)$productId ?>.window="updatePrice($event.detail);"
            >
                <?= /* @noEscape */ $productListItemViewModel->getProductPriceHtml($product) ?>
            </div>

            <?php if ($product->isSaleable()): ?>
            <?php
                $stock = $block->getLayout()->getBlock('product.item.stockstatus');
                if($stock) :
                    $stock->setProduct($product);
                    echo $stock->toHtml();
                ?>
                <?php endif; ?>
            <?php endif; ?>

            <div class="pt-3 flex flex-wrap items-center gap-y-4 gap-x-2
            <?= $viewIsGrid ? '' : 'md:justify-center' ?>">
                <?php if ($product->isSaleable()): ?>
                    <?php if ($isProductGroupedOrBundle): ?>
                        <a
                            href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                            class="w-auto btn btn-primary justify-center text-sm <?= $viewIsGrid ? 'mr-auto' : 'mr-auto md:mr-0' ?>"
                            title="<?= $escaper->escapeHtmlAttr(__('Configure %1 on the product page')) ?>"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Configure %1 on the product page', $productName)) ?>"
                        >
                            <?= $heroicons->pencilAltHtml('', 24, 24, ['aria-hidden' => 'true']) ?>
                            <span class="ml-2 inline <?= $viewIsGrid ? 'md:ml-0 md:hidden lg:ml-2 lg:inline' : '' ?>">
                        <?= $escaper->escapeHtml(__('Configure')) ?>
                    </span>
                        </a>
                    <?php else: ?>
                        <button
                            class="w-auto btn btn-primary justify-center text-sm <?= $viewIsGrid ? 'mr-auto' : 'mr-auto md:mr-0' ?>"
                            title="<?= $escaper->escapeHtmlAttr(__('Add to Cart') . ' ' . $productName) ?>"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Cart') . ' ' . $productName) ?>"
                            data-addto="cart"
                        >
                            <?= $heroicons->shoppingCartHtml('', 24, 24, ['aria-hidden' => 'true']) ?>
                            <span class="ml-2 inline <?= $viewIsGrid ? 'md:ml-0 md:hidden lg:ml-2 lg:inline' : '' ?>">
                        <?= $escaper->escapeHtml(__('Add to Cart')) ?>
                    </span>
                        </button>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="w-full justify-center">
                        <?= $block->getChildBlock('stockstatus')->setData('product', $product)->toHtml() ?>
                    </div>
                    <div class="w-auto text-primary-lightest <?= $viewIsGrid ? 'mr-auto' : 'mr-auto md:mr-0' ?>">
                        <?= __('Out of stock') ?>
                    </div>
                <?php endif; ?>
                <div class="flex flex-wrap gap-2">
                    <?php if ($showAddToWishlist): ?>
                        <button x-data="initWishlist()"
                                x-defer="intersect"
                                @click.prevent="addToWishlist(<?= (int)$productId ?>)"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Wish List') . ' ' . $productName) ?>"
                                type="button"
                                class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex shrink-0 items-center justify-center text-gray-500 hover:text-red-600"
                                data-addto="wishlist"
                        >
                            <?= $heroiconsSolid->heartHtml("w-5 h-5", 25, 25, ['aria-hidden' => 'true']) ?>
                        </button>
                    <?php endif; ?>
                    <?php if ($showAddToCompare): ?>
                        <button x-data="initCompareOnProductList()"
                                x-defer="intersect"
                                @click.prevent="addToCompare(<?= (int)$productId ?>)"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Compare') . ' ' . $productName) ?>"
                                type="button"
                                class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex shrink-0 items-center justify-center text-gray-500 hover:text-yellow-500"
                                data-addto="compare"
                        >
                            <?= $heroicons->scaleHtml("w-5 h-5", 25, 25, ['aria-hidden' => 'true']) ?>
                        </button>
                    <?php endif; ?>
                    <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                        <?= $addToBlock->setProduct($product)->getChildHtml() ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php if ($product->isSaleable()): ?>
</form>
<?php else: ?>
    </div>
<?php endif; ?>

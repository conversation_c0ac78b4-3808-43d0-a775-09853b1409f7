<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <referenceBlock name="after.body.start">
            <block name="header-notification" class="Magento\Cms\Block\Block" after="global_notices">
                <arguments>
                    <argument name="block_id" xsi:type="string">usps-header</argument>
                </arguments>
            </block>
        </referenceBlock>

        <referenceBlock name="header-content">
            <arguments>
                <argument name="show_compare" xsi:type="boolean">true</argument> <!-- Show compare button (true/false) -->
                <argument name="show_wishlist" xsi:type="boolean">true</argument> <!-- Show wishlist button (true/false) -->
                <argument name="show_language" xsi:type="boolean">true</argument> <!-- Show language select (true/false) -->
            </arguments>

            <block name="header.compare" as="compare" template="Magento_Theme::html/header/compare.phtml"/>

            <block
                name="topmenu_mobile_wrapper"
                as="mobilemenu"
                template="Magento_Theme::html/header/mobilemenu.phtml"
                ttl="3600"
            >
                <arguments>
                    <argument name="cache_lifetime" xsi:type="string">3600</argument>
                </arguments>
            </block>
        </referenceBlock>

        <referenceBlock name="topmenu_desktop">
            <arguments>
                <argument name="open_top_level_menu_on_hover" xsi:type="boolean">true</argument> <!-- Also open on hover (true/false)-->
                <argument name="show_top_level_all_link" xsi:type="boolean">false</argument> <!-- Show view all link for top level (true/false) -->
                <argument name="top_menu_cta_item" xsi:type="string">Sale</argument> <!-- Top Menu Item to Highlight -->
            </arguments>
        </referenceBlock>

        <referenceBlock name="cart-drawer">
            <arguments>
                <argument name="show_sku" xsi:type="boolean">false</argument> <!-- Show sku in product options (true/false) -->
                <argument name="qty_style" xsi:type="string">select</argument> <!-- Qty style (text/input/select/incrementor) -->
            </arguments>
        </referenceBlock>
        <move element="topmenu_mobile" destination="topmenu_mobile_wrapper"/>

        <referenceContainer name="before.body.end">
            <block name="scroll.to.top" template="Magento_Theme::scroll-to-top.phtml">
                <arguments>
                    <argument name="visible_offset" xsi:type="number">200</argument> <!-- Offset before showing the button -->
                    <argument name="visible_only_to_top" xsi:type="boolean">true</argument> <!-- Only show when scrolling to top (true/false) -->
                    <argument name="hide_on_inactivity" xsi:type="number">4000</argument> <!-- Hide after x ms -->
                    <argument name="has_sticky_header" xsi:type="boolean">false</argument> <!-- Add support for sticky header (true/false) -->
                </arguments>
            </block>
        </referenceContainer>

    </body>
</page>

<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Logo\LogoSizeResolver;
use Hyva\Theme\ViewModel\Logo\LogoPathResolver;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Header\Logo;

/** @var Logo $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$storeName = $storeConfig->getStoreConfig('general/store_information/name') ?: $block->getThemeName() ?: __('Store logo');

/** @var LogoSizeResolver $logoSizeResolver */
$logoSizeResolver = $viewModels->require(LogoSizeResolver::class);
$logoWidth = $logoSizeResolver && $logoSizeResolver->getWidth()
    ? $logoSizeResolver->getWidth()
    : ($block->getLogoWidth() ?: 200);
$logoHeight = $logoSizeResolver && $logoSizeResolver->getHeight()
    ? $logoSizeResolver->getHeight()
    : ($block->getLogoHeight() ?: 150);

/** @var LogoPathResolver $logoPathResolver */
$logoPathResolver = $block->getData('logoPathResolver');
$logoSrc = $logoPathResolver && method_exists($logoPathResolver, 'getLogoSrc')
    ? $logoPathResolver->getLogoSrc($block->getData('logo_file'))
    : $block->getLogoSrc();
?>

<a
    class="inline-block align-middle"
    href="<?= $escaper->escapeUrl($block->getUrl()) ?>"
    title="<?= $escaper->escapeHtmlAttr(__('Go to Home page')) ?>"
>
    <?php if ($logoSrc): ?>
        <img
            src="<?= $escaper->escapeUrl($logoSrc) ?>"
            alt="<?= $escaper->escapeHtmlAttr($block->getLogoAlt() ?: $storeName) ?>"
            width="<?= $escaper->escapeHtmlAttr($logoWidth) ?>"
            height="<?= $escaper->escapeHtmlAttr($logoHeight) ?>"
            loading="eager"
            fetchpriority="high"
        >
    <?php else: ?>
        <span class="text-xl font-medium font-title">
            <?= $escaper->escapeHtml($storeName) ?>
        </span>
    <?php endif; ?>
</a>

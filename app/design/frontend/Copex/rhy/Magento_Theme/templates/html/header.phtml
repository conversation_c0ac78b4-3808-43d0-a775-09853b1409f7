<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Checkout\Block\Cart\Sidebar as SidebarCart;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(SidebarCart::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);

$showCompare = $block->getShowCompare() !== null ? (bool) $block->getShowCompare() : true;
$showWishlist = $block->getShowWishlist() !== null ? (bool) $block->getShowWishlist() : true;
$showLanguage = $block->getShowLanguage() !== null ? (bool) $block->getShowLanguage() : true;
?>
<script>
    function initHeader () {
        return {
            searchOpen: false,
            cart: {},
            isCartOpen: false,
            init() {
                this.checkHeaderSize();
            },
            checkHeaderSize() {
                const pageHeader = document.querySelector('.page-header');
                if (!pageHeader) return;
                document.documentElement.style.setProperty("--page-header-height", `${pageHeader.offsetHeight}px`);
            },
            getData(data) {
                if (data.cart) { this.cart = data.cart }
            },
            isCartEmpty() {
                return !this.cart.summary_count
            },
            toggleCart(event) {
                if (event.detail && event.detail.isOpen !== undefined) {
                    this.isCartOpen = event.detail.isOpen
                    if (!this.isCartOpen && this.$refs && this.$refs.cartButton) {
                        this.$refs.cartButton.focus()
                    }
                } else {
                    <?php
                    /*
                     * The toggle-cart event was previously dispatched without parameter to open the drawer (not toggle).
                     * Keeping this in here for backwards compatibility.
                     */
                    ?>
                    this.isCartOpen = true
                }
            }
        }
    }
</script>
<div
    id="header"
    class="bg-white"
    x-data="initHeader()"
    @private-content-loaded.window="getData(event.detail.data)"
    @resize.window.debounce="checkHeaderSize"
>
    <div class="container pt-3.5 lg:pb-3.5 grid grid-cols-[auto_minmax(0px,1fr)_auto]
        lg:grid-cols-[auto_minmax(0px,1fr)_auto] gap-y-3.5 lg:gap-y-0 gap-x-4 lg:gap-x-10 items-center">
        <?= $block->getChildHtml('mobilemenu') ?>
        <?= $block->getChildHtml('logo'); ?>

        <div class="ml-auto flex gap-0.5 items-center lg:order-2">
            <?php if ($showCompare): ?>
                <?= $block->getChildHtml('compare'); ?>
            <?php endif; ?>

            <?php if ($showLanguage): ?>
                <?= $block->getChildHtml('store-language-switcher'); ?>
            <?php endif; ?>

            <?= $block->getChildHtml('additional') ?>

            <?= $block->getChildHtml('customer') ?>

            <?php if ($showWishlist): ?>
                <?= $block->getChildHtml('wishlist') ?>
            <?php endif ?>

            <?php if ($showMiniCart): ?>
                <button
            <?php else: ?>
                <a
            <?php endif ?>
                id="menu-cart-icon"
                class="relative inline-block rounded p-1 hover:bg-primary/10 outline-offset-2"
                x-ref="cartButton"
                :aria-disabled="isCartEmpty()"
                :aria-label="`
                    <?= $escaper->escapeHtmlAttr($showMiniCart ? __('Toggle minicart') : __('View cart')) ?>,
                    ${isCartEmpty()
                        ? '<?= $escaper->escapeHtmlAttr(__('Cart is empty')) ?>'
                        : cart.summary_count > 1
                            ? hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 items')) ?>', cart.summary_count)
                            : hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 item')) ?>', cart.summary_count)
                    }`"
                <?php if ($showMiniCart): ?>
                    @click.prevent.stop="() => {
                        $dispatch('toggle-cart', { isOpen: true })
                    }"
                    @toggle-cart.window="toggleCart($event)"
                    :aria-expanded="isCartOpen"
                    aria-haspopup="dialog"
                <?php else: ?>
                    href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
                    title="<?= $escaper->escapeHtmlAttr(__('View cart')) ?>"
                <?php endif ?>
            >
                <?= $heroicons->shoppingCartHtml('', 24, 24, ["aria-hidden" => "true"]) ?>
                <span
                    x-text="cart.summary_count"
                    x-show="!isCartEmpty()"
                    x-cloak
                    class="absolute -top-1.5 -right-1.5 px-2 py-0.5 rounded-full bg-green-200 text-green-800
                        text-xs font-semibold leading-none text-center uppercase tabular-nums"
                    aria-hidden="true"
                ></span>
            <?php if ($showMiniCart): ?>
                </button>
            <?php else: ?>
                </a>
            <?php endif ?>
        </div>

        <?php // The bg is set using border-image (full-bleed method) ?>
        <div class="col-span-full py-4 lg:py-0 lg:col-auto lg:order-1
            [border-image:conic-gradient(theme(colors.slate.200)_0_0)_fill_0//0_100vw] lg:[border-image:none]">
            <?= $block->getChildHtml('header-search'); ?>
        </div>
    </div>

    <div class="bg-slate-200 hidden lg:block">
        <div class="container">
            <?= $block->getChildHtml('topmenu') ?>
        </div>
    </div>

    <?= $block->getChildHtml('cart-drawer'); ?>
    <?= $block->getChildHtml('authentication-popup'); ?>
</div>

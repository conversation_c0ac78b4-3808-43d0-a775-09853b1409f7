<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Top menu for store
 *
 * @var $block \Magento\Theme\Block\Html\Topmenu
 */
?>
<?php $columnsLimit = $block->getColumnsLimit() ?: 0; ?>
<?php $_menu = $block->getHtml('level-top', 'submenu', $columnsLimit) ?>

<nav class="navigation" data-action="navigation">
    <ul data-mage-init='{"menu":{"responsive":true, "expanded":true, "position":{"my":"left top","at":"left bottom"}}}'>
        <?= $_menu ?>
        <li class="level0 nav-2  parent level-top ui-menu-item" role="presentation">
            <a href="<?php echo $block->getBaseUrl().'info/' ?>"
               class="level-top ui-corner-all" id="ui-id-222"
               tabindex="-1" aria-haspopup="true" role="menuitem">
                <?php echo __("Info") ?>
            </a>
        </li>
        <li class="level0 nav-3 levelt-top ui-menu-item role="presentation">
            <a href="<?php echo $block->getBaseUrl().'contact-shop/' ?>"
               class="level-top ui-corner-all" id="ui-id-2"
               tabindex="-1" role="menuitem"><span><?php echo __("Contact") ?></span></a>
        </li>
    </ul>
</nav>

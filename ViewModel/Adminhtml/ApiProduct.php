<?php

namespace Incert\Voucher\ViewModel\Adminhtml;

use Incert\Client\Model\Shop\Response\Product;
use Incert\Voucher\Helper\Voucher;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class ApiProduct implements ArgumentInterface
{

    private Voucher $voucher;
    private Session $authSession;

    public function __construct(Voucher $voucher, Session $authSession)
    {
        $this->voucher = $voucher;
        $this->authSession = $authSession;
    }

    public function getProducts()
    {
        try {
            return $this->voucher->getProductsFromApi();
        } catch (\Exception $exception) {
        }
        return null;
    }

    public function getInterfaceLocale()
    {
        return $this->authSession->getUser()->getInterfaceLocale();
    }

    public function getProductName(Product $product)
    {
        $locale = substr($this->getInterfaceLocale(), 0, 2);
        $descriptions = $product->getProductDescriptions();
        foreach ($descriptions as $description) {
            if($description['language']['code'] === $locale){
                return $description['name'];
            }
        }
    }
}
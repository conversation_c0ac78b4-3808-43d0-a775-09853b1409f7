<?php

namespace Incert\Voucher\ViewModel\Adminhtml;

use Incert\Client\Model\Shop\Response\Product;
use Incert\Voucher\Helper\Voucher;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class ApiProduct implements ArgumentInterface
{
    /**
     * @var Voucher
     */
    private Voucher $voucher;

    /**
     * @var Session
     */
    private Session $authSession;

    /**
     * Constructor
     *
     * @param Voucher $voucher
     * @param Session $authSession
     */
    public function __construct(Voucher $voucher, Session $authSession)
    {
        $this->voucher = $voucher;
        $this->authSession = $authSession;
    }

    /**
     * Get products from API
     *
     * @return array|null
     */
    public function getProducts()
    {
        try {
            return $this->voucher->getProductsFromApi();
        } catch (\Exception $exception) {
            // Log exception if needed
        }
        return null;
    }

    /**
     * Get interface locale
     *
     * @return string
     */
    public function getInterfaceLocale()
    {
        return $this->authSession->getUser()->getInterfaceLocale();
    }

    /**
     * Get product name based on interface locale
     *
     * @param Product $product
     * @return string|null
     */
    public function getProductName(Product $product)
    {
        $locale = substr($this->getInterfaceLocale(), 0, 2);
        $descriptions = $product->getProductDescriptions();
        foreach ($descriptions as $description) {
            if ($description['language']['code'] === $locale) {
                return $description['name'];
            }
        }
        return null;
    }
}

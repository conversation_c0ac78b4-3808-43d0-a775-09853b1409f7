<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface VoucherRepositoryInterface
{

    /**
     * Save Voucher
     * @param \Incert\Voucher\Api\Data\VoucherInterface $voucher
     * @return \Incert\Voucher\Api\Data\VoucherInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Incert\Voucher\Api\Data\VoucherInterface $voucher
    );

    /**
     * Retrieve Voucher
     * @param string $voucherId
     * @return \Incert\Voucher\Api\Data\VoucherInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($voucherId);

    /**
     * Retrieve Voucher matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Incert\Voucher\Api\Data\VoucherSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Voucher
     * @param \Incert\Voucher\Api\Data\VoucherInterface $voucher
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Incert\Voucher\Api\Data\VoucherInterface $voucher
    );

    /**
     * Delete Voucher by ID
     * @param string $voucherId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($voucherId);
}

<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface PurchaseRepositoryInterface
{

    /**
     * Save Purchase
     * @param \Incert\Voucher\Api\Data\PurchaseInterface $purchase
     * @return \Incert\Voucher\Api\Data\PurchaseInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Incert\Voucher\Api\Data\PurchaseInterface $purchase
    );

    /**
     * Retrieve Purchase
     * @param string $purchaseId
     * @return \Incert\Voucher\Api\Data\PurchaseInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($purchaseId);

    /**
     * Retrieve Purchase matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Incert\Voucher\Api\Data\PurchaseSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Purchase
     * @param \Incert\Voucher\Api\Data\PurchaseInterface $purchase
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Incert\Voucher\Api\Data\PurchaseInterface $purchase
    );

    /**
     * Delete Purchase by ID
     * @param string $purchaseId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($purchaseId);
}


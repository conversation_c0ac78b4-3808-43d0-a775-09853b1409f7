<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Api\Data;

interface VoucherSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Voucher list.
     * @return \Incert\Voucher\Api\Data\VoucherInterface[]
     */
    public function getItems();

    /**
     * Set quote_id list.
     * @param \Incert\Voucher\Api\Data\VoucherInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}


<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Api\Data;

interface PurchaseSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Purchase list.
     * @return \Incert\Voucher\Api\Data\PurchaseInterface[]
     */
    public function getItems();

    /**
     * Set voucher_code list.
     * @param \Incert\Voucher\Api\Data\PurchaseInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}


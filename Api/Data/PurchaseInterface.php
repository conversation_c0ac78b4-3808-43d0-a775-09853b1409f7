<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Api\Data;

interface PurchaseInterface
{

    const ID = 'entity_id';
    const INVOICE_ID = 'invoice_id';
    const INCERT_ORDER_ID = 'incert_order_id';
    const INVOICE_ITEM_ID = 'invoice_item_id';
    const STATUS = 'status';
    const VOUCHER_INFO = 'voucher_info';
    const VOUCHER_CODE = 'voucher_code';

    /**
     * Get Id
     * @return string|null
     */
    public function getId();

    /**
     * Set id
     * @param string $id
     * @return \Incert\Voucher\Purchase\Api\Data\PurchaseInterface
     */
    public function setId($id);

    /**
     * Get voucher_code
     * @return string|null
     */
    public function getVoucherCode();

    /**
     * Set voucher_code
     * @param string $voucherCode
     * @return \Incert\Voucher\Purchase\Api\Data\PurchaseInterface
     */
    public function setVoucherCode($voucherCode);

    /**
     * Get incert_order_id
     * @return string|null
     */
    public function getIncertOrderId();

    /**
     * Set incert_order_id
     * @param string $incertOrderId
     * @return \Incert\Voucher\Purchase\Api\Data\PurchaseInterface
     */
    public function setIncertOrderId($incertOrderId);

    /**
     * Get invoice_id
     * @return string|null
     */
    public function getInvoiceId();

    /**
     * Set invoice_id
     * @param string $invoiceId
     * @return \Incert\Voucher\Purchase\Api\Data\PurchaseInterface
     */
    public function setInvoiceId($invoiceId);

    /**
     * Get invoice_item_id
     * @return string|null
     */
    public function getInvoiceItemId();

    /**
     * Set invoice_item_id
     * @param string $invoiceItemId
     * @return \Incert\Voucher\Purchase\Api\Data\PurchaseInterface
     */
    public function setInvoiceItemId($invoiceItemId);

    /**
     * Get status
     * @return string|null
     */
    public function getStatus();

    /**
     * Set status
     * @param string $status
     * @return \Incert\Voucher\Purchase\Api\Data\PurchaseInterface
     */
    public function setStatus($status);

    /**
     * Get voucher_info
     * @return string|null
     */
    public function getVoucherInfo();

    /**
     * Set voucher_info
     * @param string $voucherInfo
     * @return \Incert\Voucher\Purchase\Api\Data\PurchaseInterface
     */
    public function setVoucherInfo($voucherInfo);
}


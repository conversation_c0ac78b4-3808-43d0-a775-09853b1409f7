<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\VatValidator\Api\Data;

interface VatValidationResultSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get VatValidationResult list.
     * @return \CopeX\VatValidator\Api\Data\VatValidationResultInterface[]
     */
    public function getItems();

    /**
     * Set is_valid list.
     * @param \CopeX\VatValidator\Api\Data\VatValidationResultInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

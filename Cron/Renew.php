<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\Core\Cron;

use CopeX\Core\Model\Registry;

class Renew
{

    private Registry $registry;

    public function __construct(Registry $registry)
    {
        $this->registry = $registry;
    }

    /**
     * Execute the cron
     *
     * @return void
     */
    public function execute(): void
    {
        $modules = $this->registry->getModules();
        foreach($modules as $module) {
            try{
                $module->renew();
            }catch (\Exception $exception){}
        }
    }
}

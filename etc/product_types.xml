<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Catalog:etc/product_types.xsd">
    <type name="voucher" label="Incert Vouchers" modelInstance="Incert\Voucher\Model\Product\Type\Voucher" indexPriority="60" sortOrder="100" isQty="true">
        <customAttributes>
            <attribute name="refundable" value="true"/>
            <attribute name="taxable" value="false"/>
            <attribute name="Is_real_product" value="false"/>
        </customAttributes>
    </type>
    <composableTypes>
        <type name="voucher" />
    </composableTypes>
</config>
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Incert\Voucher\Api\VoucherRepositoryInterface" type="Incert\Voucher\Model\VoucherRepository"/>
    <preference for="Incert\Voucher\Api\Data\VoucherInterface" type="Incert\Voucher\Model\Voucher"/>
    <preference for="Incert\Voucher\Api\Data\VoucherSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
    <virtualType name="Incert\Voucher\Model\ResourceModel\Voucher\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">incert_voucher</argument>
            <argument name="resourceModel" xsi:type="string">Incert\Voucher\Model\ResourceModel\Voucher\Collection</argument>
        </arguments>
    </virtualType>
    <virtualType name="Incert\Voucher\Model\ResourceModel\Product\Indexer\Price" type="Magento\Catalog\Model\ResourceModel\Product\Indexer\Price\SimpleProductPrice">
        <arguments>
            <argument name="productType" xsi:type="string">voucher</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="incert_voucher_listing_data_source" xsi:type="string">Incert\Voucher\Model\ResourceModel\Voucher\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <preference for="Incert\Voucher\Api\PurchaseRepositoryInterface" type="Incert\Voucher\Model\PurchaseRepository"/>
    <preference for="Incert\Voucher\Api\Data\PurchaseInterface" type="Incert\Voucher\Model\Purchase"/>
    <preference for="Incert\Voucher\Api\Data\PurchaseSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
    <virtualType name="Incert\Voucher\Model\ResourceModel\Purchase\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">incert_voucher_purchase</argument>
            <argument name="resourceModel" xsi:type="string">Incert\Voucher\Model\ResourceModel\Purchase\Collection</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="incert_voucher_purchase_listing_data_source" xsi:type="string">Incert\Voucher\Model\ResourceModel\Purchase\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Sales\Model\Order">
        <plugin name="incert_can_creditmemo_when_refunded" type="Incert\Voucher\Plugin\Sales\OrderPlugin"/>
    </type>
    <type name="Incert\Voucher\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Incert\Voucher\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">incert_voucher</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">Incert\Voucher\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Sales\Model\Order\CreditmemoValidator">
        <plugin name="incert_can_refund_purchased_item_for_creditmemo" type="Incert\Voucher\Plugin\Sales\CreditmemoValidator"/>
    </type>
    <type name="Magento\Sales\Model\Order\Item">
        <plugin name="incert_qty_to_refund"
                type="Incert\Voucher\Plugin\Sales\Order\OrderItem"/>
    </type>
    <type name="Magento\Sales\Model\Order\Creditmemo\Item">
        <plugin name="incert_creditmemo_item"
                type="Incert\Voucher\Plugin\Sales\Order\CreditmemoItem"/>
    </type>
</config>

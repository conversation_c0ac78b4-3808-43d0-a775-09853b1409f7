<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Sales:etc/sales.xsd">
    <section name="quote">
        <group name="totals">
            <item name="voucher" instance="Incert\Voucher\Model\Sales\Quote\Totals\Voucher" sort_order="500">
                <renderer name="adminhtml" instance="Magento\Sales\Block\Adminhtml\Order\Create\Totals\DefaultTotals"/>
            </item>
        </group>
    </section>
    <section name="order_invoice">
        <group name="totals">
            <item name="voucher" instance="Incert\Voucher\Model\Sales\Order\Invoice\Totals\Voucher" sort_order="300"/>
        </group>
    </section>
    <section name="order_creditmemo">
        <group name="totals">
            <item name="voucher" instance="Incert\Voucher\Model\Sales\Order\Creditmemo\Totals\Voucher" sort_order="350"/>
        </group>
    </section>
</config>

<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="incert_vouchers" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" translate="label">
            <label>Incert Vouchers</label>
            <resource>Incert_Voucher::config</resource>
            <tab>sales</tab>
            <group id="incert" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>General</label>
                <field id="enabled" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Enabled</label>
                    <config_path>promo/incert/enabled</config_path>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="host" type="text" translate="label" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>API Url</label>
                    <validate>required-entry</validate>
                    <config_path>promo/incert/host</config_path>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="api_key" type="text" translate="label" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>API Key</label>
                    <validate>required-entry</validate>
                    <config_path>promo/incert/api_key</config_path>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="client_id" type="text" translate="label" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Client ID</label>
                    <validate>required-entry</validate>
                    <config_path>promo/incert/client_id</config_path>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="client_secret" type="obscure" translate="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Client Secret</label>
                    <validate>required-entry</validate>
                    <config_path>promo/incert/client_secret</config_path>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="booking_partner" type="text" translate="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Booking Partner ID</label>
                    <comment>Booking Partner ID in case of multi store</comment>
                    <config_path>promo/incert/booking_parter</config_path>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="regex" type="text" translate="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Coupon Pattern</label>
                    <comment>Pattern of the vouchers thus only these get checked. e.g. /API.*/ , Leave empty to check all non Magento Coupons</comment>
                    <config_path>promo/incert/regex</config_path>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="allow_discount" type="select" sortOrder="90" showInWebsite="1" showInStore="0" showInDefault="1" translate="label comment">
                    <label>Allow Discount on Vouchers</label>
                    <comment>Possibility to apply Magento Sales Rules Discounts on Incert Vouchers</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <config_path>promo/incert/allow_discount</config_path>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
        <section id="sales">
            <group id="totals_sort" >
                <field id="incert" translate="label" type="text" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Incert Vouchers</label>
                </field>
            </group>
        </section>
    </system>
</config>

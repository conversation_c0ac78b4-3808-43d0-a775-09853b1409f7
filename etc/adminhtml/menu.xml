<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
	<menu>
		<add id="Incert::top_level" title="Incert Vouchers" module="Incert_Voucher" sortOrder="40" parent="Magento_Backend::marketing" resource="Incert_Voucher::Voucher"/>
		<add id="Incert_Voucher::incert_voucher" title="Applied Vouchers" module="Incert_Voucher" sortOrder="40" dependsOnModule="Magento_Sales" resource="Incert_Voucher::Voucher_view" parent="Incert::top_level" action="incert_voucher/voucher/index"/>
		<add id="Incert_Voucher::incert_voucher_purchase" title="Purchased Vouchers" module="Incert_Voucher" sortOrder="41" dependsOnModule="Magento_Sales" resource="Incert_Voucher::Purchase_view" parent="Incert::top_level" action="incert_voucher/purchase/index"/>
		<add id="Incert_Voucher::incert_catalog" title="Create Incert Voucher Product" module="Incert_Voucher" sortOrder="41" dependsOnModule="Magento_Sales" resource="Incert_Voucher::Voucher_save" parent="Magento_Catalog::catalog" action="incert_voucher/product/create"/>
	</menu>
</config>

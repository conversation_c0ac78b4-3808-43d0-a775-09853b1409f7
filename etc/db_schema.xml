<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="incert_voucher" resource="default" engine="innodb" comment="Incert Voucher Table">
		<column xsi:type="int" name="voucher_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<column name="code" nullable="true" xsi:type="varchar" comment="Code" length="255"/>
		<column name="quote_id" nullable="true" xsi:type="int" comment="Quote ID" unsigned="true"/>
		<column name="order_id" nullable="true" xsi:type="int" comment="Order ID" unsigned="true"/>
		<column name="amount" nullable="true" xsi:type="float" comment="Amount"/>
		<column name="currency" nullable="false" xsi:type="varchar" comment="currency" length="255"/>
		<column name="redemption_id" nullable="true" xsi:type="text" comment="Redemption ID"/>
		<column name="redemption_amount" nullable="true" xsi:type="float" comment="Redemption Amount"/>
		<column name="refund_amount" nullable="true" xsi:type="float" comment="Refund Amount"/>
		<column name="voucher_info" nullable="true" xsi:type="text" comment="Voucher Info"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="voucher_id"/>
		</constraint>
		<constraint xsi:type="foreign" referenceId="INCERT_VOUCHER_QUOTE_ID" table="incert_voucher"
					column="quote_id" referenceTable="quote" referenceColumn="entity_id" onDelete="SET NULL"/>
		<constraint xsi:type="foreign" referenceId="INCERT_VOUCHER_ORDER_ID" table="incert_voucher"
					column="order_id" referenceTable="sales_order" referenceColumn="entity_id" onDelete="SET NULL"/>
		<index referenceId="INCERT_VOUCHER_QUOTE_ID" indexType="btree">
			<column name="quote_id"/>
		</index>
		<index referenceId="INCERT_VOUCHER_ORDER_ID" indexType="btree">
			<column name="order_id"/>
		</index>
		<index referenceId="INCERT_VOUCHER_CODE" indexType="btree">
			<column name="code"/>
		</index>
	</table>

	<table name="incert_voucher_purchase" resource="default" engine="innodb" comment="Incert Voucher Purchased Products Table">
		<column xsi:type="int" name="entity_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Incert Order Id"/>
		<column name="voucher_code" nullable="true" xsi:type="varchar" comment="Code" length="255"/>
		<column name="incert_order_id" nullable="true" xsi:type="int" comment="Incert Order ID" unsigned="true"/>
		<column name="invoice_id" nullable="true" xsi:type="int" comment="Invoice ID" unsigned="true"/>
		<column name="invoice_item_id" nullable="true" xsi:type="int" unsigned="true" comment="Invoice Item Id"/>
		<column name="status" nullable="true" xsi:type="int" comment="Voucher Status" default="1" unsigned="true"/>
		<column name="voucher_info" nullable="true" xsi:type="text" comment="Voucher Info"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="entity_id"/>
		</constraint>
		<constraint xsi:type="foreign" referenceId="INCERT_VOUCHER_PURCHASED_INVOICE_ID" table="incert_voucher_purchase"
					column="invoice_id" referenceTable="sales_invoice" referenceColumn="entity_id" onDelete="SET NULL"/>
		<constraint xsi:type="foreign" referenceId="INCERT_VOUCHER_PURCHASED_INVOICE_ITEM_ID" table="incert_voucher_purchase"
					column="invoice_item_id" referenceTable="sales_invoice_item" referenceColumn="entity_id" onDelete="SET NULL"/>
		<index referenceId="INCERT_VOUCHER_INVOICE_ID" indexType="btree">
			<column name="invoice_id"/>
		</index>
		<index referenceId="INCERT_VOUCHER_ORDER_ID" indexType="btree">
			<column name="invoice_item_id"/>
		</index>
		<index referenceId="INCERT_VOUCHER_CODE" indexType="btree">
			<column name="voucher_code"/>
		</index>
		<index referenceId="INCERT_ORDER_CODE" indexType="btree">
			<column name="incert_order_id"/>
		</index>
	</table>

	<table name="quote_address">
		<column xsi:type="decimal" name="voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="base_voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Base Incert Voucher Discount Amount"/>
	</table>

	<table name="sales_order" resource="default" engine="innodb">
		<column xsi:type="decimal" name="voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="base_voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Base Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="voucher_invoiced" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="base_voucher_invoiced" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Base Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="voucher_refunded" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="base_voucher_refunded" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Base Incert Voucher Discount Amount"/>
	</table>

	<table name="sales_invoice" resource="default" engine="innodb">
		<column xsi:type="decimal" name="voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="base_voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Base Incert Voucher Discount Amount"/>
	</table>

	<table name="sales_creditmemo" resource="default" engine="innodb">
		<column xsi:type="decimal" name="voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Incert Voucher Discount Amount"/>
		<column xsi:type="decimal" name="base_voucher_amount" scale="4" precision="12" unsigned="false" nullable="false" default="0" comment="Base Incert Voucher Discount Amount"/>
	</table>

</schema>

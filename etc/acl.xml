<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
	<acl>
		<resources>
			<resource id="Magento_Backend::admin">
				<resource id="Incert_Voucher::Voucher" title="Incert Voucher" sortOrder="10">
					<resource id="Incert_Voucher::Voucher_save" title="Save Voucher" sortOrder="10"/>
					<resource id="Incert_Voucher::Voucher_delete" title="Delete Voucher" sortOrder="20"/>
					<resource id="Incert_Voucher::Voucher_update" title="Update Voucher" sortOrder="30"/>
					<resource id="Incert_Voucher::Voucher_view" title="View Voucher" sortOrder="40"/>
					<resource id="Incert_Voucher::Purchase" title="Purchase" sortOrder="50">
						<resource id="Incert_Voucher::Purchase_save" title="Save Purchase" sortOrder="10"/>
						<resource id="Incert_Voucher::Purchase_delete" title="Delete Purchase" sortOrder="20"/>
						<resource id="Incert_Voucher::Purchase_update" title="Update Purchase" sortOrder="30"/>
						<resource id="Incert_Voucher::Purchase_view" title="View Purchase" sortOrder="40"/>
					</resource>
				</resource>
				<resource id="Magento_Backend::stores">
					<resource id="Magento_Backend::stores_settings">
						<resource id="Magento_Config::config">
							<resource id="Incert_Voucher::config" title="Incert Voucher Config" sortOrder="295"/>
						</resource>
					</resource>
				</resource>

			</resource>
		</resources>
	</acl>
</config>

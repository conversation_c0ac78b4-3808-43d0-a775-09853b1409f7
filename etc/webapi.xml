<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/incert-voucher/voucher" method="POST">
		<service class="Incert\Voucher\Api\VoucherRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Incert_Voucher::Voucher_save"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/voucher/search" method="GET">
		<service class="Incert\Voucher\Api\VoucherRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Incert_Voucher::Voucher_view"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/voucher/:voucherId" method="GET">
		<service class="Incert\Voucher\Api\VoucherRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Incert_Voucher::Voucher_view"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/voucher/:voucherId" method="PUT">
		<service class="Incert\Voucher\Api\VoucherRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Incert_Voucher::Voucher_update"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/voucher/:voucherId" method="DELETE">
		<service class="Incert\Voucher\Api\VoucherRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Incert_Voucher::Voucher_delete"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/purchase" method="POST">
		<service class="Incert\Voucher\Api\PurchaseRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Incert_Voucher::Purchase_save"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/purchase/search" method="GET">
		<service class="Incert\Voucher\Api\PurchaseRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Incert_Voucher::Purchase_view"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/purchase/:purchaseId" method="GET">
		<service class="Incert\Voucher\Api\PurchaseRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Incert_Voucher::Purchase_view"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/purchase/:purchaseId" method="PUT">
		<service class="Incert\Voucher\Api\PurchaseRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Incert_Voucher::Purchase_update"/>
		</resources>
	</route>
	<route url="/V1/incert-voucher/purchase/:purchaseId" method="DELETE">
		<service class="Incert\Voucher\Api\PurchaseRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Incert_Voucher::Purchase_delete"/>
		</resources>
	</route>
</routes>

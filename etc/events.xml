<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../../../lib/internal/Magento/Framework/Event/etc/events.xsd">
    <!-- Filter vouchers from coupons -->
    <event name="sales_quote_collect_totals_before">
        <observer name="incert_voucher_sales_quote_collect_totals_before" instance="Incert\Voucher\Observer\Sales\Quote\Totals\Before" />
    </event>
    <!-- Reset coupon value -->
    <event name="sales_quote_collect_totals_after">
        <observer name="incert_voucher_sales_quote_collect_totals_after" instance="Incert\Voucher\Observer\Sales\Quote\Totals\After" />
    </event>

    <!-- Register Redeemed Vouchers at Incert -->
    <event name="sales_order_place_after">
        <observer name="incert_voucher_sales_order_place_after" instance="Incert\Voucher\Observer\Sales\Order\PlaceAfter" />
    </event>

    <event name="order_cancel_after">
        <observer name="incert_voucher_order_cancel_after" instance="Incert\Voucher\Observer\Sales\Order\CancelAfter" />
    </event>

    <event name="checkout_submit_all_after">
        <observer name="incert_voucher_checkout_submit_all_after" instance="Incert\Voucher\Observer\Sales\Order\SubmitAfter" />
    </event>

    <event name="paypal_express_place_order_success">
        <observer name="incert_voucher_paypal_express_place_order_success" instance="Incert\Voucher\Observer\Sales\Order\SubmitAfter" />
    </event>

    <!-- Check if voucher payment is calculated correctly  -->
    <event name="sales_order_invoice_pay">
        <observer name="incert_voucher_sales_order_invoice_pay" instance="Incert\Voucher\Observer\Sales\Order\Invoice\Pay" />
    </event>

    <event name="sales_order_payment_refund">
        <observer name="incert_voucher_sales_order_payment_refund" instance="Incert\Voucher\Observer\Sales\Order\Payment\Refund" />
    </event>

    <!-- Revalidate vouchers + add information to quote -->
    <event name="sales_model_service_quote_submit_before">
        <observer name="voucher-convert-data" instance="Incert\Voucher\Observer\Sales\Service\Quote\SubmitBefore" />
    </event>

    <!-- Save data to order -->
    <event name="sales_order_invoice_register">
        <observer name="incert_voucher_sales_order_invoice_register" instance="Incert\Voucher\Observer\Sales\Order\Invoice\Register" />
    </event>

    <!-- order vouchers and save data in database -->
    <event name="sales_order_invoice_save_after">
        <observer name="incert_voucher_sales_order_invoice_save_after_order_vouchers" instance="Incert\Voucher\Observer\Sales\Order\Invoice\Save" />
    </event>

    <event name="sales_order_invoice_save_commit_after">
        <observer name="incert_voucher_sales_order_invoice_save_after_order_vouchers" instance="Incert\Voucher\Observer\Sales\Order\Invoice\SaveAfter" />
    </event>

    <!-- Paypal -->
    <event name="payment_cart_collect_items_and_amounts">
        <observer name="incert_voucher_paypal_collect" instance="Incert\Voucher\Observer\Paypal\Collect" />
    </event>
</config>
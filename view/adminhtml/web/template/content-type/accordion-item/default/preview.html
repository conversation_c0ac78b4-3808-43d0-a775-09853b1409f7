<div attr="data.main.attributes"
     ko-style="data.main.style"
     css="data.main.css">
    <div class="pagebuilder-accordion-item pagebuilder-affordance-row pagebuilder-content-type pagebuilder-content-type-affordance"
         event="{ mouseover: onMouseOver, mouseout: onMouseOut }, mouseoverBubble: false">
        <div class="pagebuilder-display-label"
             text="function () { return displayLabel().toUpperCase(); }()"></div>
        <span attr="data.headline.attributes"
              data-bind="liveEdit: { field: 'headline_text', placeholder: 'Edit heading' }"
              class="pagebuilder-accordion-item-collapsible" aria-expanded="true"></span>
        <div data-role="content"
             class="pagebuilder-content-type no-system-border type-container pagebuilder-row children-min-height"
             css="Object.assign({'empty-container': contentType.children().length == 0})">
            <render args="getOptions().template"></render>
            <div class="element-children content-type-container" each="contentType.getChildren()"
                 css="getChildrenCss()"
                 attr="{id: contentType.id + '-children'}"
                 data-bind="sortable: getSortableOptions()"
                 afterRender="function (element) { if (typeof afterChildrenRender === 'function') { afterChildrenRender(element); } }">
                <if args="$parent.isContainer()">
                    <div class="pagebuilder-drop-indicator"></div>
                </if>
                <div class="pagebuilder-content-type-wrapper"
                     template="{ name: preview.template, data: preview, afterRender: function () { preview.dispatchAfterRenderEvent.apply(preview, arguments); } }"
                     attr="{ id: id }" css="{'pagebuilder-content-type-hidden': !preview.display()}"></div>
                <if args="$parent.isContainer() && $index() === $parent.contentType.getChildren()().length - 1">
                    <div class="pagebuilder-drop-indicator"></div>
                </if>
            </div>
            <div class="pagebuilder-empty-container empty-placeholder"
                 css="placeholderCss()"
                 translate="'Drag content types or columns here'"></div>
        </div>
    </div>
</div>




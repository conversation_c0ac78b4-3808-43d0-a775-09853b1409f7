<?php

/** @var \Incert\Voucher\ViewModel\Adminhtml\ApiProduct $viewModel */

use Incert\Voucher\ViewModel\Adminhtml\ApiProduct;

$viewModel = $block->getViewModel();
$products = $viewModel->getProducts();
?>
<?php if($products) : ?>
<form action="<?php echo $this->getUrl('incert_voucher/product/post'); ?>" method="post">
    <div class="page-main-actions">
        <div class="page-actions-placeholder" style=""></div><div class="page-actions"><div class="page-actions-inner">
                <div class="page-actions-buttons">
                    <div title="Speichern" class="actions-split save primary" >
                        <button id="save-button" title="Speichern" class="action-default primary"  type="submit">
                            <span><?= __('Save'); ?></span>
                        </button>
                    </div>
                    <button id="back" title="Zurück" type="button" class="action- scalable back" onclick="history.back()" >
                        <span ><?= __('Back') ?></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" name="form_key" value="<?php echo $block->getFormKey(); ?>"/>
    <fieldset class="admin__fieldset">
        <div class="admin__field">
            <label for="product_id" class="admin__field-label"><?= __('Choose Incert Product')?></label>

            <div class="admin__field-control">
                <select name="product_id" id="product_id" class="admin__control-select" style="min-width: 32rem;">
                    <?php foreach($products as $product) : ?>
                    <option value="<?= $product->getId() ?>"><?= $viewModel->getProductName($product) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </fieldset>
</form>
<?php else : ?>
<?= __("ERROR: No products provided by API. Please contact Incert"); ?>
<?php endif; ?>

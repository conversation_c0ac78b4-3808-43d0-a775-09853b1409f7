<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">incert_voucher_purchase_listing.incert_voucher_purchase_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>incert_voucher_purchase_columns</spinner>
		<deps>
			<dep>incert_voucher_purchase_listing.incert_voucher_purchase_listing_data_source</dep>
		</deps>
	</settings>
	<dataSource name="incert_voucher_purchase_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
			<storageConfig>
				<param name="indexField" xsi:type="string">entity_id</param>
			</storageConfig>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Incert_Voucher::Purchase_view</aclResource>
		<dataProvider name="incert_voucher_purchase_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>entity_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="incert_voucher_purchase_columns">
		<selectionsColumn name="ids">
			<settings>
				<indexField>entity_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="entity_id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="voucher_code">
			<settings>
				<filter>text</filter>
				<label translate="true">Purchase Code</label>
			</settings>
		</column>
		<column name="incert_order_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Incert Order ID</label>
			</settings>
		</column>
		<column name="invoice_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Invoice ID</label>
			</settings>
		</column>
		<column name="invoice_item_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Invoice Item ID</label>
			</settings>
		</column>
		<column name="status">
			<settings>
				<filter>text</filter>
				<label translate="true">Status</label>
			</settings>
		</column>
		<actionsColumn name="actions" class="Incert\Voucher\Ui\Component\Listing\Column\Actions">
			<settings>
				<label translate="true">Actions</label>
				<indexField>entity_id</indexField>
			</settings>
		</actionsColumn>

	</columns>
</listing>

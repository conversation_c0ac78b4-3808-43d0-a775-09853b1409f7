<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">incert_voucher_listing.incert_voucher_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>incert_voucher_columns</spinner>
		<deps>
			<dep>incert_voucher_listing.incert_voucher_listing_data_source</dep>
		</deps>
	</settings>
	<dataSource name="incert_voucher_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
			<storageConfig>
				<param name="indexField" xsi:type="string">voucher_id</param>
			</storageConfig>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Incert_Voucher::Voucher_view</aclResource>
		<dataProvider name="incert_voucher_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>voucher_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="incert_voucher_columns">
		<selectionsColumn name="ids">
			<settings>
				<indexField>voucher_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="voucher_id">
			<settings>
				<label translate="true">ID</label>
				<filter>text</filter>
				<sorting>asc</sorting>
			</settings>
		</column>
		<column name="code">
			<settings>
				<label translate="true">Code</label>
				<filter>text</filter>
			</settings>
		</column>
		<column name="quote_id">
			<settings>
				<label translate="true">Quote</label>
				<filter>text</filter>
			</settings>
		</column>
		<column name="order_id">
			<settings>
				<label translate="true">Order</label>
				<filter>text</filter>
			</settings>
		</column>
		<column name="invoice_id">
			<settings>
				<label translate="true">Invoice</label>
				<filter>text</filter>
			</settings>
		</column>
		<column name="creditmemo_id">
			<settings>
				<label translate="true">Credit Memo</label>
				<filter>text</filter>
			</settings>
		</column>
		<column name="amount">
			<settings>
				<label translate="true">Amount</label>
				<filter>text</filter>
			</settings>
		</column>
		<column name="currency">
			<settings>
				<label translate="true">Currency</label>
				<filter>text</filter>
			</settings>
		</column>
	</columns>
</listing>

<?php
$_order  = $block->getOrder();
$_source = $block->getSource();
?>


<tr class="totals-tax">
    <th <?= /* @noEscape */ $block->getLabelProperties() ?> scope="row">
        <?php if ($block->getIsCreditMemo()): ?>
            <?= $block->escapeHtml(__('Refunded to Voucher')); ?>
        <?php else: ?>
            <?= $block->escapeHtml(__('Vouchers')); ?>
        <?php endif; ?>
    </th>
    <td <?= /* @noEscape */ $block->getValueProperties() ?> data-th="<?= $block->escapeHtmlAttr(__('Vouchers')); ?>">
        <?= /* @noEscape */ $_order->formatPrice($_source->getVoucherAmount()) ?>
    </td>
</tr>

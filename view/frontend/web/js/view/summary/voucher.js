define(
    [
        'Magento_Checkout/js/view/summary/abstract-total',
        'Magento_Checkout/js/model/quote',
        'Magento_Catalog/js/price-utils',
        'Magento_Checkout/js/model/totals'
    ],
    function (Component, quote, priceUtils, totals) {
        "use strict";
        return Component.extend({
            defaults: {
                template: 'Incert_Voucher/summary/voucher'
            },

            totals: quote.getTotals(),

            isDisplayed: function() {
                return this.isLoaded() && totals.getSegment('voucher').value !== 0;
            },

            isLoaded: function() {
                return this.totals() && totals.getSegment('voucher') !== null;
            },

            getValue: function() {
                return this.isLoaded() ? this.getFormattedPrice(totals.getSegment('voucher').value) : 0;
            }
        });
    }
);

<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.cart.totals">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="block-totals" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="voucher" xsi:type="array">
                                    <item name="component"  xsi:type="string">Incert_Voucher/js/view/cart/totals/voucher</item>
                                    <item name="sortOrder" xsi:type="string">90</item>
                                    <item name="config" xsi:type="array">
                                        <item name="title" xsi:type="string" translate="true">Vouchers</item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
        <referenceContainer name="cart.summary">
            <block name="checkout.cart.coupon.incert" as="incert.message" template="Incert_Voucher::cart/message.phtml" after="checkout.cart.coupon"  ifconfig="promo/incert/enabled"/>
        </referenceContainer>
<!--        <move element="checkout.cart.coupon.incert" destination="cart.discount" after="checkout.cart.coupon"/>-->
        <move element="checkout.cart.coupon.incert" destination="checkout.cart.container" after="cart.discount"/>
    </body>
</page>

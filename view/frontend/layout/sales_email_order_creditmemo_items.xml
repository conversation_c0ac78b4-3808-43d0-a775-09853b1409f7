<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Email Creditmemo Items List" design_abstraction="custom">
    <body>
        <referenceBlock name="creditmemo_totals">
            <block class="Incert\Voucher\Block\Sales\Order\Voucher" name="voucher_refund" template="Incert_Voucher::sales/email/order/voucher.phtml">
                <action method="setIsPlaneMode">
                    <argument name="value" xsi:type="string">1</argument>
                </action>
                <action method="setIsCreditMemo">
                    <argument name="value" xsi:type="string">1</argument>
                </action>
            </block>
        </referenceBlock>
    </body>
</page>

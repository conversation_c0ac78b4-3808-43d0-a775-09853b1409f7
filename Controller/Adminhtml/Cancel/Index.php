<?php

namespace CopeX\Plc\Controller\Adminhtml\Cancel;


use CopeX\Plc\Helper\Module;
use Magento\Framework\App\Filesystem\DirectoryList;

class Index extends \Magento\Backend\App\Action
{
    protected $_responseFactory;
    protected $_redirect;
    protected $plcHelper;
    private Module $moduleHelper;

    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \CopeX\Plc\Helper\Data $helper,
        \Magento\Framework\App\ResponseFactory $responseFactory,
        DirectoryList $directoryList,
        Module $moduleHelper
    )
    {
        $this->_responseFactory = $responseFactory;
        $this->plcHelper = $helper;
        $this->moduleHelper = $moduleHelper;
        parent::__construct($context);
    }


    public function execute()
    {
        $redirectUrl = $this->_redirect->getRedirectUrl();
        if ($this->moduleHelper->isModuleEnabled()) {
            $labelId = $this->getRequest()->getParam('label_id');
            $orderId = $this->getRequest()->getParam('order_id');
            if ($orderId != null && $labelId != null) {
                $label = $this->plcHelper->getLabelById($labelId);
                if($label->getId() && $label->getOrderId() == $orderId){
                    $order = $this->plcHelper->getOrder($orderId);
                    $com = $this->plcHelper->getPLCUtilsCommunicator($order->getStoreId());
                    $response = $com->cancelLabel($label->getPostCodeNumber());
                    if ($response != null) {
                        if ($response->getErrorMessage() == null) {
                            $this->plcHelper->deleteLabel($label);
                            $this->messageManager->addSuccessMessage(__("Label cancelled."));
                        } else {
                            $this->messageManager->addErrorMessage(__("Unable to cancel label."));
                        }
                    } else {
                        $this->messageManager->addErrorMessage(__("Unknown error, response is null"));
                    }
                }
                else {
                    $this->messageManager->addErrorMessage(__("Label not found"));
                }
            }
        }
        else {
            $this->messageManager->addErrorMessage(__("Module is not enabled"));
        }
        $this->_responseFactory->create()->setRedirect($redirectUrl)->sendResponse();
        exit;
    }

    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('CopeX_Plc::cancel');
    }

}
<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace Incert\Voucher\Controller\Adminhtml\Product;

use Incert\Voucher\Helper\Voucher;
use Incert\Voucher\Model\Product\ImageDownloader;
use Magento\Backend\Model\View\Result\ForwardFactory;
use Magento\Backend\Model\View\Result\RedirectFactory;
use Magento\Catalog\Model\ProductFactory;
use Magento\Catalog\Model\ResourceModel\Product;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Notification\NotifierInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class Post extends \Magento\Backend\App\Action implements \Magento\Framework\App\Action\HttpPostActionInterface
{

    const ADMIN_RESOURCE = 'Incert_Voucher::Voucher_save';

    private ForwardFactory $resultForwardFactory;
    private RedirectFactory $redirectFactory;
    private Voucher $voucher;
    private ProductFactory $productFactory;
    private Product $resourceModel;
    private StoreManagerInterface $storeManager;
    private SerializerInterface $serializer;
    private ScopeConfigInterface $scopeConfig;
    private ImageDownloader $imageDownloader;

    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        ForwardFactory $resultForwardFactory,
        RedirectFactory $redirectFactory,
        Voucher $voucher,
        ProductFactory $productFactory,
        Product $resourceModel,
        StoreManagerInterface $storeManager,
        SerializerInterface $serializer,
        ScopeConfigInterface $scopeConfig,
        ImageDownloader $imageDownloader
    ) {
        parent::__construct($context);
        $this->resultForwardFactory = $resultForwardFactory;
        $this->redirectFactory = $redirectFactory;
        $this->voucher = $voucher;
        $this->productFactory = $productFactory;
        $this->resourceModel = $resourceModel;
        $this->storeManager = $storeManager;
        $this->serializer = $serializer;
        $this->scopeConfig = $scopeConfig;
        $this->imageDownloader = $imageDownloader;
    }

    /**
     * Index action
     * Action to get data from the Incert product
     * and create a new Magento Product with the details
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $productId = $this->getRequest()->getParam('product_id');
        if (!$productId) {
            return $this->resultForwardFactory->create()->forward('noroute');
        }
        $product = $this->prepareAndSaveProduct($productId);
        $this->messageManager->addSuccessMessage(__('Incert Vouchers') . ": " . __('New Voucher created'));
        $resultRedirect = $this->redirectFactory->create();
        return $resultRedirect->setPath(
            'catalog/*/edit',
            ['id' => $product->getEntityId(), 'back' => null, '_current' => true]
        );
        // return $resultRedirect->setPath('catalog/product/index');
    }

    private function getStoreLanguage($store)
    {
        $storeLocale = $this->scopeConfig->getValue('general/locale/code', ScopeInterface::SCOPE_STORE, $store);
        return substr($storeLocale, 0, 2);
    }

    private function getDescriptionByLanguage($incertProduct, $lang)
    {
        $descriptions = $incertProduct->getProductDescriptions();
        foreach ($descriptions as $description) {
            if ($description['language']['code'] === $lang) {
                return $description;
            }
        }
        return false;
    }

    private function saveStoreViewValues(\Magento\Catalog\Model\Product $product, $incertProduct, string $defaultLang)
    {
        foreach ($this->storeManager->getStores(false) as $store) {
            $storeLang = $this->getStoreLanguage($store);
            if ($storeLang != $defaultLang) {
                $data = $this->getDescriptionByLanguage($incertProduct, $storeLang);
                if ($data) {
                    $product->setStoreId($store->getId());
                    $product->addData([
                        'name'              => $data['name'],
                        'description'       => $data['description'],
                        'short_description' => $data['shortDescription'],
                        'meta_keywords'     => $data['keywords'],
                    ]);
                    $this->resourceModel->save($product);
                }
            }
        }
    }

    /**
     * @param mixed  $incertProduct
     * @param string $defaultLang
     * @param string $randomProductSku
     * @return \Magento\Catalog\Model\Product
     */
    public function getMagentoProductFromIncertProduct(
        mixed $incertProduct,
        string $defaultLang,
        string $randomProductSku
    ): \Magento\Catalog\Model\Product {
        $product = $this->productFactory->create();
        $defaultData = $this->getDescriptionByLanguage($incertProduct, $defaultLang);
        if (!$defaultData) {
            $defaultData = $product->getProductDescriptions()[0];
        }
        $product->setData([
            'incert_product_id'   => $incertProduct->getId(),
            'incert_product_info' => $this->serializer->serialize($incertProduct),
            'sku'                 => 'INCERT_' . $randomProductSku,
            'name'                => $defaultData['name'],
            'description'         => $defaultData['description'],
            'short_description'   => $defaultData['shortDescription'],
            'meta_keyword'            => $defaultData['keywords'],
            'type_id'             => \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE,
            'attribute_set_id'    => $this->voucher->getConfig()->getAttributeSetId(),
            'status'              => \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED,
            'weight'              => 0,
            'visibility'          => \Magento\Catalog\Model\Product\Visibility::VISIBILITY_BOTH,
            'stock_data'          => [
                'use_config_manage_stock' => 0,
                'manage_stock'            => 0,
                'is_in_stock'             => 1,
            ],
            'website_ids'         => array_keys($this->storeManager->getWebsites(true)),
        ]);
        $this->setImages($product, $incertProduct);
        return $product;
    }

    /**
     * @param mixed $productId
     * @return \Magento\Catalog\Model\Product
     * @throws \Exception
     */
    public function prepareAndSaveProduct(mixed $productId): \Magento\Catalog\Model\Product
    {
        $defaultLang = $this->getStoreLanguage($this->storeManager->getDefaultStoreView());
        $products = $this->voucher->getProductsFromApi();
        $incertProduct = current(array_filter($products, function ($item) use ($productId) {
            return $item->getId() == $productId;
        }));
        $randomProductSku = $incertProduct->getId() . rand(0, 99999);
        $product = $this->getMagentoProductFromIncertProduct($incertProduct, $defaultLang, $randomProductSku);
        try {
            $this->resourceModel->save($product);
        } catch (\Magento\UrlRewrite\Model\Exception\UrlAlreadyExistsException $e) {
            $product->setUrlKey($product->getUrlKey() . "-" . $randomProductSku);
            $product->setUrlPath("");
            $this->resourceModel->save($product);
        }
        $this->_eventManager->dispatch(
            'controller_action_catalog_product_save_entity_after',
            ['controller' => $this, 'product' => $product]
        );
        $product->unsetData('enabled');
        $this->saveStoreViewValues($product, $incertProduct, $defaultLang);
        return $product;
    }

    private function setImages(\Magento\Catalog\Model\Product $product, mixed $incertProduct)
    {
        $fileName = $product->getSku() . rand(0,500);
        if ($link = $incertProduct->getImageLink()) {
            $imageLink = $this->getLinkByType($link, $fileName);
            if ($imageLink) {
                $product->addImageToMediaGallery($imageLink,['image','small_image','thumbnail'], false, false);
            }
        }
        if($link = $incertProduct->getThumbnailImageLink()){
            $imageLink = $this->getLinkByType($link, $fileName, "_thumbnail.");
            if ($imageLink) {
                $product->addImageToMediaGallery($imageLink,['thumbnail'], false, true);
            }
        }
    }

    private function getLinkByType($link, $fileName, $type = "")
    {
        $baseName = basename($link);
        $fileExtension = pathinfo($baseName, PATHINFO_EXTENSION);
        return $this->imageDownloader->downloadImage($link, $fileName . $type . "." . $fileExtension);
    }
}





<?php

namespace Incert\Voucher\Controller\Adminhtml\Purchase;

use Incert\Client\Model\Response\BadRequestResponse;
use Incert\Voucher\Helper\Voucher;
use Incert\Voucher\Model\ResourceModel\Purchase\Collection;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\App\ResponseFactory;
use Magento\Framework\App\ResponseInterface;

class Download extends Action
{

    const ADMIN_RESOURCE = 'Incert_Voucher::Purchase';

    private FileFactory $fileFactory;
    private ResponseFactory $responseFactory;
    private Voucher $voucher;
    private Collection $collection;

    public function __construct(
        Context $context,
        FileFactory $fileFactory,
        ResponseFactory $responseFactory,
        Voucher $voucher,
        Collection $collection
    ) {
        parent::__construct($context);
        $this->fileFactory = $fileFactory;
        $this->responseFactory = $responseFactory;
        $this->voucher = $voucher;
        $this->collection = $collection;
    }



    /**
     * Execute action based on request and return result
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        $redirectUrl = $this->_redirect->getRedirectUrl();
        $this->_response = $this->responseFactory->create()->setRedirect($redirectUrl);
        $entityId = $this->_request->getParam('entity_id');
        if ($entityId) {
            $entity = $this->collection->addFieldToFilter('entity_id', $entityId)->getFirstItem();
            if ($entity && $entity->getId()) {
                $fileDetails = $this->voucher->downloadVoucher($entity->getIncertOrderId(), $entity->getVoucherCode());
                if ($fileDetails instanceof \Incert\Client\Model\Shop\Response\Pdf) {
                    return $this->_response = $this->fileFactory->create(
                        $fileDetails['fileName'] ?? (string)__("Your Voucher") . ".pdf",
                        ["type" => "string", "value" => $fileDetails['stream'] ?? $fileDetails->getData()],
                        DirectoryList::VAR_DIR,
                        'application/pdf'
                    );
                } else {
                    /** @var BadRequestResponse $fileDetails */
                    $this->messageManager->addErrorMessage(__($fileDetails->getMessage()));
                }
                $this->_response->setRedirect($redirectUrl)->sendResponse();
                return $this->_response;
            }
        }
    }
}
<?php

namespace Cope<PERSON>\Plc\Controller\Index;

use Cope<PERSON>\Plc\Helper\Config;
use CopeX\Plc\Helper\Module;
use CopeX\Plc\Helper\Retouring;
use CopeX\Plc\Helper\Data;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use \Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Registry;
use Magento\Sales\Controller\AbstractController\OrderLoaderInterface;

class Index implements HttpPostActionInterface
{

    protected Retouring $_retouring;
    protected Data $plcHelper;
    protected FileFactory $fileFactory;
    private RedirectInterface $redirect;
    private ResultFactory $resultFactory;
    private ManagerInterface $messageManager;
    private RequestInterface $request;
    private OrderLoaderInterface $orderLoader;
    private Registry $registry;
    private Config $config;
    private Module $moduleHelper;

    public function __construct(
        FileFactory $fileFactory,
        Retouring $retouring,
        Data $data,
        RedirectInterface $redirect,
        ResultFactory $resultFactory,
        ManagerInterface $messageManager,
        RequestInterface $request,
        OrderLoaderInterface $orderLoader,
        Config $config,
        Module $moduleHelper,
        Registry $registry

    ) {
        $this->_retouring = $retouring;
        $this->fileFactory = $fileFactory;
        $this->plcHelper = $data;
        $this->redirect = $redirect;
        $this->resultFactory = $resultFactory;
        $this->messageManager = $messageManager;
        $this->request = $request;
        $this->orderLoader = $orderLoader;
        $this->registry = $registry;
        $this->config = $config;
        $this->moduleHelper = $moduleHelper;
    }

    /**
     * Execute view action
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $referrer = $this->redirect->getRefererUrl();
        $response = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setUrl($referrer);
        if ($this->moduleHelper->isModuleEnabled()) {
            try {
                if ($this->_retouring->isReturnEnabled() && $this->_retouring->isFrontendReturnEnabled()) {
                    $this->orderLoader->load($this->request);
                    $order = $this->registry->registry('current_order');
                    if ($order && $order->getId() && $this->_retouring->isReturnValid($order)) {
                        $orderId = $order->getId();
                        $lastLabel = $this->plcHelper->getLabelByOrderId($orderId);
                        if ($this->_retouring->isLabelReturn($lastLabel)) {
                            $response = $this->fileFactory->create(
                                $lastLabel->getLabelName(),
                                [
                                    "type"  => "filename",
                                    "value" => Data::ROOT_PATH . Data::PATH . $lastLabel->getLabelName(),
                                ],
                                DirectoryList::MEDIA
                            );
                        } else {
                            $labelResponse = $this->plcHelper->createLabel($order,
                                $this->plcHelper->getReturnCode($orderId),
                                $lastLabel->getWeight() ?? $this->config->getDefaultWeight(),
                                $lastLabel->getLength() ?? $this->config->getDefaultLength(),
                                $lastLabel->getHeight() ?? $this->config->getDefaultHeight(),
                                $lastLabel->getWidth() ?? $this->config->getDefaultWidth(), $this->config->getCreateShipment());
                            if ($labelResponse->isSuccess()) {
                                $this->messageManager->addSuccessMessage(__("Return Label created"));
                                $pdfDetails = $labelResponse->getData();
                                $response = $this->fileFactory->create(
                                    $pdfDetails['fileName'],
                                    [
                                        "type"  => "filename",
                                        "value" => Data::ROOT_PATH . Data::PATH . $pdfDetails['fileName'],
                                    ],
                                    DirectoryList::MEDIA
                                );
                            } else {
                                $this->messageManager->addErrorMessage(__($this->plcHelper->getErrorMessage($labelResponse)));
                            }
                        }
                    } else {
                        $this->messageManager->addErrorMessage(__("Order can't be shipped or is invalid"));
                    }
                }
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage(__($e->getMessage()));
            }
        }
        else {
            $this->messageManager->addErrorMessage(__("Module is not enabled"));
        }
        return $response;
    }
}

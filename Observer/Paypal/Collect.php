<?php

namespace Incert\Voucher\Observer\Paypal;

use Incert\Voucher\Helper\Config;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class Collect implements ObserverInterface
{
    /**
     * @var Config
     */
    public $config;

    /**
     * Collect constructor.
     * @param Config $config
     */
    public function __construct(
        Config $config
    ) {
        $this->config = $config;
    }

    /**
     * Add voucher amount to paypal as custom item
     * @param Observer $observer
     */
    public function execute(Observer $observer)
    {
        if (!$this->config->isEnabled()) {
            return;
        }

        /** @var \Magento\Payment\Model\Cart $cart */
        $cart = $observer->getEvent()->getCart();
        $salesEntity = $cart->getSalesModel();
        $voucherAmount = $salesEntity->getDataUsingMethod('voucher_amount');
        if ($voucherAmount != 0) {
            $cart->addCustomItem(__('Vouchers'), 1, $voucherAmount);
        }
    }
}

<?php

namespace Incert\Voucher\Observer\Sales\Quote\Totals;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

use Incert\Voucher\Helper\Config;
use Incert\Voucher\Helper\CouponCodes;

class After implements ObserverInterface
{

    private Config $config;
    private CouponCodes $couponCodes;

    public function __construct( Config $config, CouponCodes $couponCodes)
    {
        $this->config = $config;
        $this->couponCodes = $couponCodes;
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        if ($this->config->isEnabled()) {
            $quote = $observer->getQuote();
            $quote->setCouponCode(implode(",", array_merge($this->couponCodes->getMagentoCouponCodes(),$this->couponCodes->getIncertVouchers())));
        }
    }
}
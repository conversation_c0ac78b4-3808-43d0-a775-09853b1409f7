<?php

namespace Incert\Voucher\Observer\Sales\Quote\Totals;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

use Incert\Voucher\Helper\Config;
use Incert\Voucher\Helper\CouponCodes;

class Before implements ObserverInterface
{

    private Config $config;
    private CouponCodes $couponCodes;

    public function __construct( Config $config, CouponCodes $couponCodes)
    {
        $this->config = $config;
        $this->couponCodes = $couponCodes;
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $quote = $observer->getQuote();
        if ($this->config->isEnabled()) {
            $this->extractNonMagentoCouponCodes($quote);
            $this->preventDiscountOnVoucherProducts($quote);
        }
    }

    /**
     * @param $quote
     * @return void
     */
    public function preventDiscountOnVoucherProducts($quote): void
    {
        if (!$this->config->canApplyDiscount()) {
            foreach ($quote->getAllItems() as $item) {
                if ($item->getProductType() === \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE) {
                    $item->setNoDiscount(true);
                    if ($item->getParentItem()) {
                        $item->getParentItem()->setNoDiscount(true);
                    }
                }
            }
        }
    }

    /**
     * @param $quote
     * @return void
     */
    public function extractNonMagentoCouponCodes($quote): void
    {
        $couponCode = $quote->getCouponCode();
        if ($couponCode) {
            $this->couponCodes->splitCouponCodes($couponCode, $this->config->getPattern());
            $quote->setCouponCode(implode(",", $this->couponCodes->getMagentoCouponCodes()));
        }
    }
}
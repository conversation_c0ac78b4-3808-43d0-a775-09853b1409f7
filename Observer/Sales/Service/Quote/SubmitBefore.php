<?php

namespace Incert\Voucher\Observer\Sales\Service\Quote;

use Incert\Voucher\Exception\InvalidVoucher;
use Incert\Voucher\Exception\VoucherAmountChanged;
use Incert\Voucher\Helper\Config;
use Incert\Voucher\Model\Voucher\Management;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Store\Model\ScopeInterface;

class SubmitBefore implements ObserverInterface
{

    private Management $management;
    private Config $config;

    public function __construct(Management $management, Config $config)
    {
        $this->management = $management;
        $this->config = $config;
    }

    /**
     * Assign quote address data to order
     * @param Observer $observer
     */
    public function execute(Observer $observer)
    {
        /**
         * @var \Magento\Sales\Model\Order
         * @var \Magento\Quote\Model\Quote
         */
        $order = $observer->getOrder();
        $quote = $observer->getQuote();
        $addresses = $quote->getAllAddresses();
        foreach ($addresses as $address) {
            if ($address->getVoucherAmount() != 0) {
                $order->setVoucherAmount($address->getVoucherAmount());
                $order->setBaseVoucherAmount($address->getBaseVoucherAmount());
                $this->validateVouchers($quote);
            }
        }
    }

    /**
     * @param $quote
     * @return void
     * @throws InvalidVoucher
     * @throws VoucherAmountChanged
     */
    private function validateVouchers($quote)
    {
        /** @var  \Magento\Quote\Model\Quote $quote */
        $appliedCoupons = $this->management->getCollectionByQuoteId($quote->getId());
        foreach ($appliedCoupons as $appliedCoupon) {
            $voucherResult = $this->management->checkVoucher($appliedCoupon->getCode(), false);
            if ($voucherResult->hasError()) {
                throw new InvalidVoucher(__(sprintf($voucherResult->getErrorMessage($this->config->getStoreLanguage($quote->getStoreId())),"%1"), $appliedCoupon->getCode()));
            }
            if($appliedCoupon->getAmount() > $voucherResult->getCurrentAmount()){
                throw new VoucherAmountChanged($appliedCoupon->getCode());
            }
        }
    }
}

<?php

namespace Incert\Voucher\Observer\Sales\Order\Payment;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class Refund implements ObserverInterface
{
    public $management;

    public $creditmemo;

    public function __construct(
        \Incert\Voucher\Model\Voucher\Management $management,
        \Incert\Voucher\Helper\Creditmemo $creditmemo
    ) {
        $this->creditmemo = $creditmemo;
        $this->management = $management;
    }

    /**
     * Actions after refund
     * @param Observer $observer
     * @return $this
     */
    public function execute(Observer $observer)
    {
        $creditMemo = $observer->getCreditmemo();
        $this->returnCreditToVoucher($creditMemo);
        $this->disableVouchers($creditMemo);
        $this->setOrderVoucherAmount($creditMemo);

        return $this;
    }

    /**
     * Set refunded amount in order object
     * @param \Magento\Sales\Model\Order\Creditmemo $creditMemo
     */
    protected function setOrderVoucherAmount($creditMemo)
    {
        $order = $creditMemo->getOrder();
        if ($order->getVoucherAmount() == 0) {
            return;
        }

        $order->setVoucherRefunded($order->getVoucherRefunded() + $creditMemo->getVoucherAmount());
        $order->setBaseVoucherRefunded($order->getBaseVoucherRefunded() + $creditMemo->getBaseVoucherAmount());
    }

    /**
     * Cancel voucher
     * @param \Magento\Sales\Model\Order\Creditmemo $creditMemo
     */
    protected function disableVouchers($creditMemo)
    {
        $items = $creditMemo->getAllItems();
        if (count($items) == 0) {
            return;
        }

        foreach ($items as $item) {
            $orderItem = $item->getOrderItem();
            if ($orderItem->getProductType() === \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE) {
                $this->management->cancelVoucherProduct($item);
            }
        }
    }

    public function returnCreditToVoucher($creditMemo)
    {
        $this->creditmemo->refundVoucherOnCreditmemo($creditMemo);
    }
}

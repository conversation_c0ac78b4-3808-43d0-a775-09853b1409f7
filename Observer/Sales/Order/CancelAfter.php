<?php

namespace Incert\Voucher\Observer\Sales\Order;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class CancelAfter implements ObserverInterface
{

    /**
     * @var \Incert\Voucher\Helper\Order
     */
    public $order;

    /**
     * CancelAfter constructor.
     * @param \Incert\Voucher\Helper\Order $order
     */
    public function __construct(
        \Incert\Voucher\Helper\Order $order
    ) {
        $this->order = $order;
    }

    /**
     * After order place observer
     * @param Observer $observer
     * @return $this
     * @throws \Exception
     */
    public function execute(Observer $observer)
    {
        $this->order->cancelOrder($observer->getOrder());
    }
}

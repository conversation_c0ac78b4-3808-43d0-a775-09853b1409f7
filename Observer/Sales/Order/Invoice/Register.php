<?php

namespace Incert\Voucher\Observer\Sales\Order\Invoice;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class Register implements ObserverInterface
{


    /**
     * Register Invoice observer
     * @param Observer $observer
     * @return $this
     */
    public function execute(Observer $observer)
    {
        /**
         * @var \Magento\Sales\Model\Order
         */
        $order = $observer->getOrder();
        $invoice = $observer->getInvoice();

        $order->setVoucherInvoiced( $order->getVoucherInvoiced() + $invoice->getVoucherAmount());
        $order->setBaseVoucherInvoiced($order->getBaseVoucherInvoiced() + $invoice->getBaseVoucherAmount());

        return $this;
    }
}

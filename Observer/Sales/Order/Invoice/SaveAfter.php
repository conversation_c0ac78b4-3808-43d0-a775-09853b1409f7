<?php

namespace Incert\Voucher\Observer\Sales\Order\Invoice;

use Incert\Voucher\Helper\Purchase;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Sales\Model\Order\Invoice;

class SaveAfter implements ObserverInterface
{

    private Purchase $purchase;

    public function __construct( Purchase $purchase)
    {
        $this->purchase = $purchase;
    }

    /**
     * After Invoice pay observer
     * @param Observer $observer
     * @return $this
     */
    public function execute(Observer $observer)
    {
        /**
         * @var \Magento\Sales\Model\Order
         */
        $invoice = $observer->getInvoice();
        if ($invoice->getData(Save::NEW_INVOICE)) {
            $this->purchase->orderValidatedVoucherProducts($invoice);
        }

        return $this;
    }
}

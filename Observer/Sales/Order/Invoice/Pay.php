<?php

namespace Incert\Voucher\Observer\Sales\Order\Invoice;

use Incert\Voucher\Helper\Purchase;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class Pay implements ObserverInterface
{
    /**
     * @var Purchase
     */
    public $purchase;

    /**
     * Pay constructor.
     * @param Purchase $purchase
     */
    public function __construct(
        Purchase $purchase
    ) {
        $this->purchase = $purchase;
    }

    /**
     * After Invoice pay observer
     * @param Observer $observer
     */
    public function execute(Observer $observer)
    {
        $this->purchase->checkVoucherProducts($observer->getInvoice());
    }

}

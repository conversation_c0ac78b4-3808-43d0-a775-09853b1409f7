<?php

namespace Incert\Voucher\Observer\Sales\Order\Invoice;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Sales\Model\Order\Invoice;

class Save implements ObserverInterface
{

    const NEW_INVOICE = 'incert_new_invoice';

    /**
     * After Invoice pay observer
     * @param Observer $observer
     * @return $this
     */
    public function execute(Observer $observer)
    {
        /**
         * @var \Magento\Sales\Model\Order
         */
        $invoice = $observer->getInvoice();
        $storedInvoiceData = $invoice->getStoredData();
        $previousInvoiceState = $storedInvoiceData['state'] ?? null;
        if ($invoice->getState() == Invoice::STATE_PAID
            && $previousInvoiceState != Invoice::STATE_PAID
        ) {
            $invoice->setData(self::NEW_INVOICE,true);
        }

        return $this;
    }
}

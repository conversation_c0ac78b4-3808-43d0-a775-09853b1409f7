<?php

namespace Incert\Voucher\Observer\Sales\Order;

use Incert\Voucher\Helper\Purchase;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class PlaceAfter implements ObserverInterface
{

    /**
     * @var Purchase
     */
    public $purchase;

    /**
     * PlaceAfter constructor.
     * @param Purchase $purchase
     */
    public function __construct(
        Purchase $purchase
    ) {
        $this->purchase = $purchase;
    }

    /**
     * After order place observer
     * @param Observer $observer
     * @return $this
     * @throws \Exception
     */
    public function execute(Observer $observer)
    {
        if ($observer->hasData('orders')) {
            $orders = $observer->getOrders();
        } else {
            $orders = [$observer->getOrder()];
        }

        foreach ($orders as $order) {
            $this->activateVoucher($order);
        }
    }

    /**
     * Activate vouchers if invoice already exist.
     * @param $order
     */
    public function activateVoucher($order)
    {
        if ($order->hasInvoices()) {
            $invoiceCollection = $order->getInvoiceCollection();
            foreach ($invoiceCollection as $invoice) {
                if ($invoice->getId()) {
                    $this->purchase->checkVoucherProducts($invoice);
                    $this->purchase->orderValidatedVoucherProducts($invoice);
                }
            }
        }
    }

}

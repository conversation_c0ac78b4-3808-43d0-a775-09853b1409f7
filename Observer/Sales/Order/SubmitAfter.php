<?php

namespace Incert\Voucher\Observer\Sales\Order;

use Incert\Voucher\Helper\Order;
use Incert\Voucher\Model\Voucher\Management;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class SubmitAfter implements ObserverInterface
{

    private Order $order;
    private Management $management;

    public function __construct( Order $order, Management $management ) {
        $this->order = $order;
        $this->management = $management;
    }

    /**
     * Observer controller
     * @param Observer $observer
     * @return $this
     * @throws \Exception
     */
    public function execute(Observer $observer)
    {
        if ($observer->hasData('orders')) {
            $orders = $observer->getOrders();
        } else {
            $orders = [$observer->getOrder()];
        }

        foreach ($orders as $order) {
            $this->management->saveOrderToAppliedVouchers($order);
            $this->order->redeemVouchers($order);
        }
    }

}

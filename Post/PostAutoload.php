<?php
/**
 * File to load generated classes once at once time
 * @package Post
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
/**
 * Includes for all generated classes files
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
require_once __DIR__ . '/PostWsdlClass.php';
require_once __DIR__ . '/Entity/Base/PostStructEntityBase.php';
require_once __DIR__ . '/Import/Address/PostStructImportAddress.php';
require_once __DIR__ . '/Array/Row/PostStructArrayOfAddressRow.php';
require_once __DIR__ . '/Import/Response/PostStructImportShipmentReturnImageResponse.php';
require_once __DIR__ . '/Import/Image/PostStructImportShipmentReturnImage.php';
require_once __DIR__ . '/Import/Response/PostStructImportShipmentResponse.php';
require_once __DIR__ . '/Import/Barcode/PostStructImportShipmentAndGenerateBarcode.php';
require_once __DIR__ . '/Import/Response/PostStructImportShipmentAndGenerateBarcodeResponse.php';
require_once __DIR__ . '/Import/Response/PostStructImportAddressResponse.php';
require_once __DIR__ . '/Perform/Response/PostStructPerformEndOfDayResponse.php';
require_once __DIR__ . '/Cancel/Response/PostStructCancelShipmentsResponse.php';
require_once __DIR__ . '/Array/Result/PostStructArrayOfCancelShipmentResult.php';
require_once __DIR__ . '/Cancel/Result/PostStructCancelShipmentResult.php';
require_once __DIR__ . '/Cancel/Row/PostStructCancelShipmentRow.php';
require_once __DIR__ . '/Array/Row/PostStructArrayOfCancelShipmentRow.php';
require_once __DIR__ . '/Printer/Row/PostStructPrinterRow.php';
require_once __DIR__ . '/Cancel/Shipments/PostStructCancelShipments.php';
require_once __DIR__ . '/Perform/Day/PostStructPerformEndOfDay.php';
require_once __DIR__ . '/Additional/Row/PostStructAdditionalInformationRow.php';
require_once __DIR__ . '/Shipment/Entry/PostStructShipmentDocumentEntry.php';
require_once __DIR__ . '/Import/Shipment/PostStructImportShipment.php';
require_once __DIR__ . '/Shipment/Row/PostStructShipmentRow.php';
require_once __DIR__ . '/Array/Entry/PostStructArrayOfShipmentDocumentEntry.php';
require_once __DIR__ . '/Address/Row/PostStructAddressRow.php';
require_once __DIR__ . '/Array/Row/PostStructArrayOfColloRow.php';
require_once __DIR__ . '/Collo/Row/PostStructColloCodeRow.php';
require_once __DIR__ . '/Array/Row/PostStructArrayOfAdditionalInformationRow.php';
require_once __DIR__ . '/Array/Row/PostStructArrayOfColloCodeRow.php';
require_once __DIR__ . '/Collo/Row/PostStructColloArticleRow.php';
require_once __DIR__ . '/Collo/Row/PostStructColloRow.php';
require_once __DIR__ . '/Array/Row/PostStructArrayOfColloArticleRow.php';
require_once __DIR__ . '/Array/Ofstring/PostStructArrayOfstring.php';
require_once __DIR__ . '/Import/PostServiceImport.php';
require_once __DIR__ . '/Perform/PostServicePerform.php';
require_once __DIR__ . '/Cancel/PostServiceCancel.php';
require_once __DIR__ . '/PostClassMap.php';
require_once __DIR__ . '/PostSoapClient.php';

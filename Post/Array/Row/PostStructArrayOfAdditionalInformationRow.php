<?php
/**
 * File for class PostStructArrayOfAdditionalInformationRow
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
/**
 * This class stands for PostStructArrayOfAdditionalInformationRow originally named ArrayOfAdditionalInformationRow
 * Meta informations extracted from the WSDL
 * - from schema : {@link https://abn-plc.post.at/DataService/Post.Webservice/ShippingService.svc?xsd=xsd0}
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
class PostStructArrayOfAdditionalInformationRow extends PostWsdlClass
{
    /**
     * The AdditionalInformationRow
     * Meta informations extracted from the WSDL
     * - maxOccurs : unbounded
     * - minOccurs : 0
     * - nillable : true
     * @var PostStructAdditionalInformationRow
     */
    public $AdditionalInformationRow;
    /**
     * Constructor method for ArrayOfAdditionalInformationRow
     * @see parent::__construct()
     * @param PostStructAdditionalInformationRow $_additionalInformationRow
     * @return PostStructArrayOfAdditionalInformationRow
     */
    public function __construct($_additionalInformationRow = NULL)
    {
        parent::__construct(array('AdditionalInformationRow'=>$_additionalInformationRow),false);
    }
    /**
     * Get AdditionalInformationRow value
     * @return PostStructAdditionalInformationRow|null
     */
    public function getAdditionalInformationRow()
    {
        return $this->AdditionalInformationRow;
    }
    /**
     * Set AdditionalInformationRow value
     * @param PostStructAdditionalInformationRow $_additionalInformationRow the AdditionalInformationRow
     * @return PostStructAdditionalInformationRow
     */
    public function setAdditionalInformationRow($_additionalInformationRow)
    {
        return ($this->AdditionalInformationRow = $_additionalInformationRow);
    }
    /**
     * Returns the current element
     * @see PostWsdlClass::current()
     * @return PostStructAdditionalInformationRow
     */
    public function current() :mixed
    {
        return parent::current();
    }
    /**
     * Returns the indexed element
     * @see PostWsdlClass::item()
     * @param int $_index
     * @return PostStructAdditionalInformationRow
     */
    public function item($_index)
    {
        return parent::item($_index);
    }
    /**
     * Returns the first element
     * @see PostWsdlClass::first()
     * @return PostStructAdditionalInformationRow
     */
    public function first()
    {
        return parent::first();
    }
    /**
     * Returns the last element
     * @see PostWsdlClass::last()
     * @return PostStructAdditionalInformationRow
     */
    public function last()
    {
        return parent::last();
    }
    /**
     * Returns the element at the offset
     * @see PostWsdlClass::last()
     * @param int $_offset
     * @return PostStructAdditionalInformationRow
     */
    public function offsetGet($_offset) :mixed
    {
        return parent::offsetGet($_offset);
    }
    /**
     * Returns the attribute name
     * @see PostWsdlClass::getAttributeName()
     * @return string AdditionalInformationRow
     */
    public function getAttributeName()
    {
        return 'AdditionalInformationRow';
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    public function __toString() :string
    {
        return self::class;
    }
}

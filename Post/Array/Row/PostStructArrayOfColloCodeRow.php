<?php
/**
 * File for class PostStructArrayOfColloCodeRow
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
/**
 * This class stands for PostStructArrayOfColloCodeRow originally named ArrayOfColloCodeRow
 * Meta informations extracted from the WSDL
 * - from schema : {@link https://abn-plc.post.at/DataService/Post.Webservice/ShippingService.svc?xsd=xsd0}
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
class PostStructArrayOfColloCodeRow extends PostWsdlClass
{
    /**
     * The ColloCodeRow
     * Meta informations extracted from the WSDL
     * - maxOccurs : unbounded
     * - minOccurs : 0
     * - nillable : true
     * @var PostStructColloCodeRow
     */
    public $ColloCodeRow;
    /**
     * Constructor method for ArrayOfColloCodeRow
     * @see parent::__construct()
     * @param PostStructColloCodeRow $_colloCodeRow
     * @return PostStructArrayOfColloCodeRow
     */
    public function __construct($_colloCodeRow = NULL)
    {
        parent::__construct(array('ColloCodeRow'=>$_colloCodeRow),false);
    }
    /**
     * Get ColloCodeRow value
     * @return PostStructColloCodeRow|null
     */
    public function getColloCodeRow()
    {
        return $this->ColloCodeRow;
    }
    /**
     * Set ColloCodeRow value
     * @param PostStructColloCodeRow $_colloCodeRow the ColloCodeRow
     * @return PostStructColloCodeRow
     */
    public function setColloCodeRow($_colloCodeRow)
    {
        return ($this->ColloCodeRow = $_colloCodeRow);
    }
    /**
     * Returns the current element
     * @see PostWsdlClass::current()
     * @return PostStructColloCodeRow
     */
    public function current() :mixed
    {
        return parent::current();
    }
    /**
     * Returns the indexed element
     * @see PostWsdlClass::item()
     * @param int $_index
     * @return PostStructColloCodeRow
     */
    public function item($_index)
    {
        return parent::item($_index);
    }
    /**
     * Returns the first element
     * @see PostWsdlClass::first()
     * @return PostStructColloCodeRow
     */
    public function first()
    {
        return parent::first();
    }
    /**
     * Returns the last element
     * @see PostWsdlClass::last()
     * @return PostStructColloCodeRow
     */
    public function last()
    {
        return parent::last();
    }
    /**
     * Returns the element at the offset
     * @see PostWsdlClass::last()
     * @param int $_offset
     * @return PostStructColloCodeRow
     */
    public function offsetGet($_offset) :mixed
    {
        return parent::offsetGet($_offset);
    }
    /**
     * Returns the attribute name
     * @see PostWsdlClass::getAttributeName()
     * @return string ColloCodeRow
     */
    public function getAttributeName()
    {
        return 'ColloCodeRow';
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    public function __toString() :string
    {
        return self::class;
    }
}

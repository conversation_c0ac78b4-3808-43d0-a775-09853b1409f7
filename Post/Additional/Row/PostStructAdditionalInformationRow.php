<?php
/**
 * File for class PostStructAdditionalInformationRow
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
/**
 * This class stands for PostStructAdditionalInformationRow originally named AdditionalInformationRow
 * Meta informations extracted from the WSDL
 * - from schema : {@link https://abn-plc.post.at/DataService/Post.Webservice/ShippingService.svc?xsd=xsd0}
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
class PostStructAdditionalInformationRow extends PostWsdlClass
{
    /**
     * The ThirdPartyID
     * Meta informations extracted from the WSDL
     * - minOccurs : 0
     * - nillable : true
     * @var string
     */
    public $ThirdPartyID;
    /**
     * The Value1
     * Meta informations extracted from the WSDL
     * - minOccurs : 0
     * - nillable : true
     * @var string
     */
    public $Value1;
    /**
     * The Value2
     * Meta informations extracted from the WSDL
     * - minOccurs : 0
     * - nillable : true
     * @var string
     */
    public $Value2;
    /**
     * The Value3
     * Meta informations extracted from the WSDL
     * - minOccurs : 0
     * - nillable : true
     * @var string
     */
    public $Value3;
    /**
     * The Value4
     * Meta informations extracted from the WSDL
     * - minOccurs : 0
     * - nillable : true
     * @var string
     */
    public $Value4;
    /**
     * Constructor method for AdditionalInformationRow
     * @see parent::__construct()
     * @param string $_thirdPartyID
     * @param string $_value1
     * @param string $_value2
     * @param string $_value3
     * @param string $_value4
     * @return PostStructAdditionalInformationRow
     */
    public function __construct($_thirdPartyID = NULL,$_value1 = NULL,$_value2 = NULL,$_value3 = NULL,$_value4 = NULL)
    {
        parent::__construct(array('ThirdPartyID'=>$_thirdPartyID,'Value1'=>$_value1,'Value2'=>$_value2,'Value3'=>$_value3,'Value4'=>$_value4),false);
    }
    /**
     * Get ThirdPartyID value
     * @return string|null
     */
    public function getThirdPartyID()
    {
        return $this->ThirdPartyID;
    }
    /**
     * Set ThirdPartyID value
     * @param string $_thirdPartyID the ThirdPartyID
     * @return string
     */
    public function setThirdPartyID($_thirdPartyID)
    {
        return ($this->ThirdPartyID = $_thirdPartyID);
    }
    /**
     * Get Value1 value
     * @return string|null
     */
    public function getValue1()
    {
        return $this->Value1;
    }
    /**
     * Set Value1 value
     * @param string $_value1 the Value1
     * @return string
     */
    public function setValue1($_value1)
    {
        return ($this->Value1 = $_value1);
    }
    /**
     * Get Value2 value
     * @return string|null
     */
    public function getValue2()
    {
        return $this->Value2;
    }
    /**
     * Set Value2 value
     * @param string $_value2 the Value2
     * @return string
     */
    public function setValue2($_value2)
    {
        return ($this->Value2 = $_value2);
    }
    /**
     * Get Value3 value
     * @return string|null
     */
    public function getValue3()
    {
        return $this->Value3;
    }
    /**
     * Set Value3 value
     * @param string $_value3 the Value3
     * @return string
     */
    public function setValue3($_value3)
    {
        return ($this->Value3 = $_value3);
    }
    /**
     * Get Value4 value
     * @return string|null
     */
    public function getValue4()
    {
        return $this->Value4;
    }
    /**
     * Set Value4 value
     * @param string $_value4 the Value4
     * @return string
     */
    public function setValue4($_value4)
    {
        return ($this->Value4 = $_value4);
    }
    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    public function __toString()
    {
        return self::class;
    }
}

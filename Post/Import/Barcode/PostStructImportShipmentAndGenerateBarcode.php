<?php
/**
 * File for class PostStructImportShipmentAndGenerateBarcode
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
/**
 * This class stands for PostStructImportShipmentAndGenerateBarcode originally named ImportShipmentAndGenerateBarcode
 * Meta informations extracted from the WSDL
 * - from schema : {@link https://abn-plc.post.at/DataService/Post.Webservice/ShippingService.svc?xsd=xsd0}
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
class PostStructImportShipmentAndGenerateBarcode extends PostWsdlClass
{
    /**
     * The row
     * Meta informations extracted from the WSDL
     * - minOccurs : 0
     * - nillable : true
     * @var PostStructShipmentRow
     */
    public $row;
    /**
     * Constructor method for ImportShipmentAndGenerateBarcode
     * @see parent::__construct()
     * @param PostStructShipmentRow $_row
     * @return PostStructImportShipmentAndGenerateBarcode
     */
    public function __construct($_row = NULL)
    {
        parent::__construct(array('row'=>$_row),false);
    }
    /**
     * Get row value
     * @return PostStructShipmentRow|null
     */
    public function getRow()
    {
        return $this->row;
    }
    /**
     * Set row value
     * @param PostStructShipmentRow $_row the row
     * @return PostStructShipmentRow
     */
    public function setRow($_row)
    {
        return ($this->row = $_row);
    }
    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    public function __toString()
    {
        return self::class;
    }
}

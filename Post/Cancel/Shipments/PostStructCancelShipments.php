<?php
/**
 * File for class PostStructCancelShipments
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
/**
 * This class stands for PostStructCancelShipments originally named CancelShipments
 * Meta informations extracted from the WSDL
 * - from schema : {@link https://abn-plc.post.at/DataService/Post.Webservice/ShippingService.svc?xsd=xsd0}
 * @package Post
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2017-09-18
 */
class PostStructCancelShipments extends PostWsdlClass
{
    /**
     * The shipments
     * Meta informations extracted from the WSDL
     * - minOccurs : 0
     * - nillable : true
     * @var PostStructArrayOfCancelShipmentRow
     */
    public $shipments;
    /**
     * Constructor method for CancelShipments
     * @see parent::__construct()
     * @param PostStructArrayOfCancelShipmentRow $_shipments
     * @return PostStructCancelShipments
     */
    public function __construct($_shipments = NULL)
    {
        parent::__construct(array('shipments'=>($_shipments instanceof PostStructArrayOfCancelShipmentRow)?$_shipments:new PostStructArrayOfCancelShipmentRow($_shipments)),false);
    }
    /**
     * Get shipments value
     * @return PostStructArrayOfCancelShipmentRow|null
     */
    public function getShipments()
    {
        return $this->shipments;
    }
    /**
     * Set shipments value
     * @param PostStructArrayOfCancelShipmentRow $_shipments the shipments
     * @return PostStructArrayOfCancelShipmentRow
     */
    public function setShipments($_shipments)
    {
        return ($this->shipments = $_shipments);
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    public function __toString()
    {
        return self::class;
    }
}

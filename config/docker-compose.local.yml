volumes:
    composer-cache:
        external: true

networks:
    backend:
        external: true

services:
    app:
        image: copex/nginx-php-fpm:dev
        ports:
            - "80:80"
            - "443:443"
            - "3000:3000"
            - "3001:3001"
        extra_hosts:
            - "host.docker.internal:host-gateway"
        environment:
            PHP_IDE_CONFIG: "serverName=${DEV_DOMAIN}"
            XDEBUG_MODE: debug
            XDEBUG_CONFIG: "client_host=host.docker.internal log_level=0 remote_enable=1 remote_mode=req remote_port=9000 remote_host=host.docker.internal remote_connect_back=0 remote_autostart=0 idekey=PHPSTORM"
            DOMAIN: ${DEV_DOMAIN}
            MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
            MAGEPACK_CMS_URL: "http://www.rottnerb2b.test/lieferinformationen"
            MAGEPACK_CATEGORY_PAGE_URL: "http://www.rottnerb2b.test/tresore/wertschutzschranke/"
            MAGEPACK_PRODUCT_PAGE_URL: "http://www.rottnerb2b.test/rottner-feuerschutztresor-fire-key-20-elektronikschloss.html"
        volumes:
            - ../:${DEV_MAGENTO_ROOT}
            - composer-cache:/var/www/.composer/cache
    mysql:
        environment:
            MYSQL_ROOT_PASSWORD: "r00t"
            MYSQL_USER: "magento"
            MYSQL_PASSWORD: "magento"
            MYSQL_DATABASE: "magento"
        ports:
            - "3306:3306"
    cron:
        image: tianon/true
        ports:
            - "8282:8282"
        restart: "no"
        environment:
            MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
        volumes:
            - ../:${DEV_MAGENTO_ROOT}
    search:
        ports:
            - "9200:9200"
            - "9300:9300"
    elastichq:
        image: elastichq/elasticsearch-hq
        networks:
            - backend
        ports:
            - 5000:5000
        environment:
            HQ_DEFAULT_URL: 'http://search:9200'
    redis:
        command: redis-server --maxmemory 4G --save "3600 1"
    mailcatcher:
        image: sj26/mailcatcher
        ports:
            - "1080:1080"
        networks:
            - backend

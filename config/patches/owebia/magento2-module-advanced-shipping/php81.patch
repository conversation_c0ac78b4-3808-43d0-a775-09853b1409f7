Index: Model/Carrier.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Model/Carrier.php b/Model/Carrier.php
--- a/Model/Carrier.php
+++ b/Model/Carrier.php	(date 1664957831220)
@@ -215,9 +215,13 @@
      */
     public function getAllowedMethods()
     {
-        $config = $this->getConfig(
-            $this->rateRequestFactory->create()
-        );
+        $config = null;
+        try{
+            $config = $this->getConfig(
+                $this->rateRequestFactory->create()
+            );
+        }
+        catch (\TypeError $t){}
         if (!isset($config) || !is_array($config)) {
             $this->_logger->debug("Owebia_AdvancedShipping : Invalid config");
             return [];

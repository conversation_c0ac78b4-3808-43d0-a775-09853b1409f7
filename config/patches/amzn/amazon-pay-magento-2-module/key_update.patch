Index: vendor/amzn/amazon-pay-magento-2-module/Setup/Patch/Data/PerformKeyUpgrade.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/amzn/amazon-pay-magento-2-module/Setup/Patch/Data/PerformKeyUpgrade.php b/vendor/amzn/amazon-pay-magento-2-module/Setup/Patch/Data/PerformKeyUpgrade.php
--- a/vendor/amzn/amazon-pay-magento-2-module/Setup/Patch/Data/PerformKeyUpgrade.php	
+++ b/vendor/amzn/amazon-pay-magento-2-module/Setup/Patch/Data/PerformKeyUpgrade.php	(date 1682677943201)
@@ -78,7 +78,7 @@
      */
     public function apply()
     {
-        $accessKeys = $this->getAccessKeysByScope();
+        $accessKeys = array_filter($this->getAccessKeysByScope(), fn($item) => $item['value']);
         foreach ($accessKeys as [
                 'scope' => $scopeType,
                 'scope_id' => $scopeId,

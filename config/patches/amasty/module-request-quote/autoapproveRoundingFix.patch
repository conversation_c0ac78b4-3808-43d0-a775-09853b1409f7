Index: Observer/Frontend/AutoApprove.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Observer/Frontend/AutoApprove.php b/Observer/Frontend/AutoApprove.php
--- a/Observer/Frontend/AutoApprove.php
+++ b/Observer/Frontend/AutoApprove.php	(date 1646838591000)
@@ -91,7 +91,7 @@
                 $requestedTotals += $quoteItem->getPrice();
                 $originalTotals += $this->getOriginalPrice($quoteItem);
             }
-            if ($originalTotals && $originalTotals >= $requestedTotals) {
+            if ($originalTotals && round($originalTotals,2) >= $requestedTotals) {
                 $requestedDiscount = ($originalTotals - $requestedTotals) / $originalTotals * 100;
                 $result = $requestedDiscount < $this->configHelper->getAllowedPercentForApprove();
             }

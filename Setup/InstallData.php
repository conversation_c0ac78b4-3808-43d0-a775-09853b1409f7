<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace Incert\Voucher\Setup;

use Incert\Voucher\Helper\Config;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Catalog\Setup\CategorySetupFactory;
use Magento\Eav\Model\Entity\Attribute\SetFactory as AttributeSetFactory;
use Magento\Framework\App\Cache\TypeListInterface;
use Magento\Framework\App\Config\ConfigResource\ConfigInterface;
use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

class InstallData implements InstallDataInterface
{

    private $eavSetupFactory;
    private $attributeSetFactory;
    private $categorySetupFactory;
    private ConfigInterface $configResource;
    private TypeListInterface $cacheTypeList;

    /**
     * Constructor
     * @param \Magento\Eav\Setup\EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        EavSetupFactory $eavSetupFactory,
        AttributeSetFactory $attributeSetFactory,
        CategorySetupFactory $categorySetupFactory,
        ConfigInterface $configResource,
        TypeListInterface $cacheTypeList
    ) {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->attributeSetFactory = $attributeSetFactory;
        $this->categorySetupFactory = $categorySetupFactory;
        $this->configResource = $configResource;
        $this->cacheTypeList = $cacheTypeList;
    }

    /**
     * {@inheritdoc}
     */
    public function install(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $setup->startSetup();
        try {
            $this->createAttributeSet($setup);
            $this->assignPricesToProductType($setup);
        }catch (\Exception $exception){}
        $setup->endSetup();
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @return void
     */
    private function assignPricesToProductType(ModuleDataSetupInterface $setup): void
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        // associate these attributes with new product type
        $fieldList = [
            'price',
            'special_price',
            'special_from_date',
            'special_to_date',
            'minimal_price',
            'cost',
            'tier_price',
            'weight',
        ];

        // make these attributes applicable to new product type
        foreach ($fieldList as $field) {
            $applyTo = explode(
                ',',
                $eavSetup->getAttribute(\Magento\Catalog\Model\Product::ENTITY, $field, 'apply_to')
            );
            if (!in_array(\Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE, $applyTo)) {
                $applyTo[] = \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE;
                $eavSetup->updateAttribute(
                    \Magento\Catalog\Model\Product::ENTITY,
                    $field,
                    'apply_to',
                    implode(',', $applyTo)
                );
            }
        }
    }

    private function createAttributeSet(ModuleDataSetupInterface $setup): void
    {
        $categorySetup = $this->categorySetupFactory->create(['setup' => $setup]);
        $attributeSet = $this->attributeSetFactory->create();
        $entityTypeId = $categorySetup->getEntityTypeId(\Magento\Catalog\Model\Product::ENTITY);
        $defaultAttributeSetId = $categorySetup->getDefaultAttributeSetId($entityTypeId);

        // Step 1: Create the Attribute Set
        $data = [
            'attribute_set_name' => 'Incert Voucher',
            'entity_type_id'     => $entityTypeId,
            'sort_order'         => 200,
        ];
        $attributeSet->setData($data);
        $attributeSet->validate();
        $attributeSet->save();
        $attributeSet->initFromSkeleton($defaultAttributeSetId);
        $attributeSet->save();

        $attributeSetId = $attributeSet->getId();
        // Save configuration value
        $this->configResource->saveConfig(
            Config::PATH_ATTRIBUTE_SET,
            $attributeSetId
        );
        $this->cacheTypeList->cleanType(\Magento\Framework\App\Cache\Type\Config::TYPE_IDENTIFIER);

        $attributesToAssign = [
            'special_price',
            'special_from_date',
            'special_to_date',
            'minimal_price',
            'image',
            'small_image',
            'thumbnail',
            'gallery',
            'media_gallery',
            'sku',
            'name',
            'description',
            'short_description',
            'price',
            'price_type',
            'status',
            'visibility',
            'custom_design',
            'custom_design_from',
            'custom_design_to',
            'custom_layout_update',
            'page_layout',
            'category_ids',
            'image_label',
            'small_image_label',
            'thumbnail_label',
            'created_at',
            'updated_at',
            'quantity_and_stock_status',
            'incert_product_id',
            'incert_product_info',
            'no_apply_incert_voucher',
            'url_key',
            'url_path',
            'meta_title',
            'meta_description',
            'meta_keyword',
        ];
        $setup->getConnection()->query("DELETE FROM " . $setup->getTable('eav_entity_attribute') . " where attribute_set_id = $attributeSetId and attribute_id not in (
    select attribute_id from " . $setup->getTable('eav_attribute') . " where attribute_code in ( '" .
                                       implode("','", $attributesToAssign) . "' ))");
    }
}

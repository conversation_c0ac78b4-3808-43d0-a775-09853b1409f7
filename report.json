{"tool": "phpcs", "status": "FAIL", "reports": [{"php_version": "8.3.7", "tool": "phpcs", "vendor": "incert-etourismus", "extension": "module-voucher", "version": "1.0.6", "platform": "m2", "status": "FAIL", "date": "2025-05-28T11:11:46Z", "details": {"totals": {"errors": 1, "warnings": 854, "fixable": 223}, "files": {"/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Setup/InstallData.php": {"errors": 1, "warnings": 20, "messages": [{"message": "InstallData scripts are obsolete. Please use data patches approach in module's Setup/Patch/Data dir", "source": "Magento2.Legacy.InstallUpgrade.ObsoleteInstallDataScript", "severity": 10, "fixable": false, "type": "ERROR", "line": 1, "column": 1}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 13}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 25, "column": 13}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 26, "column": 13}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 29}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 31}, {"message": "@param is not found for one or more params in method annotation", "source": "Magento2.Annotation.MethodArguments.ParamMissing", "severity": 5, "fixable": false, "type": "WARNING", "line": 34, "column": 12}, {"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 8}, {"message": "{@inheritdoc} does not import parameter annotation", "source": "Magento2.Annotation.MethodArguments.InheritDoc", "severity": 5, "fixable": false, "type": "WARNING", "line": 51, "column": 12}, {"message": "Expected 1 space after closing brace; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 59, "column": 9}, {"message": "Empty CATCH statement detected", "source": "Magento2.CodeAnalysis.EmptyBlock.DetectedCatch", "severity": 7, "fixable": false, "type": "WARNING", "line": 59, "column": 10}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 59, "column": 38}, {"message": "Newline required after opening brace", "source": "Squiz.ControlStructures.ControlSignature.NewlineAfterOpenBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 59, "column": 39}, {"message": "Closing brace must be on a line by itself", "source": "Squiz.WhiteSpace.ScopeClosingBrace.ContentBefore", "severity": 6, "fixable": true, "type": "WARNING", "line": 59, "column": 40}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 63, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 64, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 69, "column": 9}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 69, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 102, "column": 13}, {"message": "Possible raw SQL statement \"DELETE FROM \" detected.", "source": "Magento2.SQL.RawQuery.FoundRawSql", "severity": 9, "fixable": false, "type": "WARNING", "line": 168, "column": 40}, {"message": "Line exceeds 120 characters; contains 167 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 168, "column": 100}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/ViewModel/Adminhtml/ApiProduct.php": {"errors": 0, "warnings": 10, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 21}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 32, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 37, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 37, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 37, "column": 60}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 42, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/view/adminhtml/templates/product/create.phtml": {"errors": 0, "warnings": 18, "messages": [{"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 10, "column": 7}, {"message": "Expected 0 spaces after closing parenthesis; found 1", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 10, "column": 19}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 11, "column": 15}, {"message": "The use of $this in templates is deprecated. Use $block instead.", "source": "Magento2.Templates.ThisInTemplate.FoundThis", "severity": 8, "fixable": true, "type": "WARNING", "line": 11, "column": 26}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 11, "column": 33}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 17, "column": 39}, {"message": "Line exceeds 120 characters; contains 123 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 20, "column": 1}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 21, "column": 36}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 27, "column": 49}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 27, "column": 68}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 30, "column": 68}, {"message": "Expected 1 space after FOREACH keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 34, "column": 27}, {"message": "Expected 0 spaces after closing parenthesis; found 1", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 34, "column": 56}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 35, "column": 50}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 35, "column": 78}, {"message": "Expected 0 spaces after ELSE keyword; 1 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 42, "column": 7}, {"message": "Line indented incorrectly; expected at least 4 spaces, found 0", "source": "Generic.WhiteSpace.ScopeIndent.Incorrect", "severity": 6, "fixable": true, "type": "WARNING", "line": 43, "column": 1}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 43, "column": 5}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/view/frontend/templates/cart/message.phtml": {"errors": 0, "warnings": 1, "messages": [{"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundUnescaped", "severity": 9, "fixable": false, "type": "WARNING", "line": 1, "column": 43}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/view/frontend/templates/sales/email/order/voucher.phtml": {"errors": 0, "warnings": 14, "messages": [{"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 8, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 8, "column": 53}, {"message": "Line indented incorrectly; expected at least 12 spaces, found 8", "source": "Generic.WhiteSpace.ScopeIndent.Incorrect", "severity": 6, "fixable": true, "type": "WARNING", "line": 10, "column": 9}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 10, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 10, "column": 45}, {"message": "Line indented incorrectly; expected at least 12 spaces, found 8", "source": "Generic.WhiteSpace.ScopeIndent.Incorrect", "severity": 6, "fixable": true, "type": "WARNING", "line": 12, "column": 9}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 12, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 12, "column": 45}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 15, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 15, "column": 53}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 15, "column": 85}, {"message": "Line exceeds 120 characters; contains 135 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 134}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 16, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 16, "column": 54}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/view/frontend/templates/sales/order/voucher.phtml": {"errors": 0, "warnings": 14, "messages": [{"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 8, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 8, "column": 53}, {"message": "Line indented incorrectly; expected at least 12 spaces, found 8", "source": "Generic.WhiteSpace.ScopeIndent.Incorrect", "severity": 6, "fixable": true, "type": "WARNING", "line": 10, "column": 9}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 10, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 10, "column": 45}, {"message": "Line indented incorrectly; expected at least 12 spaces, found 8", "source": "Generic.WhiteSpace.ScopeIndent.Incorrect", "severity": 6, "fixable": true, "type": "WARNING", "line": 12, "column": 9}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 12, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 12, "column": 45}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 15, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 15, "column": 53}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 15, "column": 85}, {"message": "Line exceeds 120 characters; contains 135 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 134}, {"message": "Short echo tag syntax must be used; expected \"<?=\" but found \"<?php echo\"", "source": "Magento2.PHP.ShortEchoSyntax.ShortEchoTag", "severity": 8, "fixable": false, "type": "WARNING", "line": 16, "column": 9}, {"message": "Unescaped output detected.", "source": "Magento2.Security.XssTemplate.FoundNotAllowed", "severity": 9, "fixable": false, "type": "WARNING", "line": 16, "column": 54}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Ui/Component/Listing/Column/Actions.php": {"errors": 0, "warnings": 4, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 15}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 12}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 52, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Setup/Patch/Data/AddIncertProductIdProductAttribute.php": {"errors": 0, "warnings": 7, "messages": [{"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 44, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 9}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 84, "column": 12}, {"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 8}, {"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 103, "column": 8}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 111, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Setup/Patch/Data/AddNoApplyIncertVoucherProductAttribute.php": {"errors": 0, "warnings": 7, "messages": [{"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 44, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 9}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 13}, {"message": "Use ::class notation instead.", "source": "Magento2.PHP.LiteralNamespaces.LiteralClassUsage", "severity": 7, "fixable": false, "type": "WARNING", "line": 58, "column": 29}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 84, "column": 12}, {"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 8}, {"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 103, "column": 8}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Setup/Patch/Data/AddIncertProductInfoProductAttribute.php": {"errors": 0, "warnings": 7, "messages": [{"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 44, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 9}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 84, "column": 12}, {"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 8}, {"message": "If the @inheritdoc not inline it shouldn’t have braces", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 103, "column": 8}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 111, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Plugin/NewProduct.php": {"errors": 0, "warnings": 12, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 29}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 8}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 27, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 27, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 27, "column": 35}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 28, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 28, "column": 13}, {"message": "Expected 0 spaces after opening bracket; 1 found", "source": "PSR2.ControlStructures.ControlStructureSpacing.SpacingAfterOpenBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 28, "column": 16}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 28, "column": 79}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 34, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Plugin/Sales/OrderPlugin.php": {"errors": 0, "warnings": 5, "messages": [{"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 9, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 8}, {"message": "$subject parameter is missing in method arguments signature", "source": "Magento2.Annotation.MethodArguments.ArgumentMissing", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 8}, {"message": "$order parameter is missing in method annotation", "source": "Magento2.Annotation.MethodArguments.ArgumentMissing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 12}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 22, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Plugin/Sales/CreditmemoValidator.php": {"errors": 0, "warnings": 24, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 31}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 25, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 26, "column": 8}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 46, "column": 67}, {"message": "Multi-line function call not indented correctly; expected 16 spaces but found 20", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 47, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 47, "column": 49}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 63, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 64, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 64, "column": 8}, {"message": "There must not be a newline before the closing parenthesis of a single-line function declaration", "source": "Squiz.Functions.MultiLineFunctionDeclaration.CloseBracketNewLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 68, "column": 5}, {"message": "Opening brace should be on a new line", "source": "Squiz.Functions.MultiLineFunctionDeclaration.BraceOnSameLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 68, "column": 64}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 75, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 76, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 94, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 8}, {"message": "$currentVoucherProduct parameter is missing in method arguments signature", "source": "Magento2.Annotation.MethodArguments.ArgumentMissing", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 8}, {"message": "$incertOrderId parameter is missing in method annotation", "source": "Magento2.Annotation.MethodArguments.ArgumentMissing", "severity": 5, "fixable": false, "type": "WARNING", "line": 98, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 103, "column": 9}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 103, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 108, "column": 13}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 121, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Plugin/Sales/Order/OrderItem.php": {"errors": 0, "warnings": 3, "messages": [{"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 9, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 22, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Plugin/Sales/Order/CreditmemoItem.php": {"errors": 0, "warnings": 20, "messages": [{"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 9, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 9, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 8}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 16, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 16, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 16, "column": 124}, {"message": "Line exceeds 120 characters; contains 125 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 16, "column": 125}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 18, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 18, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 18, "column": 88}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 25, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 27, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 27, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 27, "column": 113}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 29, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 29, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 29, "column": 111}, {"message": "Line exceeds 120 characters; contains 185 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 31, "column": 185}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 37, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Plugin/Checkout/CouponPost.php": {"errors": 0, "warnings": 24, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 25}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 20}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 29}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 18, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 30}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 21}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 38, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 38, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 39, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 39, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 58, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 62, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 62, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 62, "column": 108}, {"message": "Line exceeds 120 characters; contains 147 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 66, "column": 147}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 72, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 73, "column": 8}, {"message": "Line exceeds 120 characters; contains 132 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 79, "column": 132}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 81, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 81, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 81, "column": 41}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 84, "column": 98}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 108, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Paypal/Collect.php": {"errors": 0, "warnings": 2, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 8}, {"message": "There must be exactly one blank line between lines short and long descriptions", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 33}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Quote/Totals/Before.php": {"errors": 0, "warnings": 13, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 20}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 25}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 12}, {"message": "Expected 0 spaces after opening parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 17, "column": 32}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 36, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 37, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 37, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 54, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 55, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 55, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 66, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Quote/Totals/After.php": {"errors": 0, "warnings": 9, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 20}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 25}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 12}, {"message": "Expected 0 spaces after opening parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 17, "column": 32}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 8}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 31, "column": 103}, {"message": "Line exceeds 120 characters; contains 146 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 31, "column": 146}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 34, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/SubmitAfter.php": {"errors": 0, "warnings": 8, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 19}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 24}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 12}, {"message": "Expected 0 spaces after opening parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 16, "column": 32}, {"message": "Expected 0 spaces before closing parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingBeforeClose", "severity": 6, "fixable": true, "type": "WARNING", "line": 16, "column": 71}, {"message": "Opening brace should be on a new line", "source": "Squiz.Functions.MultiLineFunctionDeclaration.BraceOnSameLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 16, "column": 73}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 8}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 41, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/CancelAfter.php": {"errors": 0, "warnings": 1, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 8}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/PlaceAfter.php": {"errors": 0, "warnings": 4, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 29, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 48, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 48, "column": 8}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 63, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/Invoice/Save.php": {"errors": 0, "warnings": 3, "messages": [{"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 12, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 8}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 30, "column": 48}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/Invoice/Register.php": {"errors": 0, "warnings": 3, "messages": [{"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 10, "column": 1}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 8}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 25, "column": 17}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/Invoice/Pay.php": {"errors": 0, "warnings": 3, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 8}, {"message": "There must be exactly one blank line between lines short and long descriptions", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 33}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 35, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/Invoice/SaveAfter.php": {"errors": 0, "warnings": 4, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 22}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 12}, {"message": "Expected 0 spaces after opening parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 15, "column": 32}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 8}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Order/Payment/Refund.php": {"errors": 0, "warnings": 8, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 12}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 12, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 12}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 39, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 54, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 54, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 71, "column": 12}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Observer/Sales/Service/Quote/SubmitBefore.php": {"errors": 0, "warnings": 13, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 24}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 20}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 12}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 8}, {"message": "There must be exactly one blank line between lines short and long descriptions", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 33}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 47, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 48, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 48, "column": 8}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 60, "column": 139}, {"message": "Line exceeds 120 characters; contains 174 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 60, "column": 174}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 62, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 62, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 62, "column": 80}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Voucher.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 1}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 17, "column": 5}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Purchase.php": {"errors": 0, "warnings": 3, "messages": [{"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 16, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 17, "column": 5}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 138, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/PurchaseRepository.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 48, "column": 1}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 148, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/VoucherRepository.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 48, "column": 1}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 148, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/ResourceModel/Voucher.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 22, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/ResourceModel/Purchase.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 22, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/ResourceModel/Purchase/Collection.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Class properties must have type declaration using @var tag.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.MissingVar", "severity": 5, "fixable": false, "type": "WARNING", "line": 18, "column": 15}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 30, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/ResourceModel/Voucher/Collection.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Class properties must have type declaration using @var tag.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.MissingVar", "severity": 5, "fixable": false, "type": "WARNING", "line": 18, "column": 15}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 30, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Sales/Quote/Totals/Voucher.php": {"errors": 0, "warnings": 24, "messages": [{"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 18, "column": 5}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 15}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 20}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 25}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 25, "column": 24}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 26, "column": 21}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 12}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 42, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 42, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 79, "column": 13}, {"message": "Function's nesting level (7) exceeds 5; consider refactoring the function", "source": "Generic.Metrics.NestingLevel.TooHigh", "severity": 7, "fixable": false, "type": "WARNING", "line": 79, "column": 13}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 102, "column": 37}, {"message": "Multi-line function call not indented correctly; expected 28 spaces but found 32", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 103, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 103, "column": 93}, {"message": "Expected 1 space after closing brace; newline found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 111, "column": 21}, {"message": "Expected \"} else {\\n\"; found \"}\\n                    else {\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 112, "column": 21}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 124, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 124, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 124, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 124, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 144, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 149, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 160, "column": 13}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 165, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Sales/Order/Creditmemo/Totals/Voucher.php": {"errors": 0, "warnings": 4, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 12, "column": 24}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 39, "column": 13}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Sales/Order/Invoice/Totals/Voucher.php": {"errors": 0, "warnings": 6, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 21}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 12}, {"message": "Opening brace should be on a new line", "source": "Squiz.Functions.MultiLineFunctionDeclaration.BraceOnSameLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 15, "column": 57}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 33, "column": 13}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 38, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Voucher/Management.php": {"errors": 0, "warnings": 47, "messages": [{"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 18, "column": 1}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 27}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 34}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 28}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 31}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 22}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 25, "column": 20}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 43, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 48, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 62, "column": 12}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 64, "column": 35}, {"message": "Only one argument is allowed per line in a multi-line function call", "source": "PSR2.Methods.FunctionCallSignature.MultipleArguments", "severity": 6, "fixable": true, "type": "WARNING", "line": 64, "column": 70}, {"message": "Multi-line function call not indented correctly; expected 8 spaces but found 12", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 65, "column": 1}, {"message": "Only one argument is allowed per line in a multi-line function call", "source": "PSR2.Methods.FunctionCallSignature.MultipleArguments", "severity": 6, "fixable": true, "type": "WARNING", "line": 65, "column": 38}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 65, "column": 46}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 76, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 88, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 96, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 96, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 97, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 97, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 97, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 105, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 110, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 110, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 110, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 110, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 110, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 110, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 112, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 122, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 142, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 152, "column": 12}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 157, "column": 28}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 161, "column": 12}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 166, "column": 28}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 170, "column": 12}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 182, "column": 1}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 182, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Voucher/Invoice/Converter.php": {"errors": 0, "warnings": 46, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 34}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 18, "column": 19}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 12}, {"message": "Expected 1 space after FUNCTION keyword; 0 found", "source": "Squiz.Functions.MultiLineFunctionDeclaration.SpaceAfterFunction", "severity": 6, "fixable": true, "type": "WARNING", "line": 39, "column": 49}, {"message": "Opening brace must be the last content on the line", "source": "Squiz.Functions.MultiLineFunctionDeclaration.ContentAfterBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 39, "column": 65}, {"message": "Closing brace must be on a line by itself", "source": "Squiz.WhiteSpace.ScopeClosingBrace.ContentBefore", "severity": 6, "fixable": true, "type": "WARNING", "line": 39, "column": 96}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 52, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 52, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 52, "column": 18}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 58, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 61, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 61, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 61, "column": 18}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 67, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 68, "column": 8}, {"message": "Opening brace should be on a new line", "source": "Squiz.Functions.MultiLineFunctionDeclaration.BraceOnSameLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 71, "column": 50}, {"message": "Expected 1 space after closing brace; newline found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 75, "column": 9}, {"message": "Expected \"} else {\\n\"; found \"}\\n        else {\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 76, "column": 9}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 13}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 13}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 13}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 18}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 18}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 18}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 83, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 88, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 93, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 9}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 9}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 9}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 13}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 13}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 113, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 124, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 139, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 141, "column": 9}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 141, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 163, "column": 13}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 165, "column": 74}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 165, "column": 102}, {"message": "Line exceeds 120 characters; contains 126 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 165, "column": 126}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 168, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Product/ImageDownloader.php": {"errors": 0, "warnings": 9, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 11, "column": 15}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 12, "column": 15}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 15}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 25, "column": 12}, {"message": "Line exceeds 120 characters; contains 199 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 29, "column": 199}, {"message": "The use of function stream_context_create() is discouraged", "source": "Magento2.Functions.DiscouragedFunction.Discouraged", "severity": 8, "fixable": false, "type": "WARNING", "line": 37, "column": 24}, {"message": "The use of function file_get_contents() is discouraged", "source": "Magento2.Functions.DiscouragedFunction.Discouraged", "severity": 8, "fixable": false, "type": "WARNING", "line": 42, "column": 29}, {"message": "Empty CATCH statement detected", "source": "Magento2.CodeAnalysis.EmptyBlock.DetectedCatch", "severity": 7, "fixable": false, "type": "WARNING", "line": 50, "column": 11}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Model/Product/Type/Voucher.php": {"errors": 0, "warnings": 9, "messages": [{"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 5}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 13}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 32, "column": 54}, {"message": "Multi-line function call not indented correctly; expected 24 spaces but found 28", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 33, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 33, "column": 60}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 38, "column": 54}, {"message": "Multi-line function call not indented correctly; expected 24 spaces but found 28", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 39, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 39, "column": 60}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Logger/Handler.php": {"errors": 0, "warnings": 4, "messages": [{"message": "Expected 1 space after class name; 2 found", "source": "PSR2.Classes.ClassDeclaration.SpaceAfterName", "severity": 6, "fixable": true, "type": "WARNING", "line": 7, "column": 7}, {"message": "Expected 1 space before extends keyword; 2 found", "source": "PSR2.Classes.ClassDeclaration.SpaceBeforeExtends", "severity": 6, "fixable": true, "type": "WARNING", "line": 7, "column": 16}, {"message": "Short description duplicates class property name.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.AlreadyHaveMeaningfulNameVar", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 20, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Logger/Logger.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Voucher.php": {"errors": 0, "warnings": 85, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 18, "column": 20}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 19}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 28}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 20}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 12}, {"message": "Expected 0 spaces after opening parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 23, "column": 32}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 30, "column": 12}, {"message": "Expected 0 spaces after opening parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 30, "column": 51}, {"message": "Expected 0 spaces before closing parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingBeforeClose", "severity": 6, "fixable": true, "type": "WARNING", "line": 30, "column": 80}, {"message": "Line exceeds 120 characters; contains 142 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 37, "column": 142}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 43, "column": 13}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 46, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 46, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 46, "column": 19}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 55, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 55, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 55, "column": 87}, {"message": "Expected 1 space after closing brace; newline found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 58, "column": 13}, {"message": "Expected \"} else {\\n\"; found \"}\\n            else {\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 59, "column": 13}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 60, "column": 32}, {"message": "Line exceeds 120 characters; contains 139 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 60, "column": 139}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 67, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 67, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 67, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 67, "column": 8}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 71, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 71, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 71, "column": 41}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 80, "column": 13}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 82, "column": 20}, {"message": "Expected 0 spaces before closing parenthesis; 1 found", "source": "PSR2.Methods.FunctionCallSignature.SpaceBeforeCloseBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 82, "column": 61}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 85, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 90, "column": 13}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 96, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 96, "column": 8}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 102, "column": 24}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 113, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 114, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 115, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 116, "column": 8}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 121, "column": 24}, {"message": "Expected 1 space after comma in argument list; 2 found", "source": "Generic.Functions.FunctionCallArgumentSpacing.TooMuchSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 121, "column": 122}, {"message": "Line exceeds 120 characters; contains 135 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 121, "column": 135}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 133, "column": 12}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 135, "column": 24}, {"message": "Line exceeds 120 characters; contains 129 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 135, "column": 129}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 146, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 157, "column": 12}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 160, "column": 28}, {"message": "Line exceeds 120 characters; contains 132 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 160, "column": 132}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 169, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 174, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 188, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 188, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 188, "column": 84}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 195, "column": 12}, {"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 201, "column": 1}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 203, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 210, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 218, "column": 17}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 218, "column": 17}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 218, "column": 81}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 219, "column": 36}, {"message": "Line exceeds 120 characters; contains 241 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 219, "column": 241}, {"message": "Line exceeds 120 characters; contains 124 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 222, "column": 124}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 224, "column": 21}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 224, "column": 21}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 224, "column": 92}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 231, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 231, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 231, "column": 25}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 238, "column": 15}, {"message": "Expected \"if (...) {\\n\"; found \"if(...) {\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 240, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 240, "column": 9}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 241, "column": 28}, {"message": "Line exceeds 120 characters; contains 137 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 241, "column": 137}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 245, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 246, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 253, "column": 1}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 253, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Creditmemo.php": {"errors": 0, "warnings": 8, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 8, "column": 19}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 12}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 24, "column": 26}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 29, "column": 15}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 34, "column": 12}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 39, "column": 1}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 39, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Cache.php": {"errors": 0, "warnings": 37, "messages": [{"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 11, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 12, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 14, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 16, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 17, "column": 5}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 28}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 33}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 12}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 30, "column": 39}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 33, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 38, "column": 12}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 23}, {"message": "Multi-line function call not indented correctly; expected 8 spaces but found 12", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 41, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 41, "column": 62}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 44, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 52, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 57, "column": 12}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 59, "column": 23}, {"message": "Multi-line function call not indented correctly; expected 8 spaces but found 12", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 60, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 60, "column": 49}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 63, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 71, "column": 12}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 73, "column": 23}, {"message": "Multi-line function call not indented correctly; expected 8 spaces but found 12", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 74, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 74, "column": 63}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 78, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 78, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 78, "column": 8}, {"message": "Missing @param for argument in method annotation", "source": "Magento2.Annotation.MethodArguments.ParamMissing", "severity": 5, "fixable": false, "type": "WARNING", "line": 80, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 90, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 13}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 104, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Invoice.php": {"errors": 0, "warnings": 22, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 11, "column": 21}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 18, "column": 12}, {"message": "Line exceeds 120 characters; contains 156 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 21, "column": 156}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 22, "column": 23}, {"message": "Expected 1 space after closing brace; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 24, "column": 9}, {"message": "Expected \"} else {\\n\"; found \"}else {\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 24, "column": 10}, {"message": "Expected 0 spaces before closing parenthesis; 1 found", "source": "PSR2.Methods.FunctionCallSignature.SpaceBeforeCloseBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 26, "column": 78}, {"message": "Space after opening parenthesis of function call prohibited", "source": "PSR2.Methods.FunctionCallSignature.SpaceAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 29, "column": 27}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 36, "column": 15}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 40, "column": 13}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 13}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 132}, {"message": "Line exceeds 120 characters; contains 133 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 40, "column": 133}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 47, "column": 15}, {"message": "Opening brace should be on a new line", "source": "Squiz.Functions.MultiLineFunctionDeclaration.BraceOnSameLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 47, "column": 48}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 58, "column": 21}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 58, "column": 21}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 58, "column": 80}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 67, "column": 15}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 72, "column": 1}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 72, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Purchase.php": {"errors": 0, "warnings": 30, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 27}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 26}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 33}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 18, "column": 5}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 30}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 12}, {"message": "Line exceeds 120 characters; contains 164 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 22, "column": 164}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 31, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 34, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 34, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 34, "column": 58}, {"message": "Line exceeds 120 characters; contains 134 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 35, "column": 134}, {"message": "Expected \"foreach (...) {\\n\"; found \"foreach(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 38, "column": 21}, {"message": "Expected 1 space after FOREACH keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 38, "column": 21}, {"message": "Space found after opening bracket of FOREACH loop", "source": "Squiz.ControlStructures.ForEachLoopDeclaration.SpaceAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 38, "column": 21}, {"message": "Expected 0 spaces after opening bracket; 1 found", "source": "PSR2.ControlStructures.ControlStructureSpacing.SpacingAfterOpenBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 38, "column": 29}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 38, "column": 74}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 40, "column": 25}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 25}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 169}, {"message": "Line exceeds 120 characters; contains 170 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 40, "column": 170}, {"message": "Line exceeds 120 characters; contains 174 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 41, "column": 174}, {"message": "Line exceeds 120 characters; contains 122 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 42, "column": 122}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 51, "column": 12}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 98}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 94, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 95, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 104, "column": 1}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 104, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/CouponCodes.php": {"errors": 0, "warnings": 25, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 11, "column": 31}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 12, "column": 18}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 19}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 19}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 19}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 16, "column": 19}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 33, "column": 12}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 94}, {"message": "Expected 1 space after closing brace; newline found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 41, "column": 13}, {"message": "Expected \"} else {\\n\"; found \"}\\n            else {\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 42, "column": 13}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 44, "column": 94}, {"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 49, "column": 1}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 51, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 56, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 61, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 12}, {"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 70, "column": 1}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 72, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 12}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 82, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 83, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 90, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Product.php": {"errors": 0, "warnings": 19, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 10, "column": 27}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 12, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 12}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 24, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 24, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 24, "column": 57}, {"message": "Opening parenthesis of a multi-line function call must be the last content on the line", "source": "PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket", "severity": 6, "fixable": true, "type": "WARNING", "line": 25, "column": 44}, {"message": "Only one argument is allowed per line in a multi-line function call", "source": "PSR2.Methods.FunctionCallSignature.MultipleArguments", "severity": 6, "fixable": true, "type": "WARNING", "line": 25, "column": 84}, {"message": "Multi-line function call not indented correctly; expected 12 spaces but found 16", "source": "PSR2.Methods.FunctionCallSignature.Indent", "severity": 6, "fixable": true, "type": "WARNING", "line": 26, "column": 1}, {"message": "Closing parenthesis of a multi-line function call must be on a line by itself", "source": "PSR2.Methods.FunctionCallSignature.CloseBracketLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 26, "column": 39}, {"message": "Expected 1 space after closing brace; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace", "severity": 6, "fixable": true, "type": "WARNING", "line": 27, "column": 9}, {"message": "Expected \"} else {\\n\"; found \"}else {\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 27, "column": 10}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 35, "column": 68}, {"message": "Long description must start with a capital letter", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 37, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 41, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 42, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 50, "column": 1}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 50, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Config.php": {"errors": 0, "warnings": 33, "messages": [{"message": "There must be one blank line after the namespace declaration", "source": "PSR2.Namespaces.NamespaceDeclaration.BlankLineAfter", "severity": 6, "fixable": true, "type": "WARNING", "line": 3, "column": 1}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 12, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 13, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 14, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 16, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 17, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 18, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 19, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 20, "column": 5}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 22, "column": 34}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 24, "column": 12}, {"message": "Expected 0 spaces after opening parenthesis; 1 found", "source": "Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterOpen", "severity": 6, "fixable": true, "type": "WARNING", "line": 24, "column": 32}, {"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 28, "column": 1}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 30, "column": 12}, {"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 34, "column": 1}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 36, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 41, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 46, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 51, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 56, "column": 12}, {"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 60, "column": 1}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 62, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 67, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 72, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 77, "column": 12}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 82, "column": 12}, {"message": "Opening brace should be on a new line", "source": "Squiz.Functions.MultiLineFunctionDeclaration.BraceOnSameLine", "severity": 6, "fixable": true, "type": "WARNING", "line": 82, "column": 54}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 83, "column": 74}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 87, "column": 12}, {"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 91, "column": 1}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 93, "column": 12}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 97, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Helper/Order.php": {"errors": 0, "warnings": 15, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 12, "column": 24}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 14, "column": 12}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 8}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 8}, {"message": "Line indented incorrectly; expected at least 8 spaces, found 7", "source": "Generic.WhiteSpace.ScopeIndent.Incorrect", "severity": 6, "fixable": true, "type": "WARNING", "line": 26, "column": 8}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 26, "column": 42}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 29, "column": 12}, {"message": "Line exceeds 120 characters; contains 129 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 38, "column": 84}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 41, "column": 57}, {"message": "Line exceeds 120 characters; contains 125 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 52, "column": 89}, {"message": "There must be exactly one blank line between lines short and long descriptions", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 64, "column": 55}, {"message": "Type is not specified", "source": "Magento2.Annotation.MethodArguments.NoTypeSpecified", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 79, "column": 1}, {"message": "The closing brace for the class must go on the next line after the body", "source": "PSR2.Classes.ClassDeclaration.CloseBraceAfterBody", "severity": 6, "fixable": true, "type": "WARNING", "line": 79, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/CancelRedemption.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/ConfigException.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/VoucherExceptionInterface.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/Redeem.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/Recharge.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/InvalidVoucher.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/VoucherAmountChanged.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Exception/Payment.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 8, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Controller/Adminhtml/Purchase/Download.php": {"errors": 0, "warnings": 8, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 18, "column": 25}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 19, "column": 29}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 20, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 24}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodArguments", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 12}, {"message": "Code must not contain multiple empty lines in a row; found 3 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 36, "column": 1}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 41, "column": 8}, {"message": "Expected 1 newline at end of file; 0 found", "source": "PSR2.Files.EndFileNewline.NoneFound", "severity": 6, "fixable": true, "type": "WARNING", "line": 69, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Controller/Adminhtml/Purchase/Index.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 15}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Controller/Adminhtml/Voucher/Index.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 15}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Controller/Adminhtml/Product/Create.php": {"errors": 0, "warnings": 2, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 13, "column": 15}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 40, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Controller/Adminhtml/Product/Post.php": {"errors": 0, "warnings": 36, "messages": [{"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 25, "column": 28}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 26, "column": 29}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 28, "column": 28}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 29, "column": 21}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 30, "column": 35}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 31, "column": 33}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 32, "column": 34}, {"message": "Missing <PERSON><PERSON> for class property.", "source": "Magento2.Commenting.ClassPropertyPHPDocFormatting.Missing", "severity": 5, "fixable": false, "type": "WARNING", "line": 33, "column": 29}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 35, "column": 12}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 63, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 63, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 63, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 63, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 81, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 87, "column": 13}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 98, "column": 13}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 118, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 119, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 158, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 158, "column": 5}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 158, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 159, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 159, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 159, "column": 8}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 188, "column": 13}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 190, "column": 48}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 194, "column": 60}, {"message": "Expected \"if (...) {\\n\"; found \"if(...){\\n\"", "source": "PEAR.ControlStructures.ControlSignature.Found", "severity": 6, "fixable": false, "type": "WARNING", "line": 197, "column": 9}, {"message": "Expected 1 space after IF keyword; 0 found", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterKeyword", "severity": 6, "fixable": true, "type": "WARNING", "line": 197, "column": 9}, {"message": "Expected 1 space after closing parenthesis; found 0", "source": "Squiz.ControlStructures.ControlSignature.SpaceAfterCloseParenthesis", "severity": 6, "fixable": true, "type": "WARNING", "line": 197, "column": 59}, {"message": "No space found after comma in argument list", "source": "Generic.Functions.FunctionCallArgumentSpacing.NoSpaceAfterComma", "severity": 6, "fixable": true, "type": "WARNING", "line": 200, "column": 60}, {"message": "Comment block is missing", "source": "Magento2.Annotation.MethodArguments.NoCommentBlock", "severity": 5, "fixable": false, "type": "WARNING", "line": 205, "column": 13}, {"message": "The use of function basename() is discouraged", "source": "Magento2.Functions.DiscouragedFunction.Discouraged", "severity": 8, "fixable": false, "type": "WARNING", "line": 207, "column": 21}, {"message": "The use of function pathinfo() is discouraged; use Magento\\Framework\\Filesystem\\Io\\File::getPathInfo() instead", "source": "Magento2.Functions.DiscouragedFunction.DiscouragedWithAlternative", "severity": 8, "fixable": false, "type": "WARNING", "line": 208, "column": 26}, {"message": "Expected 1 blank line at end of file; 5 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 211, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Block/Adminhtml/Sales/Order/Totals.php": {"errors": 0, "warnings": 1, "messages": [{"message": "Code must not contain multiple empty lines in a row; found 2 empty lines.", "source": "Magento2.Whitespace.MultipleEmptyLines.MultipleEmptyLines", "severity": 6, "fixable": false, "type": "WARNING", "line": 56, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Block/Sales/Order/Voucher.php": {"errors": 0, "warnings": 5, "messages": [{"message": "Line exceeds 120 characters; contains 122 characters", "source": "Generic.Files.LineLength.TooLong", "severity": 6, "fixable": false, "type": "WARNING", "line": 51, "column": 122}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 57, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 58, "column": 8}, {"message": "Missing short description", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 65, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 8}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Api/VoucherRepositoryInterface.php": {"errors": 0, "warnings": 5, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 35, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 45, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 55, "column": 8}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Api/PurchaseRepositoryInterface.php": {"errors": 0, "warnings": 6, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 17, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 35, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 45, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 55, "column": 8}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 61, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Api/Data/VoucherInterface.php": {"errors": 0, "warnings": 44, "messages": [{"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 13, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 14, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 16, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 17, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 18, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 19, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 20, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 21, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 22, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 23, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 27, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 33, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 40, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 40, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 46, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 53, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 53, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 59, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 66, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 72, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 79, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 79, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 85, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 92, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 92, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 98, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 105, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 105, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 111, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 118, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 118, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 124, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 131, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 131, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 137, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 144, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 144, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 150, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 157, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 157, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 163, "column": 8}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Api/Data/VoucherSearchResultsInterface.php": {"errors": 0, "warnings": 3, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 8}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 25, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Api/Data/PurchaseSearchResultsInterface.php": {"errors": 0, "warnings": 3, "messages": [{"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 15, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 21, "column": 8}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 25, "column": 1}]}, "/usr/share/eqp/PhpCodeSnifferTool/tmp/work/Api/Data/PurchaseInterface.php": {"errors": 0, "warnings": 29, "messages": [{"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 13, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 14, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 15, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 16, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 17, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 18, "column": 5}, {"message": "Visibility must be declared on all constants if your project supports PHP 7.1 or later", "source": "PSR12.Properties.ConstantVisibility.NotFound", "severity": 6, "fixable": false, "type": "WARNING", "line": 19, "column": 5}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 23, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 29, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 36, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 36, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 42, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 49, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 55, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 62, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 62, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 68, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 75, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 75, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 81, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 88, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 88, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 94, "column": 8}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 101, "column": 8}, {"message": "Short description should not be in multi lines", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 101, "column": 27}, {"message": "There must be exactly one blank line before tags", "source": "Magento2.Annotation.MethodAnnotationStructure.MethodAnnotation", "severity": 5, "fixable": false, "type": "WARNING", "line": 107, "column": 8}, {"message": "Expected 1 blank line at end of file; 2 found", "source": "PSR2.Files.EndFileNewline.TooMany", "severity": 6, "fixable": true, "type": "WARNING", "line": 111, "column": 1}]}}, "standard": {"name": "Magento2", "package": "magento/magento-coding-standard", "version": "31"}}}]}
# Load DSL and set up stages
require 'capistrano/setup'

# Include default deployment tasks
require 'capistrano/deploy'

require 'capistrano/setup'

# Load Magento deployment tasks
require 'capistrano/magento2/deploy'
require 'capistrano/magento2/pending'

# Load custom tasks from `lib/capistrano/tasks` if you have any defined
Dir.glob('config/capistrano/tasks/*.rake').each { |r| import r }

DOCKERSETUP=false

if DOCKERSETUP
    set :deploy_config_path, File.expand_path('config/deploy_docker.rb')
    Dir.glob('config/capistrano/tasks/docker/*.rake').each { |r| import r }
end
"VAT number","UID-Nummer"
"VAT number is valid","Die UID-Nummer ist gültig"
"VAT request success","Umsatzsteueranforderung erfolgreich"
"VAT validation time","MwSt.-Validierungszeit"
"Yes","Ja"
"No","Nein"
"VAT number validation result","Ergebnis der Validierung der UID-Nummer"
"API connection error","API-Verbindungsfehler"
"VAT ID invalid","UID-Nummer ist ungültig"
"VAT ID valid","UID-Nummer ist gültig"
"VAT ID qualified","UID-Nummer ist qualifiziert"
"VAT ID Validation","UID-Nummer Validierung"
"Please enter VAT ID","Bitte UID-Nummer eingeben"
"Check","Überprüfen"
"Close","Schließen"
"Validation result","Validierungsergebnis"
"An error occured, please try again later","Ein Fehler ist aufgetreten. Bitte versuchen Sie es später noch einmal."
"VAT ID is required","UID-Nummer ist erforderlich"
"Validation status","Validierungsstatus"
"API type","API-Typ"
"Request time","Anfragezeit"
"Company info","Firmeninfo"
"Vat Validation","Mehrwertsteuerprüfung"
"Configuration","Konfiguration"
"Vat Validation Settings","Einstellungen zur Mehrwertsteuerprüfung"
"API Type","API-Typ"
"Endereco Configuration","Endereco-Konfiguration"
"Endereco Settings","Endereco-Einstellungen"
"API URL","API-URL"
"Endpoint URL of the Endereco-API","Endpunkt-URL der Endereco-API"
"API Key","API-Schlüssel"
"Authorization key retrieved from Endereco","Von Endereco abgerufener Autorisierungsschlüssel"
Valid,Valide
Invalid,Invalide
Save order comment for VAT validation,Kommentar für Validierung auf der Bestellung speichern
Cache Result Time,Gültige Validierung zwischenspeichern
How long is a validation result valid? (Time in seconds), Wie lange ist ein Validierungsergebnis gültig? (Zeit in Sekunden)
Qualified Validation,Qualifizierte Prüfung
"Strict validation of uid against company details. VAT is only valid if the company details exactly match.","Strenge Validierung der UID anhand der Unternehmensangaben. Die UID-Nummer ist nur gültig, wenn die Unternehmensangaben genau übereinstimmen."
Company Name invalid,Firmenname stimmt nicht überein
Street invalid,Straße stimmt nicht überein
City invalid, Ort / Stadt stimmt nicht überein
Postcode invalid, Postleitzahl stimmt nicht überein
"Number in % to match input to. 0% means no qualification. 100% means completely identical to validation result.","Prozentzahl, um die Eingabe abzugleichen. 0% bedeutet keine Übereinstimmung. 100% bedeutet vollständig identisch mit dem Validierungsergebnis."
Certification details,Zertifikat
Copy to default billing address,In Rechnungsadresse kopieren
Copy to default shipping address,In Versandadresse kopieren
Module Version,Modulversion
Enable Module,Modul aktivieren
"Is Valid","Ist gültig"
"VAT number validation results","Ergebnisse der UID-Nr.-Prüfung"
Validate again,Erneut prüfen
"Delete validation data for VAT number %1","Lösche Validierungsdaten für die UID %1"
"Are you sure you want to delete the validation data?","Sind Sie sicher, dass Sie die Validierungsdaten löschen möchten?"
Validation result for VAT number %1 was deleted.,Validierungsdaten für %1 wurden gelöscht.
VAT number %1 was revalidated and is %2.,UID-Nummer %1 wurde erneut validiert und ist %2.
Validation Date,Prüfdatum
Validated VAT Numbers,Validierte UID-Nummern
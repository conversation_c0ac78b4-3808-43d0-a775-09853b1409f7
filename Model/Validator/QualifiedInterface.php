<?php

namespace CopeX\VatValidator\Model\Validator;

interface QualifiedInterface
{
    public function checkCompanyValid(RequestInterface $request, ResultInterface $result);
    public function checkStreetValid(RequestInterface $request, ResultInterface $result);
    public function checkCityValid(RequestInterface $request, ResultInterface $result);
    public function checkPostcodeValid(RequestInterface $request, ResultInterface $result);
}
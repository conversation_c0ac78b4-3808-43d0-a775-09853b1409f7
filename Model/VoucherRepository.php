<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Model;

use Incert\Voucher\Api\Data\VoucherInterface;
use Incert\Voucher\Api\Data\VoucherInterfaceFactory;
use Incert\Voucher\Api\Data\VoucherSearchResultsInterfaceFactory;
use Incert\Voucher\Api\VoucherRepositoryInterface;
use Incert\Voucher\Model\ResourceModel\Voucher as ResourceVoucher;
use Incert\Voucher\Model\ResourceModel\Voucher\CollectionFactory as VoucherCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class VoucherRepository implements VoucherRepositoryInterface
{

    /**
     * @var VoucherCollectionFactory
     */
    protected $voucherCollectionFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var VoucherInterfaceFactory
     */
    protected $voucherFactory;

    /**
     * @var ResourceVoucher
     */
    protected $resource;

    /**
     * @var Voucher
     */
    protected $searchResultsFactory;


    /**
     * @param ResourceVoucher $resource
     * @param VoucherInterfaceFactory $voucherFactory
     * @param VoucherCollectionFactory $voucherCollectionFactory
     * @param VoucherSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceVoucher $resource,
        VoucherInterfaceFactory $voucherFactory,
        VoucherCollectionFactory $voucherCollectionFactory,
        VoucherSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->voucherFactory = $voucherFactory;
        $this->voucherCollectionFactory = $voucherCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(VoucherInterface $voucher)
    {
        try {
            $this->resource->save($voucher);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the voucher: %1',
                $exception->getMessage()
            ));
        }
        return $voucher;
    }

    /**
     * @inheritDoc
     */
    public function get($voucherId)
    {
        $voucher = $this->voucherFactory->create();
        $this->resource->load($voucher, $voucherId);
        if (!$voucher->getId()) {
            throw new NoSuchEntityException(__('Voucher with id "%1" does not exist.', $voucherId));
        }
        return $voucher;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->voucherCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(VoucherInterface $voucher)
    {
        try {
            $voucherModel = $this->voucherFactory->create();
            $this->resource->load($voucherModel, $voucher->getVoucherId());
            $this->resource->delete($voucherModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Voucher: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($voucherId)
    {
        return $this->delete($this->get($voucherId));
    }
}


<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Model;

use Incert\Voucher\Api\Data\PurchaseInterface;
use Incert\Voucher\Api\Data\PurchaseInterfaceFactory;
use Incert\Voucher\Api\Data\PurchaseSearchResultsInterfaceFactory;
use Incert\Voucher\Api\PurchaseRepositoryInterface;
use Incert\Voucher\Model\ResourceModel\Purchase as ResourcePurchase;
use Incert\Voucher\Model\ResourceModel\Purchase\CollectionFactory as PurchaseCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class PurchaseRepository implements PurchaseRepositoryInterface
{

    /**
     * @var ResourcePurchase
     */
    protected $resource;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var PurchaseSearchResultsInterfaceFactory
     */
    protected $searchResultsFactory;

    /**
     * @var PurchaseInterfaceFactory
     */
    protected $purchaseFactory;

    /**
     * @var PurchaseCollectionFactory
     */
    protected $purchaseCollectionFactory;


    /**
     * @param ResourcePurchase $resource
     * @param PurchaseInterfaceFactory $purchaseFactory
     * @param PurchaseCollectionFactory $purchaseCollectionFactory
     * @param PurchaseSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourcePurchase $resource,
        PurchaseInterfaceFactory $purchaseFactory,
        PurchaseCollectionFactory $purchaseCollectionFactory,
        PurchaseSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->purchaseFactory = $purchaseFactory;
        $this->purchaseCollectionFactory = $purchaseCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(PurchaseInterface $purchase)
    {
        try {
            $this->resource->save($purchase);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the purchase: %1',
                $exception->getMessage()
            ));
        }
        return $purchase;
    }

    /**
     * @inheritDoc
     */
    public function get($purchaseId)
    {
        $purchase = $this->purchaseFactory->create();
        $this->resource->load($purchase, $purchaseId);
        if (!$purchase->getId()) {
            throw new NoSuchEntityException(__('Purchase with id "%1" does not exist.', $purchaseId));
        }
        return $purchase;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->purchaseCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(PurchaseInterface $purchase)
    {
        try {
            $purchaseModel = $this->purchaseFactory->create();
            $this->resource->load($purchaseModel, $purchase->getPurchaseId());
            $this->resource->delete($purchaseModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Purchase: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($purchaseId)
    {
        return $this->delete($this->get($purchaseId));
    }
}


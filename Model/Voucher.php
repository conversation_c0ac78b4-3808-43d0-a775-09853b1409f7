<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Model;

use Incert\Voucher\Api\Data\VoucherInterface;
use Magento\Framework\Model\AbstractModel;

class Voucher extends AbstractModel implements VoucherInterface
{


    const STATUS_ACTIVE = \Incert\Client\Model\Redeem\Response\StatusDetail::VALID;

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Incert\Voucher\Model\ResourceModel\Voucher::class);
    }

    /**
     * @inheritDoc
     */
    public function getVoucherId()
    {
        return $this->getData(self::VOUCHER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setVoucherId($voucherId)
    {
        return $this->setData(self::VOUCHER_ID, $voucherId);
    }

    /**
     * @inheritDoc
     */
    public function getQuoteId()
    {
        return $this->getData(self::QUOTE_ID);
    }

    /**
     * @inheritDoc
     */
    public function setQuoteId($quoteId)
    {
        return $this->setData(self::QUOTE_ID, $quoteId);
    }

    /**
     * @inheritDoc
     */
    public function getOrderId()
    {
        return $this->getData(self::ORDER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setOrderId($orderId)
    {
        return $this->setData(self::ORDER_ID, $orderId);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceId()
    {
        return $this->getData(self::INVOICE_ID);
    }

    /**
     * @inheritDoc
     */
    public function setInvoiceId($invoiceId)
    {
        return $this->setData(self::INVOICE_ID, $invoiceId);
    }

    /**
     * @inheritDoc
     */
    public function getCreditmemoId()
    {
        return $this->getData(self::CREDITMEMO_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCreditmemoId($creditmemoId)
    {
        return $this->setData(self::CREDITMEMO_ID, $creditmemoId);
    }

    /**
     * @inheritDoc
     */
    public function getAmount()
    {
        return $this->getData(self::AMOUNT);
    }

    /**
     * @inheritDoc
     */
    public function setAmount($amount)
    {
        return $this->setData(self::AMOUNT, $amount);
    }

    /**
     * @inheritDoc
     */
    public function getCurrency()
    {
        return $this->getData(self::CURRENCY);
    }

    /**
     * @inheritDoc
     */
    public function setCurrency($currency)
    {
        return $this->setData(self::CURRENCY, $currency);
    }

    /**
     * @inheritDoc
     */
    public function getRedemptionId()
    {
        return $this->getData(self::REDEMPTION_ID);
    }

    /**
     * @inheritDoc
     */
    public function setRedemptionId($redemptionId)
    {
        return $this->setData(self::REDEMPTION_ID, $redemptionId);
    }

    /**
     * @inheritDoc
     */
    public function getRedemptionAmount()
    {
        return $this->getData(self::REDEMPTION_AMOUNT);
    }

    /**
     * @inheritDoc
     */
    public function setRedemptionAmount($redemptionAmount)
    {
        return $this->setData(self::REDEMPTION_AMOUNT, $redemptionAmount);
    }

    /**
     * @inheritDoc
     */
    public function getVoucherInfo()
    {
        return $this->getData(self::VOUCHER_INFO);
    }

    /**
     * @inheritDoc
     */
    public function setVoucherInfo($voucherInfo)
    {
        return $this->setData(self::VOUCHER_INFO, $voucherInfo);
    }

    /**
     * @inheritDoc
     */
    public function getCode()
    {
        return $this->getData(self::CODE);
    }

    /**
     * @inheritDoc
     */
    public function setCode($code)
    {
        return $this->setData(self::CODE, $code);
    }
}

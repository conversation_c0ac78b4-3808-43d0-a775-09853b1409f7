<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace Incert\Voucher\Model\Product\Type;

use Magento\Framework\Exception\LocalizedException;

class Voucher extends \Magento\Catalog\Model\Product\Type\Virtual
{

    const TYPE_CODE = 'voucher';

    public function beforeSave($product)
    {
        parent::beforeSave($product);
        $this->checkPrice($product);
    }

    private function checkPrice($product)
    {
        if (is_numeric($product->getPrice())) {
            $maxPrice = max($product->getPrice(), $product->getSpecialPrice());
            $minPrice = min($product->getPrice(), $product->getSpecialPrice() ?? $product->getPrice());
            $productInfo = $this->serializer->unserialize($product->getIncertProductInfo() ?? "{}");
            if ($productInfo) {
                if (isset($productInfo['articleMinPrice'])) {
                    if ($minPrice < $productInfo['articleMinPrice']) {
                        throw new LocalizedException(__('Please use a price higher than %1',
                            $productInfo['articleMinPrice']));
                    }
                }
                if (isset($productInfo['articleMaxPrice'])) {
                    if ($maxPrice > $productInfo['articleMaxPrice']) {
                        throw new LocalizedException(__('Please use a price lower than %1',
                            $productInfo['articleMaxPrice']));
                    }
                }
            }
        }
    }
}

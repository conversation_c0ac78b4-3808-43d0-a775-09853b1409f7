<?php

namespace Incert\Voucher\Model\Product;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\Filesystem\Driver\File as FileDriver;

class ImageDownloader
{
    protected $directoryList;
    protected $file;
    protected $fileDriver;

    public function __construct(
        DirectoryList $directoryList,
        File $file,
        FileDriver $fileDriver
    ) {
        $this->directoryList = $directoryList;
        $this->file = $file;
        $this->fileDriver = $fileDriver;
    }

    public function downloadImage($url, $fileName, $timeout = 10)
    {
        try {
            $fileName = strtolower($fileName);
            $destinationFolder = $this->directoryList->getPath(DirectoryList::MEDIA) . '/catalog/product/' . $fileName[0] . DIRECTORY_SEPARATOR . ($fileName[1] ?? $fileName[0]) . DIRECTORY_SEPARATOR;
            $destinationPath = $destinationFolder .  $fileName;
            // Create destination folder if it does not exist
            if (!$this->fileDriver->isDirectory($destinationFolder)) {
                $this->file->mkdir($destinationFolder, 0755);
            }

            // Download the file with timeout and save it
            $context = stream_context_create([
                'http' => [
                    'timeout' => $timeout,
                ],
            ]);
            $fileContents = file_get_contents($url, false, $context);
            // Check if the file already exists and delete it if so
            if ($this->fileDriver->isExists($destinationPath) && $fileContents !== false) {
                $this->fileDriver->deleteFile($destinationPath);
            }

            $this->fileDriver->filePutContents($destinationPath, $fileContents);
            return $destinationPath;
        } catch (\Exception $e) {
        }
    }
}

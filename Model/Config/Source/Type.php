<?php
/**
 * Type
 * @copyright Copyright © 2020 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\Plc\Model\Config\Source;

use Magento\Framework\Option\ArrayInterface;

class Type implements ArrayInterface
{
    public function toOptionArray()
    {
        $options = [];
        $options[] = ['label' => __('PDF'), 'value' => 'pdf'];
        $options[] = ['label' => __('Zebra Printer'), 'value' => 'zpl2'];
        return $options;
    }
}
<?php

declare(strict_types=1);

namespace CopeX\VatValidator\Model\Config\Source\Qualified;

use Magento\Framework\Data\OptionSourceInterface;

class Select implements OptionSourceInterface
{


    /**
     * @return array[]
     */
    public function toOptionArray()
    {
        $resultArray = [];
        for ($i = 0; $i <= 100; $i += 5) {
            $resultArray[] = ['value' => $i, 'label' => "$i %"];
        }
        return $resultArray;
    }
}

<?php

namespace Incert\Voucher\Model\Sales\Order\Invoice\Totals;

use Incert\Voucher\Helper\Invoice;
use Incert\Voucher\Model\ResourceModel\Voucher\Collection;
use Incert\Voucher\Model\Sales\Quote\Totals\Voucher as TotalsVoucher;
use Magento\Sales\Model\Order\Invoice\Total\AbstractTotal;

class Voucher extends AbstractTotal
{

    private Invoice $invoiceHelper;

    public function __construct(Invoice $invoiceHelper) {
        $this->setCode(TotalsVoucher::CODE);
        $this->invoiceHelper = $invoiceHelper;
    }

    public function collect(\Magento\Sales\Model\Order\Invoice $invoice)
    {
        $invoice->setVoucherAmount(0);
        $invoice->setBaseVoucherAmount(0);

        $this->applyVouchers($invoice);

        $invoice->setGrandTotal($invoice->getGrandTotal() + $invoice->getVoucherAmount());
        $invoice->setBaseGrandTotal($invoice->getBaseGrandTotal() + $invoice->getBaseVoucherAmount());

        return $this;
    }

    private function applyVouchers(\Magento\Sales\Model\Order\Invoice $invoice)
    {
        $this->invoiceHelper->applyVouchers($invoice);
    }

}

<?php

namespace Incert\Voucher\Model\Sales\Order\Creditmemo\Totals;

use Incert\Voucher\Helper\Creditmemo;
use Incert\Voucher\Model\Sales\Quote\Totals\Voucher as TotalsVoucher;
use Magento\Sales\Model\Order\Creditmemo\Total\AbstractTotal;

class Voucher extends AbstractTotal
{

    private Creditmemo $creditmemoHelper;

    public function __construct(Creditmemo $creditmemoHelper)
    {
        $this->setCode(TotalsVoucher::CODE);
        $this->creditmemoHelper = $creditmemoHelper;
    }

    public function collect(\Magento\Sales\Model\Order\Creditmemo $creditMemo)
    {
        $creditMemo->setVoucherAmount(0);
        $creditMemo->setBaseVoucherAmount(0);

        $order = $creditMemo->getOrder();

        if ($order->getVoucherAmount() == 0) {
            return $this;
        }

        $this->applyVouchers($creditMemo);

        $creditMemo->setGrandTotal($creditMemo->getGrandTotal() - $creditMemo->getVoucherAmount());
        $creditMemo->setBaseGrandTotal($creditMemo->getBaseGrandTotal() - $creditMemo->getBaseVoucherAmount());

        return $this;
    }

    private function applyVouchers($creditMemo)
    {
        $this->creditmemoHelper->applyVouchers($creditMemo);
        $creditMemo->setVoucherRefunded($creditMemo->getVoucherAmount());
        $creditMemo->setBaseVoucherRefunded($creditMemo->getBaseVoucherAmount());
    }
}

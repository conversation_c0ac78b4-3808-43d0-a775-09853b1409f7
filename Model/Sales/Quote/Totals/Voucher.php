<?php

namespace Incert\Voucher\Model\Sales\Quote\Totals;

use Incert\Client\Model\Redeem\Response\StatusDetail;
use Incert\Voucher\Helper\Config;
use Incert\Voucher\Helper\CouponCodes;
use Incert\Voucher\Model\Voucher\Management;
use Incert\Voucher\Helper\Product;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Address\Total;
use Magento\Quote\Model\Quote\Address\Total\AbstractTotal;

class Voucher extends AbstractTotal
{

    const CODE = "voucher";

    protected $_code = self::CODE;

    private Config $config;

    private CouponCodes $couponCodes;
    private Management $management;
    private Product $product;

    public function __construct(
        Config $config,
        CouponCodes $couponCodes,
        Management $management,
        Product $product
    ) {
        $this->config = $config;
        $this->couponCodes = $couponCodes;
        $this->management = $management;
        $this->product = $product;
    }

    /**
     * Collect address discount amount
     * @param Quote                       $quote
     * @param ShippingAssignmentInterface $shippingAssignment
     * @param Total                       $total
     * @return $this
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function collect(
        Quote $quote,
        ShippingAssignmentInterface $shippingAssignment,
        Total $total
    ) {
        if ($this->config->isEnabled($quote->getStoreId())) {
            parent::collect($quote, $shippingAssignment, $total);

            $items = $shippingAssignment->getItems();

            if (!count($items)) {
                return $this;
            }
            $possibleVouchers = $this->couponCodes->getPossibleVouchers();
            $voucherTotalAmount = $baseVoucherTotalAmount = 0;
            $address = $shippingAssignment->getShipping()->getAddress();
            $appliedVouchers = [];
            if ($possibleVouchers) {
                $appliedVouchers = $this->applyVouchersToTotal($possibleVouchers, $quote, $total, $address);
                $voucherTotalAmount = -abs($total->getTotalAmount($this->getCode()));
                $baseVoucherTotalAmount = -abs($total->getBaseTotalAmount($this->getCode()));
                $this->management->saveQuoteToAppliedVouchers($quote, $appliedVouchers);
            }
            $this->deleteMissingVouchers($appliedVouchers, $quote);
            // Set the negatively ensured voucher amounts on the address
            $address->setVoucherAmount($voucherTotalAmount);
            $address->setBaseVoucherAmount($baseVoucherTotalAmount);
        }
        return $this;
    }

    private function applyVouchersToTotal($possibleVouchers, Quote $quote, Total $total, $address)
    {
        $appliedVouchers = [];
        $originBaseGrandTotal = $this->getBaseGrandTotal($quote, $total);
        foreach ($possibleVouchers as $possibleVoucher) {
            $voucherResult = $this->management->checkVoucher($possibleVoucher);
            if ($voucherResult->getStatus() !== StatusDetail::VOUCHER_NOT_FOUND) {
                $this->couponCodes->setIncertVoucherStatus($possibleVoucher, $voucherResult);
                if (!$voucherResult->hasError()) {
                    //Restriction BaseCurrencyCode needs to be the same as voucher currency code
                    if ($quote->getBaseCurrencyCode() === $voucherResult->getCurrency()) {
                        $alreadyAppliedVoucherAmount = $total->getBaseTotalAmount($this->getCode());
                        $baseGrandTotal = $originBaseGrandTotal + $alreadyAppliedVoucherAmount;
                        $possibleVoucherDeduction = min($baseGrandTotal, $voucherResult->getCurrentAmount());
                        if ($possibleVoucherDeduction > 0) {
                            if (abs($possibleVoucherDeduction - $voucherResult->getCurrentAmount()) > 0.0001) {
                                if (!$voucherResult->getPartlyRedeemable()) {
                                    $voucherResult->setStatus(StatusDetail::PARTLY_REDEEMABLE);
                                    continue;
                                }
                            }
                            $voucherDeductionAmount = $alreadyAppliedVoucherAmount - $possibleVoucherDeduction;
                            $total->setBaseTotalAmount($this->getCode(), -abs($voucherDeductionAmount));
                            $total->setTotalAmount($this->getCode(),
                                -abs($voucherDeductionAmount) * $quote->getBaseToQuoteRate());
                            $appliedVouchers[$possibleVoucher] = [
                                'amount'              => $possibleVoucherDeduction,
                                'result'              => $voucherResult,
                                'voucher_total_after' => $voucherDeductionAmount,
                            ];
                            $this->couponCodes->addIncertVoucher($possibleVoucher);
                        }
                    }
                    else {
                        $voucherResult->setStatus(StatusDetail::VOUCHER_NOT_FOUND);
                    }
                }
            }
        }
        $quote->setAppliedVouchers($appliedVouchers);
        return $appliedVouchers;
    }

    /**
     * Add voucher total information to address
     * @param Quote $quote
     * @param Total $total
     * @return array|null
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function fetch(Quote $quote, Total $total)
    {
        if ($this->config->isEnabled($quote->getStoreId())) {
            $amount = $total->getVoucherAmount();
            if ($amount != 0) {
                return [
                    'code'  => $this->_code,
                    'title' => __('Vouchers'),
                    'value' => $amount,
                ];
            }
        }
        return null;
    }

    public function getLabel()
    {
        return __('Vouchers');
    }

    public function getBaseGrandTotal(Quote $quote, Total $total)
    {
        $subtotalInclTax = $total->getBaseSubtotalInclTax();
        foreach ($quote->getAllItems() as $item) {
            if ($this->product->isUsableForCalculation($item->getProductType(), $item->getProduct())) {
                $subtotalInclTax -= $item->getBaseRowTotalInclTax() ?? $item->getParentItem()->getBaseRowTotalInclTax();
            }
        }
        return $subtotalInclTax + $total->getBaseShippingInclTax() + $total->getBaseDiscountAmount();
    }

    private function deleteMissingVouchers(array $appliedVouchers, $quote)
    {
        $this->management->deleteNotAppliedVoucherAssignment($appliedVouchers, $quote->getId());
    }

}

<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Model;

use Incert\Voucher\Api\Data\PurchaseInterface;
use Magento\Framework\Model\AbstractModel;

class Purchase extends AbstractModel implements PurchaseInterface
{

    const STATUS_ENABLED = 1;
    const STATUS_CANCELED = 2;

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Incert\Voucher\Model\ResourceModel\Purchase::class);
    }

    /**
     * @inheritDoc
     */
    public function getPurchaseId()
    {
        return $this->getData(self::ID);
    }

    /**
     * @inheritDoc
     */
    public function setPurchaseId($purchaseId)
    {
        return $this->setData(self::ID, $purchaseId);
    }

    /**
     * @inheritDoc
     */
    public function getVoucherCode()
    {
        return $this->getData(self::VOUCHER_CODE);
    }

    /**
     * @inheritDoc
     */
    public function setVoucherCode($voucherCode)
    {
        return $this->setData(self::VOUCHER_CODE, $voucherCode);
    }

    /**
     * @inheritDoc
     */
    public function getIncertOrderId()
    {
        return $this->getData(self::INCERT_ORDER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setIncertOrderId($incertOrderId)
    {
        return $this->setData(self::INCERT_ORDER_ID, $incertOrderId);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceId()
    {
        return $this->getData(self::INVOICE_ID);
    }

    /**
     * @inheritDoc
     */
    public function setInvoiceId($invoiceId)
    {
        return $this->setData(self::INVOICE_ID, $invoiceId);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceItemId()
    {
        return $this->getData(self::INVOICE_ITEM_ID);
    }

    /**
     * @inheritDoc
     */
    public function setInvoiceItemId($invoiceItemId)
    {
        return $this->setData(self::INVOICE_ITEM_ID, $invoiceItemId);
    }

    /**
     * @inheritDoc
     */
    public function getStatus()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * @inheritDoc
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * @inheritDoc
     */
    public function getVoucherInfo()
    {
        return $this->getData(self::VOUCHER_INFO);
    }

    /**
     * @inheritDoc
     */
    public function setVoucherInfo($voucherInfo)
    {
        return $this->setData(self::VOUCHER_INFO, $voucherInfo);
    }
}


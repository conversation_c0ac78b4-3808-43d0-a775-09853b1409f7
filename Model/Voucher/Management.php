<?php

namespace Incert\Voucher\Model\Voucher;

use Incert\Voucher\Exception\Payment;
use Incert\Voucher\Helper\Purchase;
use Incert\Voucher\Helper\Voucher as VoucherHelper;
use Incert\Voucher\Model\ResourceModel\Voucher as VoucherResourceModel;
use Incert\Voucher\Model\ResourceModel\Voucher\CollectionFactory;
use Incert\Voucher\Model\ResourceModel\Voucher\Collection;
use Incert\Voucher\Model\Voucher;
use Incert\Voucher\Model\VoucherFactory;
use Incert\Voucher\Logger\Logger;
use Magento\Framework\Exception\AlreadyExistsException;

class Management
{


    private VoucherHelper $voucher;
    private VoucherResourceModel $voucherResourceModel;
    private VoucherFactory $voucherFactory;
    private CollectionFactory $collectionFactory;
    private Purchase $purchase;
    private Logger $logger;

    public function __construct(
        VoucherHelper $voucher,
        VoucherResourceModel $voucherResourceModel,
        VoucherFactory $voucherFactory,
        CollectionFactory $collectionFactory,
        Purchase $purchase,
        Logger $logger
    ) {
        $this->voucher = $voucher;
        $this->voucherResourceModel = $voucherResourceModel;
        $this->voucherFactory = $voucherFactory;
        $this->collectionFactory = $collectionFactory;
        $this->purchase = $purchase;
        $this->logger = $logger;
    }

    public function checkVoucher($voucher, $useCache = true)
    {
        return $this->voucher->getVoucherStatusDetailsFromApi($voucher, $useCache);
    }

    public function cancelRedemption($voucher)
    {
        if ($voucher->getRedemptionId()) {
            $result = $this->voucher->cancelRedemption($voucher->getRedemptionId());
            if ($result instanceof \Incert\Client\Model\Redeem\Response\Cancel) {
                $voucher->setRefundAmount($result->getCurrentAmount());
                $this->saveVoucherModel($voucher);
            } elseif ($result instanceof \Incert\Client\Model\Response\BadRequestResponse) {
                throw new \Incert\Voucher\Exception\CancelRedemption($result->getCode() . ": " . $result->getMessage());
            }
            return $result;
        }
    }

    public function redeemVoucher($voucher, $comment = "")
    {
        $result = $this->voucher->redeemVoucher($voucher->getCode(), $voucher->getAmount(),
            $voucher->getCurrency(), $comment);
        if ($result instanceof \Incert\Client\Model\Redeem\Response) {
            $voucher->setRedemptionId($result->getRedemptionID());
            $voucher->setRedemptionAmount($result->getRedeemedAmount());
            $this->saveVoucherModel($voucher);
        } elseif ($result instanceof \Incert\Client\Model\Response\BadRequestResponse) {
            throw new \Incert\Voucher\Exception\Redeem($result->getCode() . ": " . $result->getMessage());
        }
        return $result;
    }

    public function rechargeVoucher($voucher, $amount)
    {
        $result = $this->voucher->rechargeVoucher($voucher->getCode(), $amount, $voucher->getCurrency());
        if ($result instanceof \Incert\Client\Model\Redeem\Response\RechargeVoucher) {
            $voucher->setRefundAmount($voucher->getRefundAmount() + $result->getRechargedAmount());
            $this->saveVoucherModel($voucher);
        } elseif ($result instanceof \Incert\Client\Model\Response\BadRequestResponse) {
            throw new \Incert\Voucher\Exception\Recharge($result->getCode() . ": " . $result->getMessage());
        }
        return $result;
    }

    public function cancelVoucherProduct(?\Magento\Sales\Model\Order\Creditmemo\Item $creditMemoItem)
    {
        $canceledProducts = $this->voucher->cancelVoucherProduct($creditMemoItem);
        foreach ($canceledProducts as $canceledProduct) {
            $this->purchase->save($canceledProduct);
        }
    }

    /**
     * @param $quoteId
     * @return Collection
     */
    public function getCollectionByQuoteId($quoteId)
    {
        return $this->getCollectionBy($quoteId);
    }

    public function getCollectionByOrderId($orderId)
    {
        return $this->getCollectionBy($orderId, 'order_id');
    }

    /**
     * @param $id
     * @param $field
     * @return Collection
     */
    private function getCollectionBy($id, $field = "quote_id")
    {
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter($field, $id);
        return $collection;
    }

    public function saveQuoteToAppliedVouchers(\Magento\Quote\Model\Quote $quote, array $appliedVouchers)
    {
        $collection = $this->getCollectionByQuoteId($quote->getEntityId());
        foreach ($appliedVouchers as $appliedVoucher) {
            $voucherResult = $appliedVoucher['result'];
            $voucher = $collection->getItemByColumnValue("code", $voucherResult->getCode());
            if (!$voucher) {
                $voucher = $this->voucherFactory->create();
            }
            $appliedVoucherAmount = $voucher->getAmount();
            $voucher->setQuoteId($quote->getEntityId());
            $voucher->setCode($voucherResult->getCode());
            $voucher->setAmount($appliedVoucher['amount']);
            $voucher->setCurrency($quote->getBaseCurrencyCode());
            if ($appliedVoucherAmount != $appliedVoucher['amount']) {
                $this->voucherResourceModel->save($voucher);
            }
        }
    }

    public function saveOrderToAppliedVouchers($order)
    {
        $quoteId = $order->getQuoteId();
        $collection = $this->getCollectionByQuoteId($quoteId);
        foreach ($collection as $appliedVouchers) {
            $appliedVouchers->setOrderId($order->getId());
            $this->voucherResourceModel->save($appliedVouchers);
        }
    }

    public function saveVoucherModel(Voucher $model)
    {
        try {
            $this->voucherResourceModel->save($model);
        } catch (AlreadyExistsException $e) {
            $this->logger->info( sprintf("[INCERT] Unable to save voucher model for code %s", $model->getCode()));
        }
    }

    public function deleteVoucherAssignment(Voucher $appliedVoucher)
    {
        try {
            $this->voucherResourceModel->delete($appliedVoucher);
        } catch (\Exception $e) {
            $this->logger->info( sprintf("[INCERT] Unable to delete voucher assignment %s", $appliedVoucher->getId()));
        }
    }

    public function deleteNotAppliedVoucherAssignment($appliedVouchers, $quoteId)
    {
        if ($quoteId) {
            $where = "quote_id = " . $quoteId;
            if ($appliedVouchers) {
                $where .= " AND code NOT IN ('" . implode("','", array_keys($appliedVouchers)) . "')";
            }
            $connection = $this->voucherResourceModel->getConnection();
            $connection->delete($this->voucherResourceModel->getMainTable(), $where);
        }
    }

}
<?php

namespace Incert\Voucher\Model\Voucher\Invoice;

use Incert\Voucher\Exception\ConfigException;
use Incert\Voucher\Helper\Voucher;
use Incert\Voucher\Helper\Cache;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Sales\Model\Order\Address;
use Magento\Sales\Model\Order\Invoice;
use Magento\Store\Model\ScopeInterface;

class Converter
{

    private ScopeConfigInterface $scopeConfig;
    private Voucher $voucher;
    private Cache $cache;

    public function __construct(ScopeConfigInterface $scopeConfig, Voucher $voucher, Cache $cache)
    {
        $this->scopeConfig = $scopeConfig;
        $this->voucher = $voucher;
        $this->cache = $cache;
    }

    public function getInvoiceData($invoice)
    {
        $products = $this->getShoppingCartProducts($invoice);
        if ($products) {
            $invoiceData = [
                'order_data' => [
                    'shippingModule'       => $this->getInvoiceShippingModule($invoice),
                    'paymentModule'        => $this->getInvoicePaymentModule($invoice),
                    'sendInvoice'          => $this->getSendInvoice($invoice),
                    'sendInvoiceAdminCopy' => $this->getSendInvoiceAdminCopy($invoice),
                    'customer'             => $this->getCustomer($invoice),
                    'shoppingCart'         => [
                        'products' => array_map(function($item) { return $item['voucher_data'];}, $products),
                    ],
                ],
                'products' => $products
            ];
            return $invoiceData;
        }
        return null;
    }

    public function getInvoiceShippingModule($invoice)
    {
        $info = $this->cache->getShippingAndPaymentMethod();
        if(!$info){
            $info = $this->getShippingAndPamentMethod();
        }
        return $info->getShipping()[0]->getModule();
    }

    public function getInvoicePaymentModule($invoice)
    {
        $info = $this->cache->getShippingAndPaymentMethod();
        if(!$info){
            $info = $this->getShippingAndPamentMethod();
        }
        return $info->getPayment()[0]->getModule();
    }

    /**
     * @return \Incert\Client\Model\Shop\Response\Core
     * @throws ConfigException
     */
    private function getShippingAndPamentMethod(){
        $info = $this->voucher->getShippingAndPaymentMethod();
        if ($info instanceof \Incert\Client\Model\Shop\Response\Core) {
            $this->cache->cacheShippingAndPaymentMethod($info);
        }
        else {
            /**  @var $info \Incert\Client\Model\Response\BadRequestResponse */
            throw new ConfigException($info->getCode() . ": " . $info->getMessage());
        }
        return $info;
    }

    public function getSendInvoice($invoice)
    {
        return false;
    }

    public function getSendInvoiceAdminCopy($invoice)
    {
        return false;
    }

    public function getCustomer($invoice)
    {
        /** @var Invoice $invoice */
        $billingAddress = $invoice->getBillingAddress();
        $customer = $billingAddress->getOrder()->getCustomer();
        return [
            'cid'             => $billingAddress->getCustomerId(),
            'email'           => $billingAddress->getEmail(),
            "firstName"       => $billingAddress->getFirstname(),
            "lastName"        => $billingAddress->getLastname(),
            "gender"          => $this->getCustomerGender($billingAddress),
            "dob"             => $customer ? $customer->getDob() : null,
            "phoneNumber"     => $billingAddress->getTelephone(),
            "language"        => $this->getLanguage($invoice),
            "newsletter"      => false,
            'shippingAddress' => $this->getCustomerAddress($invoice->getShippingAddress() ?: $billingAddress),
            'paymentAddress'  => $this->getCustomerAddress($billingAddress),
        ];
    }

    public function getCustomerGender($billingAddress)
    {
        $gender = $billingAddress->getOrder()->getCustomerGender();
        return match ($gender) {
            1 => 'm',
            2 => 'f',
            3 => 'c',
            default => null,
        };
    }

    public function getCustomerAddress(Address $address)
    {
        return [
            "title"      => $address->getPrefix(),
            "firstName"  => $address->getFirstname(),
            "lastName"   => $address->getLastname(),
            "gender"     => $this->getCustomerGender($address),
            "city"       => $address->getCity(),
            "postalCode" => $address->getPostcode(),
            "street"     => implode(", ", $address->getStreet()),
            "company"    => $address->getCompany(),
            "country"    => $address->getCountryId(),
        ];
    }

    public function getShoppingCartProducts($invoice)
    {
        /** @var Invoice $invoice */
        $products = [];
        $items = $invoice->getAllItems();
        foreach ($items as $item) {
            $orderItem = $item->getOrderItem();
            if ($orderItem->getProductType() == \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE) {
                $products[] = [
                    'product_id' => $item->getOrderItem()->getProduct()->getId(),
                    'invoice' => $invoice,
                    'invoice_item' => $item,
                    'voucher_data' => [
                        "id"           => (int) $item->getOrderItem()->getProduct()->getIncertProductId() ?? 1,
                        "quantity"     => (int) $item->getQty(),
                        "voucherValue" => $item->getOrderItem()->getProduct()->getPrice(),
                        "name"         => $item->getOrderItem()->getName()
                    ]
                ];
            }
        }
        return $products;
    }

    private function getLanguage(Invoice $invoice)
    {
        $storeLocale = $this->scopeConfig->getValue('general/locale/code',ScopeInterface::SCOPE_STORE,$invoice->getStoreId());
        return substr($storeLocale, 0, 2);
    }
}
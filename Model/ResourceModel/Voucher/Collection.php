<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Incert\Voucher\Model\ResourceModel\Voucher;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'voucher_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Incert\Voucher\Model\Voucher::class,
            \Incert\Voucher\Model\ResourceModel\Voucher::class
        );
    }
}


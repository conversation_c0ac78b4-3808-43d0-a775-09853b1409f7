<?php

namespace CopeX\Core\Model\Module;

use Guz<PERSON>Http\Exception\ClientException;
use Magento\Framework\App\RequestInterface;
use GuzzleHttp\Client;
use Magento\Framework\Serialize\SerializerInterface;

class Server
{
    private RequestInterface $request;
    private SerializerInterface $serializer;
    private Config $config;

    /**
     * Server constructor.
     * @param \Magento\Framework\App\Helper\Context $context
     */
    public function __construct(
        Config $config,
        RequestInterface $request,
        SerializerInterface $serializer
    ) {
        $this->config = $config;
        $this->request = $request;
        $this->serializer = $serializer;
    }

    /**
     * @return string
     */
    public function getBaseUrl()
    {
        return $this->parseUri($this->config->getStoreConfig('web/unsecure/base_url'));
    }

    /**
     * @return string
     */
    public function getServerName()
    {
        return $this->parseUri($this->request->getServer('SERVER_NAME') ?? $this->request->getServer('HTTP_HOST') ?? $this->request->getServer('HOSTNAME') ?? "" );
    }

    private function parseUri($uri)
    {
        $url = str_replace(['http://', 'https://', 'www.'], '', $uri);
        $url = explode('/', $url);
        $url = array_shift($url);
        $parsedUrl = parse_url($url, PHP_URL_HOST);
        if ($parsedUrl !== null) {
            return $parsedUrl;
        }
        return $url;
    }

    public function isDev(): bool
    {
        $baseUrl = $this->getBaseUrl();
        $excludedTLD = ['.local', '.test', 'localhost', '.dev'];
        foreach ($excludedTLD as $tld) {
            if (substr($baseUrl, -strlen($tld)) === $tld) {
                return true;
            }
        }
        return false;
    }

    public function ping($module, $version)
    {
        try {
            $client = $this->getClient();
            $response = $client->get(str_rot13('cvat'), ['query' => $this->getModuleInformation($module, $version)]);
            return $response->getBody()->getContents();
        } catch (\Exception $e) {
        }
        return "";
    }

    public function getSerialFromServer($module, $version, $coreVersion)
    {
        try {
            $client = $this->getClient();
            $result = $client->post("check", [
                'json'    => $this->getModuleInformation($module, $version, $coreVersion),
                'timeout' => 2,
            ]);
            $responseContent = $result->getBody()->getContents();
            if ($result->getStatusCode() == 200) {
                $response = $this->serializer->unserialize($responseContent);
                if (in_array($response['status'] ?? "", ['active', 'success'])) {
                    return $response['serial'] ?? "";
                }
                throw new Server\ExpiredException("$module serial expired");
            }
        } catch (ClientException $e) {
            if($e->getCode() == 404) {
                throw new Server\NotFoundException($e->getMessage(), $e->getCode(), $module, $version,
                    $e->hasResponse() ? $this->serializer->unserialize($e->getResponse()->getBody()->getContents()) : null);
            }
        }
        throw new Server\ServerException("$module server error");
    }

    private function getClient()
    {
        $client = new Client([
            'base_uri'        => $this->getUri(),
            'timeout'         => 1,
            'connect_timeout' => 0.5,
            'read_timeout'    => 0.5,
            'http_errors'     => true,
            'verify'          => false,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);
        return $client;
    }

    /**
     * @return string
     */
    private function getUri(): string
    {
        return str_rot13('uggcf://yvprafr.pbcrk.vb');
    }

    /**
     * @param array $configuration
     * @return array
     */
    private function getModuleInformation($module, $version, $coreVersion): array
    {
        $baseUrl = $this->getBaseUrl();
        $serverName = $this->getServerName();
        return [
            'module'     => $module,
            'version'    => $version,
            'baseUrl'    => $baseUrl,
            'serverName' => $serverName,
            'core'       => $coreVersion,
        ];
    }
}
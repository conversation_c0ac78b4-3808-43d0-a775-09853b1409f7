<?php

namespace CopeX\Plc\Helper;

use Magento\Framework\App\Helper\AbstractHelper;

class Retouring extends AbstractHelper
{
    public const IS_RETOURING_ENABLED = "copex_plc/retouring/enable_retouring";
    public const IS_RETOURING_ENABLED_FRONTEND = "copex_plc/retouring/enable_retouring_frontend";
    public const DAYS_RETOURING_ALLOWED = "copex_plc/retouring/days_customer_retouring";
    public const RETOURING_WITHOUT_LABEL = "copex_plc/retouring/enable_retouring_without_label";
    public const AUTOMATIC_RETOURING_WITH_LABEL = "copex_plc/retouring/automatic_retouring_with_label";
    public const NO_INTERNATIONAL_RETOURING = "copex_plc/retouring/no_international_retouring";

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $date;
    /**
     * @var Data
     */
    protected $dataHelper;

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        Data $dataHelper
    ) {
        parent::__construct($context);
        $this->date = $date;
        $this->dataHelper = $dataHelper;
    }

    public function isReturnValid($order)
    {
        if (!$order->getIsVirtual() && $this->isFrontendReturnEnabled($order->getStoreId())) {
            if (!$this->isReturnWithoutLabelAllowed($order->getStoreId())) {
                $label = $this->dataHelper->getLabelByOrderId($order->getId());
                if (!$label || !$label->getId()) {
                    return false;
                }
            }
            $days = $this->getDaysReturnConfig($order->getStoreId());
            if (is_numeric($days)){
                if($days < 0) return true;
                $compareDate = $this->date->date(null, $order->getCreatedAt());
                $date = strtotime("+" . $days . " days", strtotime($compareDate));
                if (strtotime($this->date->gmtDate()) <= $date) {
                    return true;
                }
            }
        }
        return false;
    }

    public function isFrontendReturnEnabled($storeViewCode = null)
    {
        return $this->isReturnEnabled($storeViewCode) && $this->scopeConfig->getValue(self::IS_RETOURING_ENABLED_FRONTEND,
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeViewCode);
    }

    public function isReturnEnabled($storeViewCode = null)
    {
        return $this->scopeConfig->getValue(self::IS_RETOURING_ENABLED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeViewCode);
    }

    public function isReturnWithoutLabelAllowed($storeViewCode = null)
    {
        return $this->scopeConfig->getValue(self::RETOURING_WITHOUT_LABEL,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeViewCode);
    }

    public function getDaysReturnConfig($storeViewCode = null)
    {
        return $this->scopeConfig->getValue(self::DAYS_RETOURING_ALLOWED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeViewCode);
    }

    public function isLabelReturn($label)
    {
        return $this->dataHelper->isReturn($label->getPacketOption());
    }

}

<?php

namespace Incert\Voucher\Helper;

use Magento\Catalog\Model\ResourceModel\Product as ResourceModel;

class Product
{

    private ResourceModel $resourceModel;

    public function __construct(ResourceModel $resourceModel)
    {
        $this->resourceModel = $resourceModel;
    }

    public function isVoucherProductType($productType)
    {
        return $productType === \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE;
    }

    public function isProductExcluded($product)
    {
        if(!$product->hasData('no_apply_incert_voucher')){
            $value = $this->resourceModel->getAttributeRawValue($product->getId(), 'no_apply_incert_voucher',
                $product->getStoreId());
        }else {
            $value = $product->getData('no_apply_incert_voucher');
        }
        return (bool) $value;
    }

    /**
     * Check whether a product can be used for
     * discount / voucher amount calculation on redeeming a voucher
     *
     * because in this case we need to reduce the max possible amount
     * (grand total where the voucher can be redeemed on)
     * by the value of the product
     *
     * @param $productType
     * @param $product
     * @return bool
     */
    public function isUsableForCalculation($productType, $product)
    {
        return $this->isVoucherProductType($productType) || $this->isProductExcluded($product);
    }

}
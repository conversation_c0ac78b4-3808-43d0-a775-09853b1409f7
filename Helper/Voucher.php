<?php

namespace Incert\Voucher\Helper;

use Incert\Client\Api\CountryApi;
use Incert\Client\Api\OAuth20ClientCredentialsApi;
use Incert\Client\Api\RedemptionApi;
use Incert\Client\Api\ShopApi;
use Incert\Client\Configuration;
use Incert\Client\Model\Response\BadRequestResponse;
use Incert\Client\Model\Response\UnauthorizedResponse;
use Incert\Client\OAuth20\GrantAccessTokenRequest;
use Incert\Voucher\Logger\Logger;

class Voucher
{

    private Config $configHelper;
    private Cache $cache;
    private ?Configuration $configuration = null;
    private Logger $logger;

    public function __construct( Config $configHelper, Cache $cache, Logger $logger)
    {
        $this->configHelper = $configHelper;
        $this->cache = $cache;
        $this->logger = $logger;
    }

    public function getVoucherStatusDetailsFromApi( $voucher, $useCache = true )
    {
        $voucherResult = false;
        if ($useCache) {
            $voucherResult = $this->cache->getVoucherResultFromCache($voucher);
        }
        if (!$voucherResult) {
            $voucherResult = $this->getRedemptionAPI()->shopV2RedeemStatusDetailCodeGet($voucher, $this->configHelper->getBookingPartnerID());
            $this->cache->cacheVoucherResult($voucherResult);
        }
        return $voucherResult;
    }

    private function getAccessToken()
    {
        $token = $this->cache->getAccessToken();
        if(!$token){
            $config = $this->getConfiguration();
            $apiInstance = new OAuth20ClientCredentialsApi($config);

            $accessToken = new GrantAccessTokenRequest();
            $accessToken->setClientId($this->configHelper->getClientId());
            $accessToken->setClientSecret($this->configHelper->getClientSecret());

            $tokenResult = $apiInstance->oauthAccessTokenPost($accessToken);
            if($tokenResult instanceof \Incert\Client\OAuth20\GrantAccessTokenResponse){
                $token = $tokenResult->getAccessToken();
                $this->cache->cacheAccessToken($token, $tokenResult->getExpiresIn() - 200);
            }
            else {
                $this->logger->info( sprintf("[INCERT] Unable to get access token for client ( %s )", $this->configHelper->getClientId()));
            }
        }
        return $token;
    }

    /**
     * @return Configuration
     */
    private function getConfiguration()
    {
        if($this->configuration === null){
            $this->configuration = new Configuration();
            $this->configuration->setApiKey($this->configHelper->getAPIKey());
            $this->configuration->setHost($this->configHelper->getHost());
            $this->configuration->setAccessToken($this->getAccessToken());
        }
        return $this->configuration;
    }

    private function getRedemptionAPI()
    {
        return new RedemptionApi( $this->getConfiguration() );
    }

    private function getCountryAPI()
    {
        return new CountryApi($this->getConfiguration());
    }

    private function getShopAPI()
    {
        return new ShopApi($this->getConfiguration());
    }

    /**
     * @param $redemptionId
     * @return mixed
     * @throws \Incert\Client\Api\Exception\ApiException
     */
    public function cancelRedemption($redemptionId)
    {
        $this->logger->info( sprintf("[INCERT] Cancel redemption ( %s )", $redemptionId));
        $cancelRedemption = new \Incert\Client\Model\Redemption\CancelRequest();
        $cancelRedemption->setRedemptionID($redemptionId);
        $result = $this->getRedemptionAPI()->shopV2RedeemCancelPost($cancelRedemption);
        $this->logPossibleError($result);
        return $result;
    }

    /**
     * Redeem a voucher
     *
     * @param $couponCode
     * @param $amount
     * @param $currencyCode
     * @param $comment
     * @return mixed
     */
    public function redeemVoucher($couponCode, $amount, $currencyCode, $comment = "")
    {
        $this->logger->info( sprintf("[INCERT] Redeem voucher ( %s ) - %s - %s - %s", $couponCode, $currencyCode, $amount,  $comment));
        $redeemRequest = new \Incert\Client\Model\Redeem\Request();
        $redeemRequest->setCode($couponCode);
        $redeemRequest->setAmount($amount);
        $redeemRequest->setCurrency($currencyCode);
        $redeemRequest->setComment($comment);
        $redeemRequest->setBookingPartnerID($this->configHelper->getBookingPartnerID());
        $result = $this->getRedemptionAPI()->shopV2RedeemPost($redeemRequest);
        $this->logPossibleError($result);
        return $result;
    }

    public function rechargeVoucher($couponCode, $amount, $currencyCode)
    {
        $this->logger->info( sprintf("[INCERT] Recharge voucher ( %s ) with amount %s %s", $couponCode, $amount, $currencyCode));
        $rechargeVoucherRequest = new \Incert\Client\Model\Redemption\RechargeVoucher();
        $rechargeVoucherRequest->setCode($couponCode);
        $rechargeVoucherRequest->setAmount($amount);
        $rechargeVoucherRequest->setCurrency($currencyCode);
        $rechargeVoucherRequest->setBookingPartnerID($this->configHelper->getBookingPartnerID());
        $result = $this->getRedemptionAPI()->shopV2RedeemRechargeVoucherPost($rechargeVoucherRequest);
        $this->logPossibleError($result);
        return $result;
    }

    public function calculateOrder($countryCode, $currency, $data)
    {
        if ($data) {
            $order = new \Incert\Client\Model\Shop\Request\Order($data);
            $result = $this->getShopAPI()->shopV2OrderCalculatePost($countryCode, $currency, $order);
            $this->logPossibleError($result);
            return $result;
        }
        return null;
    }

    public function order($countryCode, $currency, $data)
    {
        if ($data) {
            $this->logger->debug( sprintf("[INCERT] Order voucher ( %s ) - %s || %s", $countryCode, $currency, json_encode($data)));
            $order = new \Incert\Client\Model\Shop\Request\Order($data);
            $result = $this->getShopAPI()->shopV2OrderPost($countryCode, $currency, $order);
            $this->logPossibleError($result);
            return $result;
        }
        return null;
    }

    public function getShippingAndPaymentMethod($lang = "")
    {
        return $this->getShopAPI()->shopV2ModulesCoreGet($lang);
    }

    public function getProductsFromApi($countryCode = null, $currency = null, $cache = true)
    {
        if (!$countryCode) {
            $countryCode = $this->configHelper->getDefaultCountry();
        }
        if (!$currency) {
            $currency = $this->configHelper->getDefaultCurrency();
        }
        $result = $this->cache->getProductsFromCache($countryCode, $currency);
        if (!$result) {
            $result = $this->getShopAPI()->shopV2CoreProductGet($countryCode, $currency);
            $this->logPossibleError($result);
            $this->cache->cacheProducts($result, $countryCode, $currency);
        }
        if($result instanceof \Incert\Client\Model\Shop\Response\Product\Collection){
            $products = $result->getProducts();
            return count($products) ? $result->getProducts() : [] ;
        }
        return [];
    }

    public function downloadVoucher($orderId, $voucherCode)
    {
        $result = $this->getShopAPI()->shopV2VoucherDownloadGet($orderId, $voucherCode);
        $this->logPossibleError($result);
        return $result;
    }


    public function canCancelVoucherProduct($incertOrderId)
    {
        $result = $this->getShopAPI()->shopV2OrderCanCancelGet($incertOrderId);
        $this->logPossibleError($result);
        return $result;
    }

    public function cancelVoucherProduct(?\Magento\Sales\Model\Order\Creditmemo\Item  $creditmemoItem)
    {
        $orderItem = $creditmemoItem->getOrderItem();
        $qty = $creditmemoItem->getQty() ?? $orderItem->getQtyToRefund();
        $connectedProducts = $orderItem->getConnectedIncertProducts();
        $cancelledProducts = [];
        foreach ($connectedProducts as $connectedProduct) {
            if ($connectedProduct->getStatus() == \Incert\Voucher\Model\Purchase::STATUS_ENABLED) {
                if($connectedProduct->getIncertData()['cancel_allowed'] ?? false){
                    $this->logger->alert( sprintf("[INCERT] Cancel Voucher ( Invoice ID: %s - Invoice Item ID: %s - Code: %s )", $connectedProduct->getInvoiceId(), $connectedProduct->getInvoiceItemID(), $connectedProduct->getVoucherCode()));
                    $request = new \Incert\Client\Model\Shop\Request\Voucher\Cancel();
                    $request->setVoucherCode($connectedProduct->getVoucherCode());
                    $result = $this->getShopAPI()->shopV2VoucherCancelPost($connectedProduct->getIncertOrderId(), $request);
                    $this->logPossibleError($result);
                    if($result instanceof \Incert\Client\Model\Shop\Response\Voucher\Cancel){
                        $connectedProduct->setStatus(\Incert\Voucher\Model\Purchase::STATUS_CANCELED);
                        $cancelledProducts [] = $connectedProduct;
                    }
                    $qty--;
                }
            }
            if($qty <= 0){
                break;
            }
        }
        return $cancelledProducts;
    }

    protected function logPossibleError(mixed $result)
    {
        if($result instanceof UnauthorizedResponse || $result instanceof BadRequestResponse) {
            $this->logger->alert( sprintf("[INCERT] ( %s ) - %s || %s", $result->getCode(), $result->getMessage(), $result->getError()));
        }
    }

    /**
     * @return Config
     */
    public function getConfig()
    {
        return $this->configHelper;
    }

}
<?php

namespace Incert\Voucher\Helper;

use Magento\Framework\App\CacheInterface;
use Magento\Framework\Serialize\SerializerInterface;

class Cache
{

    const CACHE_TAG = "incert";
    const CACHE_KEY = "incert";

    const CACHE_KEY_TOKEN = self::CACHE_KEY . '_api_token';
    const CACHE_KEY_COUPON = self::CACHE_KEY . '_coupon';
    const CACHE_KEY_SHIPPING_AND_PAYMENT = self::CACHE_KEY . '_shipping_payment';
    const CACHE_KEY_PRODUCTS = self::CACHE_KEY . '_products';

    private CacheInterface $cache;
    private SerializerInterface $serializer;

    public function __construct(CacheInterface $cache, SerializerInterface $serializer)
    {
        $this->cache = $cache;
        $this->serializer = $serializer;
    }

    public function cacheAccessToken($token, $lifetime)
    {
        return $this->cacheData($token,self::CACHE_KEY_TOKEN, $lifetime ?? 86400);
    }

    public function getAccessToken()
    {
        return $this->cache->load(self::CACHE_KEY_TOKEN);
    }

    public function cacheVoucherResult($voucherResult)
    {
        return $this->cacheData($this->serializer->serialize($voucherResult),
            self::CACHE_KEY . "_" . $voucherResult->getCode());
    }

    public function getVoucherResultFromCache($couponCode)
    {
        $voucherResult = $this->cache->load(self::CACHE_KEY_COUPON . "_" . $couponCode);
        return $voucherResult
            ? new \Incert\Client\Model\Redeem\Response\StatusDetail($this->serializer->unserialize($voucherResult))
            : false;
    }

    public function removeVoucherResultFromCacheByCouponCode($couponCode)
    {
        return $this->cache->remove(self::CACHE_KEY_COUPON . "_" . $couponCode);
    }

    public function cacheShippingAndPaymentMethod($shippingMethod)
    {
        return $this->cacheData($this->serializer->serialize($shippingMethod),
            self::CACHE_KEY_SHIPPING_AND_PAYMENT);
    }

    public function getShippingAndPaymentMethod()
    {
        $result = $this->cache->load(self::CACHE_KEY_SHIPPING_AND_PAYMENT);
        return $result
            ? new \Incert\Client\Model\Shop\Response\Core($this->serializer->unserialize($result))
            : false;
    }

    public function cacheProducts($products, $countryCode, $currency)
    {
        return $this->cacheData($this->serializer->serialize($products),
            $this->getProductCacheKey($countryCode, $currency));
    }

    /**
     * @return false|\Incert\Client\Model\Shop\Response\Product\Collection
     */
    public function getProductsFromCache($countryCode, $currency)
    {
        $result = $this->cache->load($this->getProductCacheKey($countryCode, $currency));
        if ($result) {
            $products = $this->serializer->unserialize($result)['products'] ?? [];
            return new \Incert\Client\Model\Shop\Response\Product\Collection($products);
        }
        return false;
    }

    private function getProductCacheKey($countryCode, $currency)
    {
        return self::CACHE_KEY_PRODUCTS . "_" . $countryCode . "_" . $currency;
    }

    private function cacheData($data, $key, $lifeTime = 86400)
    {
        return $this->cache->save(
            $data,
            $key,
            [self::CACHE_TAG],
            $lifeTime
        );
    }
}
<?php

namespace Incert\Voucher\Helper;

use Incert\Client\Model\Redeem\Response\StatusDetail;
use Magento\SalesRule\Model\ResourceModel\Coupon\CollectionFactory;

class CouponCodes
{

    private CollectionFactory $collectionFactory;
    private bool $splitCouponsExecuted = false;
    private array $magentoCouponCodes = [];
    private array $possibleVouchers = [];
    private array $incertVouchers = [];
    private array $incertVoucherStatus = [];
    private $initialCouponCode = "";

    public function __construct(CollectionFactory $collectionFactory)
    {
        $this->collectionFactory = $collectionFactory;
    }

    public function getMagentoCouponCodesFromDB(array $codes)
    {
        $collection = $this->collectionFactory->create();
        $collection->getSelect()->where(
            "UPPER(code) in ('" . implode("','", array_map("strtoupper", $codes)) . "')"
        );
        return $collection->getColumnValues("code");
    }

    public function splitCouponCodes(string $couponCode, $pattern = null)
    {
        if ($couponCode && !$this->splitCouponsExecuted) {
            $this->initialCouponCode = $couponCode;
            $couponCodes = array_map("trim", explode(",", $couponCode));
            if ($pattern) {
                $this->possibleVouchers = preg_grep($pattern, $couponCodes);
                $this->magentoCouponCodes = array_udiff($couponCodes, $this->possibleVouchers,'strcasecmp');
            }
            else {
                $this->magentoCouponCodes = $this->getMagentoCouponCodesFromDB($couponCodes);
                $this->possibleVouchers = array_udiff($couponCodes, $this->magentoCouponCodes,'strcasecmp');
            }
            $this->splitCouponsExecuted = true;
        }
    }


    public function getPossibleVouchers()
    {
        return $this->possibleVouchers;
    }

    public function getMagentoCouponCodes()
    {
        return $this->magentoCouponCodes;
    }

    public function getInitialCouponCode()
    {
        return $this->initialCouponCode;
    }

    public function getIncertVouchers()
    {
        return array_unique($this->incertVouchers);
    }


    public function addIncertVoucher($voucher)
    {
        $this->incertVouchers [] = $voucher;
    }

    public function setIncertVoucherStatus($voucher, StatusDetail $status)
    {
        $this->incertVoucherStatus[$voucher] = $status;
    }

    /**
     * @param $voucher
     * @return StatusDetail[]
     */
    public function getIncertVoucherStatus()
    {
        return $this->incertVoucherStatus ?? [];
    }
}
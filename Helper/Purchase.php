<?php

namespace Incert\Voucher\Helper;

use Incert\Voucher\Exception\Payment;
use Incert\Voucher\Model\ResourceModel\Purchase as ResourceModel;
use Incert\Voucher\Model\PurchaseFactory as ModelFactory;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Model\Order\Invoice;
use Incert\Voucher\Model\Voucher\Invoice\Converter as InvoiceConverter;

class Purchase
{
    private ResourceModel $resourceModel;
    private ModelFactory $factory;
    private SerializerInterface $serializer;

    const VALIDATED_ORDER_KEY = 'validated_voucher_orders';
    private Voucher $voucher;
    private InvoiceConverter $converter;

    public function __construct(ResourceModel $resourceModel, ModelFactory $factory, SerializerInterface $serializer, Voucher $voucher, InvoiceConverter $converter)
    {
        $this->resourceModel = $resourceModel;
        $this->factory = $factory;
        $this->serializer = $serializer;
        $this->voucher = $voucher;
        $this->converter = $converter;
    }

    public function orderValidatedVoucherProducts($invoice)
    {
        $invoiceData = $invoice->getData(self::VALIDATED_ORDER_KEY);
        if($invoiceData && $invoiceData['products'] ?? ''){
            $orderResult = $this->voucher->order($invoiceData['countryId'], $invoiceData['currencyCode'], $invoiceData['order_data']);
            if ($orderResult instanceof \Incert\Client\Model\Shop\Response\Order) {
                foreach ($orderResult->getOrderedVouchers() as $voucher) {
                    foreach( $invoiceData['products'] as &$voucherProduct){
                        $voucherData = $voucherProduct['voucher_data'];
                        if($voucherData['id'] == $voucher->getProductId() && $voucherData['voucherValue'] == $voucher->getVoucherValue() && $voucherData['quantity'] > 0){
                            $this->savePurchasedProducts($voucher, $orderResult->getOrderId(), $voucherProduct['invoice']->getId(), $voucherProduct['invoice_item']->getId());
                            $voucherProduct['voucher_data']['quantity'] = $voucherProduct['voucher_data']['quantity'] - 1;
                            break;
                        }
                    }
                }
            }
        }
    }

    public function savePurchasedProducts($voucher, $orderId, $invoiceId, $invoiceItemId)
    {
        $purchaseModel = $this->factory->create()->setData([
            'voucher_code' => $voucher->getVoucherCode(),
            'incert_order_id' => $orderId,
            'invoice_id' => $invoiceId,
            'invoice_item_id' => $invoiceItemId,
            'status' => \Incert\Voucher\Model\Purchase::STATUS_ENABLED,
            'voucher_info' => !is_string($voucher) ? $this->serializer->serialize($voucher) : $voucher,
        ]);
        $this->resourceModel->save($purchaseModel);
    }

    /**
     * Checks against the Incert API if the purchase would be possible
     * and saves the successfull order data to the invoice for later processing on actual payment
     *
     * @param Invoice $invoice
     * @return void
     * @throws Payment
     */
    public function checkVoucherProducts(Invoice $invoice)
    {
        $invoiceData = $this->converter->getInvoiceData($invoice);
        if ($invoiceData) {
            $countryId = $invoice->getBillingAddress()->getCountryId();
            $currencyCode = $invoice->getBaseCurrencyCode();
            $invoiceData['countryId'] = $countryId;
            $invoiceData['currencyCode'] = $currencyCode;
            $result = $this->voucher->calculateOrder($countryId, $currencyCode, $invoiceData['order_data']);
            if ($result instanceof \Incert\Client\Model\Shop\Response\Order\Validate) {
                if ($result->getCanProcess()) {
                    $invoice->setData(
                        Purchase::VALIDATED_ORDER_KEY,
                        $invoiceData
                    );
                }
            } else {
                throw new Payment($result->getCode() . ": " . $result->getMessage());
            }
        }
    }

    /**
     * @param \Incert\Voucher\Model\Purchase $purchase
     * @return void
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    public function save(\Incert\Voucher\Model\Purchase $purchase)
    {
        $this->resourceModel->save($purchase);
    }

}
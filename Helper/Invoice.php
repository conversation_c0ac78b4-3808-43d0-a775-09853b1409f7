<?php

namespace Incert\Voucher\Helper;

use Magento\Catalog\Model\ProductRepository;
use Magento\Sales\Model\Order\Invoice\Item;

class Invoice
{

    private Product $product;

    public function __construct(Product $product)
    {
        $this->product = $product;
    }

    public function applyVouchers($model)
    {
        $invoice = $model;
        if (((string)$invoice->getOrder()->getBaseGrandTotal()) == (string)($invoice->getBaseGrandTotal() + $invoice->getOrder()->getBaseVoucherAmount())) {
            $invoice->setBaseVoucherAmount( $invoice->getOrder()->getBaseVoucherAmount());
            $invoice->setVoucherAmount($invoice->getOrder()->getVoucherAmount());
        }else {
            $possibleVoucherDeduction = abs($invoice->getOrder()->getBaseVoucherAmount() -
                              $invoice->getOrder()->getBaseVoucherInvoiced() );
            $possibleVoucherDeduction = min($possibleVoucherDeduction, $this->getCalculationTotal($invoice));
            if ($possibleVoucherDeduction > 0) {
                $invoice->setBaseVoucherAmount( -abs($possibleVoucherDeduction));
                $invoice->setVoucherAmount($invoice->getBaseVoucherAmount() * $invoice->getBaseToOrderRate());
            }
        }
        return $invoice;
    }

    protected function getCalculationTotal($model)
    {
        $baseGrandTotal = $model->getBaseGrandTotal();
        foreach ($model->getAllItems() as $item) {
            if($this->product->isUsableForCalculation($item->getOrderItem()->getProductType(), $item->getOrderItem()->getProduct())){
                $baseGrandTotal -= $this->getItemPrice($item);
            }
        }
        return $baseGrandTotal;
    }

    protected function getItemPrice(Item $item){
        $itemPrice = $item->getBaseRowTotalInclTax() + $item->getBaseDiscountAmount();
        if (abs(0 - $itemPrice) > PHP_FLOAT_EPSILON) {
            return $itemPrice;
        }
        $orderItem = $item->getOrderItem();
        if ($orderItem) {
            $parentItem = $orderItem->getParentItem();
            if ($parentItem) {
                $modelItems = $this->getModel($item)->getAllItems();
                foreach ($modelItems as $modelItem) {
                    if($modelItem->getOrderItemId() == $parentItem->getItemId()){
                        return $modelItem->getBaseRowTotalInclTax() + $modelItem->getBaseDiscountAmount();
                    }
                }
            }
        }
        return 0;
    }

    protected function getModel($item)
    {
        return $item->getInvoice();
    }

}
<?php

namespace Incert\Voucher\Helper;


use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Config
{

    const PATH_IS_ENABLED = "promo/incert/enabled";
    const PATH_HOST = "promo/incert/host";
    const PATH_API_KEY = "promo/incert/api_key";
    const PATH_CLIENT_ID = "promo/incert/client_id";
    const PATH_CLIENT_SECRET = "promo/incert/client_secret";
    const PATH_BOOKING_PARTNER = "promo/incert/booking_partner";
    const PATH_PATTERN = "promo/incert/regex";
    const PATH_ALLOW_DISCOUNT = "promo/incert/allow_discount";
    const PATH_ATTRIBUTE_SET = "promo/incert/attribute_set";

    private ScopeConfigInterface $scopeConfig;

    public function __construct( ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }


    public function isEnabled($storeId = null)
    {
        return $this->scopeConfig->getValue(self::PATH_IS_ENABLED, ScopeInterface::SCOPE_STORE, $storeId);
    }


    public function getHost()
    {
        return $this->scopeConfig->getValue(self::PATH_HOST, ScopeInterface::SCOPE_WEBSITE);
    }

    public function getAPIKey()
    {
        return $this->scopeConfig->getValue(self::PATH_API_KEY, ScopeInterface::SCOPE_WEBSITE);
    }

    public function getClientId()
    {
        return $this->scopeConfig->getValue(self::PATH_CLIENT_ID, ScopeInterface::SCOPE_WEBSITE);
    }

    public function getClientSecret()
    {
        return $this->scopeConfig->getValue(self::PATH_CLIENT_SECRET, ScopeInterface::SCOPE_WEBSITE);
    }

    public function getBookingPartnerID()
    {
        return $this->scopeConfig->getValue(self::PATH_BOOKING_PARTNER, ScopeInterface::SCOPE_WEBSITE);
    }


    public function getPattern()
    {
        return $this->scopeConfig->getValue(self::PATH_PATTERN, ScopeInterface::SCOPE_WEBSITE);
    }

    public function canApplyDiscount()
    {
        return $this->scopeConfig->getValue(self::PATH_ALLOW_DISCOUNT, ScopeInterface::SCOPE_WEBSITE);
    }

    public function getDefaultCountry()
    {
        return $this->scopeConfig->getValue("general/country/default", ScopeInterface::SCOPE_STORE);
    }

    public function getDefaultCurrency()
    {
        return $this->scopeConfig->getValue("currency/options/base", ScopeInterface::SCOPE_WEBSITE);
    }

    public function getStoreLanguage($storeId = null){
        $storeLocale = $this->scopeConfig->getValue('general/locale/code',ScopeInterface::SCOPE_STORE, $storeId);
        return substr($storeLocale, 0, 2) ?? "en";
    }

    public function getAttributeSetId()
    {
        return $this->scopeConfig->getValue(self::PATH_ATTRIBUTE_SET) ?? 4;
    }


    public function getConfigValue($path, $scope = ScopeInterface::SCOPE_STORE, $store = null)
    {
        return $this->scopeConfig->getValue($path, $scope, $store);
    }
}
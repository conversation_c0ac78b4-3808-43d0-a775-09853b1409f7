<?php

namespace Incert\Voucher\Helper;

class Creditmemo extends Invoice
{

    private Order $order;

    public function __construct(Order $order, Product $product)
    {
        $this->order = $order;
        parent::__construct($product);
    }

    public function applyVouchers($model)
    {
        $creditMemo = $model;
        $order = $creditMemo->getOrder();
        $voucherAmount = $order->getBaseVoucherInvoiced() + $order->getBaseVoucherRefunded();
        $totalNotRefunded = $order->getBaseTotalInvoiced() - $order->getBaseTotalRefunded();
        $total = $this->getCalculationTotal($creditMemo);
        if ($total > $totalNotRefunded) {
            $creditMemo->setBaseVoucherAmount( min(abs($voucherAmount), $total - $totalNotRefunded));
            $creditMemo->setVoucherAmount($creditMemo->getBaseVoucherAmount() * $creditMemo->getBaseToOrderRate());
        }
    }

    protected function getModel($item)
    {
        return $item->getCreditmemo();
    }

    public function refundVoucherOnCreditmemo($creditMemo)
    {
        return $this->order->refundVoucherOnOrder($creditMemo->getOrder(), abs($creditMemo->getBaseVoucherAmount()));
    }

}
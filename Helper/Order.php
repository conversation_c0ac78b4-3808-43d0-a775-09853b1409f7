<?php

namespace Incert\Voucher\Helper;

use Incert\Voucher\Model\ResourceModel\Voucher\Collection;
use Incert\Voucher\Model\Voucher\Management;
use Magento\Setup\Exception;

class Order
{

    private Management $management;

    public function __construct(Management $voucherManagement)
    {
        $this->management = $voucherManagement;
    }

    /**
     * (Partly) Recharge Voucher
     * @param $order
     * @throws Exception
     */
    public function cancelOrder($order)
    {
       $this->refundVoucherOnOrder($order,$order->getBaseVoucherAmount() - $order->getBaseVoucherInvoiced());
    }

    public function refundVoucherOnOrder($order, $amountToRefund)
    {
        $remainingRefund = abs($amountToRefund); // Amount to refund
        if ($remainingRefund > 0) {
            $alreadyRefunded = $order->getBaseVoucherRefunded(); // Previously refunded amount
            $appliedVouchers = array_reverse($this->management->getCollectionByOrderId($order->getId())->getItems());

            foreach ($appliedVouchers as $appliedVoucher) {
                $voucherAmount = $appliedVoucher->getAmount(); // Amount on this voucher
                $refundableOnThisVoucher = $voucherAmount - abs($alreadyRefunded); // Amount available for refund on this voucher

                if ($refundableOnThisVoucher > 0) {
                    if (abs(min($refundableOnThisVoucher,$remainingRefund) - $voucherAmount) < 0.00001) {
                        // Refund the entire amount from this voucher
                        $this->management->cancelRedemption($appliedVoucher);
                        $remainingRefund -= $refundableOnThisVoucher;
                    } else {
                        // Refund a partial amount from this voucher
                        $refundAmount = min($remainingRefund, $refundableOnThisVoucher);
                        $this->management->rechargeVoucher($appliedVoucher, $refundAmount);
                        $remainingRefund -= $refundAmount; // No remaining refund amount
                    }
                }
                $alreadyRefunded = max(0, $alreadyRefunded - $refundableOnThisVoucher); // Update the already refunded amount

                if ($remainingRefund <= 0) {
                    break; // If no remaining refund, exit the loop
                }
            }
        }
        return $this;
    }

    /**
     * Redeem Voucher
     * Redeems an incert voucher and invalidates cache
     *
     * @param $order
     * @return void
     */
    public function redeemVouchers($order)
    {
        $voucherCollection = $this->management->getCollectionByOrderId($order->getId());
        /** @var \Incert\Voucher\Model\Voucher $voucher */
        foreach ($voucherCollection as $voucher) {
            $this->management->redeemVoucher($voucher);
            $this->management->checkVoucher($voucher->getCode(), false); //To cache new balance
        }
    }

}
# Mage2 Module Incert Voucher

    ``incert/module-voucher``

 - [Main Functionalities](#markdown-header-main-functionalities)
 - [Installation](#markdown-header-installation)
 - [Configuration](#markdown-header-configuration)
 - [Product Attributes](#markdown-header-product-attributes)
 - [Product Type](#markdown-header-product-type)
 - [Known Magento Bugs](#markdown-header-known-magento-bugs)


## Main Functionalities
Incert Voucher Module

## Installation
\* = in production please use the `--keep-generated` option


### Composer / Packagist
- Install the module composer by running `composer require incert/module-voucher`
- enable the module by running `php bin/magento module:enable Incert_Voucher`
- apply database updates by running `php bin/magento setup:upgrade`\*
- Flush the cache by running `php bin/magento cache:flush`
- 
## Configuration

The following settings configure the Incert Vouchers module in Magento. Each setting is described below along with its path and scope.

### Enabled
- **Description**: Enable or disable the Incert Vouchers module.
- **Scope**: Store view
- **Path**: `promo/incert/enabled`
- **Options**:
    - Yes
    - No

### API Url
- **Description**: If you choose the production mode you need to insert the api url provided by incert
- **Scope**: Website
- **Path**: `promo/incert/host`

### API Key
- **Description**: The API key used to authenticate with the Incert Vouchers service.
- **Scope**: Website
- **Path**: `promo/incert/api_key`

### Client ID
- **Description**: The client ID provided by the Incert Vouchers service.
- **Scope**: Website
- **Path**: `promo/incert/client_id`

### Client Secret
- **Description**: The client secret provided by the Incert Vouchers service. This is masked for security.
- **Scope**: Website
- **Path**: `promo/incert/client_secret`

### Booking Partner ID
- **Description**: The booking partner ID, used in case of a multi-store setup.
- **Scope**: Website
- **Path**: `promo/incert/booking_partner`

### Coupon Pattern
- **Description**: The pattern of the vouchers that should be checked. For example, `/API.*/`. Leave empty to check all coupons if they are incert vouchers.
- **Scope**: Website
- **Path**: `promo/incert/regex`

### Allow Discount on Vouchers
- **Description**: Allows the application of discounts for Magento Sales Rules.
- **Scope**: Website
- **Path**: `promo/incert/allow_discount`
- **Options**:
    - Yes
    - No



## Product Attributes
The module creates some product attributes to control the incert product type and have some additional information.

### Incert Voucher Product Type Attributes
- incert_product_id: Invisible attribute to connect the information of a Magento product to an Incert product (only applied to Incert Vouchers)
- incert_product_info:  Invisible attribute to store additional Incert product info (only applied to Incert Vouchers)

### Simple and Configurable Product Attributes
- Do not apply Incert voucher: Attribute to prevent redemption of an incert voucher to this product


## Product Type
The module create an own product type. If you want to create a product in Magento you should do the following.
- Go to the product overview (Catalog -> Products) in adminhtml
- Click on the small arrow next to "Add Product"
- Click on the "Incert Vouchers" option
- Select the provided Incert Voucher.
  - If there is no option please contact Incert to give you access to the voucher buy shop
- The information stored in your Incert Interface will be used to prepopulate the coupon. 
- Now you can freely choose change / enhance the information. 
  - If no Image is provided in Magento the image from Incert will be used
  - The price of the product is the value a customer is able to buy
  - The product is diabled by default, so change the product to your needs



## Known Magento Bugs
There are some core Magento Bugs that might cause problems when using the module
- ACSD-50814
  - May cause problems with Creditmemo Creation in Magento < 2.4.7
  - Fixed in Magento 2.4.7
  - https://github.com/magento/quality-patches/blob/b0065b97798f98f5f46a2ab2be8463f08cf01f06/patches/os/ACSD-50814_2.4.6.patch
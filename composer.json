{"name": "copex/project-community-edition", "license": ["OSL-3.0", "AFL-3.0"], "type": "project", "version": "2.4.5-p3", "description": "Magento 2 CE Skeleton", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://copex.io/"}], "repositories": {"xtento": {"type": "composer", "url": "https://repo.xtento.com"}, "mirasvit-fpc2": {"type": "composer", "url": "https://64659:<EMAIL>/64659:ISUZ2PL69Q/"}, "magepal": {"type": "composer", "url": "https://composer.magepal.com/"}, "mastercard-module-simplifycommerce": {"type": "git", "url": "https://github.com/arion-p/simplify-magento-module.git"}, "review-api": {"type": "git", "url": "https://github.com/jg-development/magento2-review-api.git"}, "0": {"type": "composer", "url": "https://repo.copex.io/rottner-m2-d02d2c62e8d824dd20fe3e644341c9bf"}, "1": {"type": "composer", "url": "https://repo.magento.com/"}, "3": {"type": "vcs", "url": "https://github.com/experius/Magento-2-Mo<PERSON>le-PageNotFound.git"}, "4": {"type": "vcs", "url": "https://github.com/PHOENIX-MEDIA/Magento2-BankPayment.git"}, "5": {"type": "vcs", "url": "https://github.com/CopeX/Magento2-CashOnDelivery.git"}, "6": {"type": "vcs", "url": "https://github.com/Strategery-Inc/Magento2-InfiniteScroll"}, "9": {"type": "vcs", "url": "https://github.com/aitoc/magento-2-core"}, "10": {"type": "vcs", "url": "https://github.com/aitoc/magento-2-google-customer-reviews"}, "11": {"type": "vcs", "url": "https://github.com/CopeX/VatUrl.git"}, "12": {"type": "vcs", "url": "https://github.com/CopeX/CustomOrderNumber.git"}, "13": {"type": "vcs", "url": "https://github.com/PrakashThapa/magento2-language-de-ch.git"}, "amasty": {"type": "composer", "url": "https://composer.amasty.com/community/"}, "wyomind": {"type": "composer", "url": "https://repo.wyomind.com"}, "redchamps": {"type": "composer", "url": "https://repo.redchamps.com/repositories/<EMAIL>-dtPrCBaoGrJPKmtunebHvdS3XMZfC9Wi"}, "magmodules": {"type": "composer", "url": "https://packages.magmodules.eu/"}, "copex": {"type": "composer", "url": "https://repo.copex.io/copex-59a59c4c569285759e83e34fa93f227d"}, "hyva": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/metall-im-garten-com/"}}, "require": {"magento/product-community-edition": "2.4.6-p9", "firegento/magesetup2": "^1.0", "smile/elasticsuite": "~2.11", "smile/module-elasticsuite-cms-search": "^2.2.7", "splendidinternet/mage2-locale-de-de": "^1.16", "avstudnitz/scopehint2": "^1", "magepal/magento2-googletagmanager": "^2.7", "magepal/magento2-gmailsmtpapp": "^2.6", "olegkoval/magento2-regenerate-url-rewrites": "^v1.4.1", "magepal/magento2-customeraccountlinksmanager": "^1.2", "experius/module-wysiwygdownloads": "^1.2.0", "experius/module-emailcatcher": "^4.0", "experius/module-pagenotfound": "^1.4", "fooman/emailattachments-m2": "^3.3.1", "phoenix/module-bankpayment": "v1.1.0", "phoenix/module-cashondelivery": "dev-master", "mageplaza/magento-2-hungarian-language-pack": "dev-master", "mageplaza/magento-2-french-language-pack": "dev-master", "mageplaza/magento-2-slovak-language-pack": "dev-master", "bratucornel/magento-2-romanian-language-pack": "dev-master", "catgento/module-admin-activity": "^1", "ethanyehuda/magento2-cronjobmanager": "^v2.1", "strategery/magento2-infinitescroll": "^0.6.0", "copex/module-massassigncategory": "^1.0.1", "raiym/instagram-php-scraper": "*", "mollie/magento2": "^2", "copex/import-m2": "<=0.4.8", "swissup/firecheckout": "~1.30", "owebia/magento2-module-advanced-shipping": "^2.8.4", "cweagans/composer-patches": "^1.6", "mageworx/module-seosuiteultimate": "^2.31.0", "aitoc/core": "dev-master as 1.0.8", "aitoc/google-customer-reviews": "dev-master", "mageplaza/magento-2-czech-language-pack": "dev-master", "community-engineering/language-nl_nl": "dev-master", "mageplaza/module-backend-reindex": "^1.0", "creativestyle/magesuite-magepack": "^1.1", "swissup/gdpr": "~1.3", "wyomind/paymentmethodrules": "~4", "amasty/module-advanced-permissions": "^1.4.17", "amasty/promo": "^2.9.8", "amasty/module-store-locator": "^3", "copex/module-prg": "<=1.0.13", "magefan/module-translation": "^2.0", "magenuts/module-custom-order-number": "dev-master", "interactivated/customerreview": "^2.1", "wyomind/datafeedmanager": "^14.0", "redchamps/module-extra-sorting-options": "^1.0", "m2epro/magento2-extension": "^1.14", "ampersand/magento2-disable-stock-reservation": "^1.0", "swissup/module-pagespeed": "^1.4", "amzn/amazon-pay-magento-2-module": "^5.9", "heureka/overeno-zakazniky": "^4.0", "mageplaza/magento-2-polish-language-pack": "dev-master", "magmodules/m2-alternate-hreflang": "^1.3", "swissup/module-checkout-fields": "^1.6", "reessolutions/db-override": "*", "yireo/magento2-webp2": "^0.13", "mageplaza/module-call-for-price": "^v4", "customgento/module-admin-payment-m2": "^1.1", "magefan/module-wysiwyg-advanced": "^2.0", "redchamps/module-clean-admin-menu": "^1.1", "doctrine/annotations": "^1", "mastercard/module-simplifycommerce": "dev-m247-compat", "magepal/magento2-google-analytics4": "^1.7", "mageplaza/magento-2-croatian-language-pack": "dev-master", "symfonysi/magento2-sl-si": "^1.0", "staempfli/magento2-language-de-ch": "dev-master", "hyva-themes/magento2-theme-module": "^1.3", "hyva-themes/magento2-reset-theme": "^1.1", "hyva-themes/magento2-graphql-tokens": "^1.0", "hyva-themes/magento2-email-module": "^1.0", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-compat-module-fallback": "^1.1", "hyva-themes/magento2-theme-fallback": "^1.0", "hyva-themes/magento2-luma-checkout": "^1.1", "hyva-themes/magento2-payment-icons": "^1.0", "hyva-themes/magento2-cms-tailwind-jit": "^1.1", "hyva-themes/magento2-smile-elasticsuite": "^1.2", "hyva-themes/magento2-magepal-googletagmanager": "^1.0", "hyva-themes/magento2-mageworx-htmlsitemap": "^1.1", "hyva-themes/magento2-mageworx-seomarkup": "^1.0", "yireo/magento2-webp2-for-hyva": "^1.0", "hyva-themes/magento2-magepal-google-analytics4": "^1.0", "mirasvit/module-cache-warmer": "*", "xtento/productexport": "^2.16", "copex/module-performance-tweaks": "^1.0", "hyva-themes/magento2-amasty-storelocator": "^1.0", "hyva-themes/magento2-amasty-promo": "dev-main", "copex/module-landing-page-parameter": "^1.0", "copex/module-cartdiscountpercentage": "^1.1", "divante/magento2-review-api": "^1.7", "yireo/magento2-disable-csp": "^1.0", "m2e/otto-adobe-commerce": "^2.0"}, "require-dev": {"yireo/magento2-whoops": "*", "mage2tv/magento-cache-clean": "*", "magepal/magento2-preview-checkout-success-page": "*", "lusitanian/oauth": "~0.8.10", "pdepend/pdepend": "2.*", "magento/magento-coding-standard": "*", "smile/module-debug-toolbar": "^7.0", "salecto2/magento2-mediastoragesync": "^1.0"}, "replace": {"braintree/braintree_php": "*", "braintree/braintree": "*", "paypal/module-braintree": "*", "magento/module-adobe-ims": "*", "magento/module-admin-adobe-ims": "*", "magento/module-admin-adobe-ims-two-factor-auth": "*", "magento/module-adobe-ims-api": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-configurable-import-export": "*", "magento/module-sampledata": "*", "shopialfb/facebook-module": "*", "temando/module-shipping-m2": "*", "dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "temando/module-shipping": "*", "vertex/product-magento-module": "*", "vertex/module-tax": "*", "vertex/sdk": "*", "vertexinc/product-magento-module": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*", "magento/google-shopping-ads": "*", "magento/module-advanced-pricing-import-export": "*", "magento/module-amqp": "*", "magento/module-amqp-store": "*", "magento/module-analytics": "*", "magento/module-authorizenet": "*", "magento/module-authorizenet-acceptjs": "*", "magento/module-authorizenet-cardinal": "*", "magento/module-bundle-import-export": "*", "magento/module-catalog-analytics": "*", "magento/module-cardinal-commerce": "*", "magento/module-customer-analytics": "*", "magento/module-cybersource": "*", "magento/module-dhl": "*", "magento/module-downloadable-import-export": "*", "magento/module-eway": "*", "magento/module-elasticsearch6": "*", "magento/module-elasticsearch7": "*", "magento/module-fedex": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-grouped-import-export": "*", "magento/module-marketplace": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-sample-data": "*", "magento/module-signifyd": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-tax-import-export": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-version": "*", "magento/module-wishlist-analytics": "*", "magento/module-worldpay": "*", "magento/module-authorizenet-graph-ql": "*", "magento/module-braintree-graph-ql": "*", "magento/module-bundle-graph-ql": "*", "magento/module-catalog-cms-graph-ql": "*", "magento/module-catalog-customer-ql": "*", "magento/module-catalog-customer-graph-ql": "*", "magento/module-catalog-inventory-graph-ql": "*", "magento/module-catalog-url-rewrite-graph-ql": "*", "magento/module-checkout-agreements-graph-ql": "*", "magento/module-cms-graph-ql": "*", "magento/module-cms-url-rewrite-graph-ql": "*", "magento/module-configurable-product-graph-ql": "*", "magento/module-customer-balance-graph-ql": "*", "magento/module-customer-downloadable-graph-ql": "*", "magento/module-directory-graph-ql": "*", "magento/module-downloadable-graph-ql": "*", "magento/module-gift-card-account-graph-ql": "*", "magento/module-gift-card-graph-ql": "*", "magento/module-grouped-product-graph-ql": "*", "magento/module-paypal-graph-ql": "*", "magento/module-related-product-graph-ql": "*", "magento/module-reward-graph-ql": "*", "magento/module-rma-graph-ql": "*", "magento/module-sales-graph-ql": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-store-graph-ql": "*", "magento/module-swatches-graph-ql": "*", "magento/module-tax-graph-ql": "*", "magento/module-theme-graph-ql": "*", "magento/module-url-rewrite-graph-ql": "*", "magento/module-vault-graph-ql": "*", "magento/module-weee-graph-ql": "*", "magento/module-wishlist-graph-ql": "*", "magento/module-aws-s3-page-builder": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-two-factor-auth": "*", "magefan/module-admin-user-guide": "*", "magefan/module-crowdin-integration": "*"}, "config": {"use-include-path": true, "http-basic": {"repo.wyomind.com": {"username": "go_rottner_services", "password": "QqC4KlZV"}, "repo.redchamps.com": {"username": "RkIXo16HMUDYAKLXxu4WASILzmPS", "password": "OMRKsT5QD9uNJ1Y2T5nHqPtzmeu"}, "packages.magmodules.eu": {"username": "2igwrAgwv2kfGckD5A6Uk3KgDM5q4awF", "password": "Fclgxs5U1ULtuNPHhqPCUvBbR0pqXF5H"}}, "allow-plugins": {"magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "laminas/laminas-dependency-plugin": true, "cweagans/composer-patches": true, "magento/composer-dependency-version-audit-plugin": false, "dealerdirect/phpcodesniffer-composer-installer": true}}, "autoload": {"psr-4": {"Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\": "app/code/Magento/"}, "psr-0": {"": "app/code/"}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**", ".giti<PERSON>re", "vendor/wikimedia/less.php"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/"}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"magento-force": "override", "magento-deploy-ignore": {"*": ["/pub/.htaccess", ".giti<PERSON>re", "/pub/media/.htaccess", "/pub/media/custom_options/.htaccess", "/pub/media/customer/.htaccess", "/pub/media/customer_address/.htaccess", "/pub/media/downloadable/.htaccess", "/pub/media/import/.htaccess", "/pub/media/sitemap/.htaccess", "/pub/media/theme_customization/.htaccess"]}, "composer-exit-on-patch-failure": true, "patches": {"smile/elasticsuite": {"autocomplete_order": "config/patches/smile/elasticsuite/autocomplete_order.patch", "digit-to-fix": "config/patches/smile/elasticsuite/digit-to-fix.patch"}, "amasty/module-store-locator": {"router-sort-order": "config/patches/amasty/storelocator/router-order.patch"}, "amasty/module-advanced-permissions": {"norule": "config/patches/amasty/module-advanced-permissions/norule.patch"}, "amasty/promo": {"wrong_tax_in_minicart_fix": "config/patches/amasty/promo/wrong_tax.patch"}, "wyomind/datafeedmanager": {"same-images": "config/patches/wyomind/datafeedmanager/images.patch"}, "mageworx/module-seobase": {"layer": "config/patches/mageworx/module-seobase/layer.patch"}, "mageworx/module-seoextended": {"layer": "config/patches/mageworx/module-seoextended/layer.patch"}, "owebia/magento2-module-advanced-shipping": {"php81": "config/patches/owebia/magento2-module-advanced-shipping/php81.patch"}, "magefan/module-community": {"nohome": "config/patches/magefan/module-community/nohome.patch"}, "amzn/amazon-pay-magento-2-module": {"nohome": "config/patches/amzn/amazon-pay-magento-2-module/key_update.patch"}, "mageplaza/module-call-for-price": {"nohome": "config/patches/mageplaza/module-call-for-price/product_not_found.patch", "zend_json_serialize": "config/patches/mageplaza/module-call-for-price/zend_patch.patch"}, "swissup/module-checkout-registration": {"no_cart": "config/patches/swissup/module-checkout-registration/no_cart.patch"}, "magento/framework": {"38214": "config/patches/magento/framwork/38214.patch", "cookie_size": "config/patches/magento/framwork/cookie_size.patch"}, "magento/module-theme": {"welcome_php82": "config/patches/magento/module-theme/welcome_php82.patch"}, "phoenix/module-bankpayment": {"php8.1": "config/patches/phoenix/module-bankpayment/php8.1.patch"}, "copex/import-m2": {"m246": "config/patches/copex/import-m2/m246.patch", "helperm246": "config/patches/copex/import-m2/helper246.patch"}, "copex/module-cartdiscountpercentage": {"performance": "config/patches/copex/module-cartdiscountpercentage/performance.patch"}}}}
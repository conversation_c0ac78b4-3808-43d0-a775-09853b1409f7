{"name": "copex/project-community-edition", "license": ["OSL-3.0", "AFL-3.0"], "type": "project", "version": "2.4.7", "description": "Magento 2 Open Source Skeleton", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://copex.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://copex.io/"}], "repositories": {"copex": {"type": "composer", "url": "https://packages.copex.io"}, "magento": {"type": "composer", "url": "https://repo.magento.com/"}, "hyva": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/metall-im-garten-com/"}, "swissup": {"type": "composer", "url": "https://repo.copex.io/swissuplabs-5e97661bbf255ff67300ccc98b5a4557"}, "copex_msp": {"type": "vcs", "url": "https://github.com/CopeX/m2-MSP_DevTools.git"}}, "require": {"magento/product-community-edition": "2.4.7-p4", "creativestyle/composer-plugin-patchset": "^3", "firegento/magesetup2": "^1.0", "splendidinternet/mage2-locale-de-de": "^1.16", "ethanyehuda/magento2-cronjobmanager": "^2", "avstudnitz/scopehint2": "^1", "yireo/magento2-googletagmanager2": "^3.3", "olegkoval/magento2-regenerate-url-rewrites": "^v1.4.1", "experius/module-wysiwygdownloads": "^1.2.0", "magepal/magento2-customeraccountlinksmanager": "^1.1", "experius/module-emailcatcher": "^4.0", "opengento/module-gdpr": "^4.4", "n98/magerun2-dist": "*", "magento/module-bundle-sample-data": "100.4.*", "magento/module-widget-sample-data": "100.4.*", "magento/module-theme-sample-data": "100.4.*", "magento/module-catalog-sample-data": "100.4.*", "magento/module-customer-sample-data": "100.4.*", "magento/module-cms-sample-data": "100.4.*", "magento/module-catalog-rule-sample-data": "100.4.*", "magento/module-sales-rule-sample-data": "100.4.*", "magento/module-review-sample-data": "100.4.*", "magento/module-sales-sample-data": "100.4.*", "magento/module-grouped-product-sample-data": "100.4.*", "magento/module-downloadable-sample-data": "100.4.*", "magento/module-msrp-sample-data": "100.4.*", "magento/module-configurable-sample-data": "100.4.*", "magento/module-product-links-sample-data": "100.4.*", "magento/module-wishlist-sample-data": "100.4.*", "magento/module-swatches-sample-data": "100.4.*", "magento/sample-data-media": "100.4.*", "magento/module-offline-shipping-sample-data": "100.4.*", "copex/module-plc": "*", "copex/module-prg": "*", "copex/module-massassigncategory": "*", "copex/category-headline-m2": "*", "copex/module-vat-validator-austria": "dev-main", "copex/module-vat-validation-frontend": "^1.2", "magento/module-sample-data": "^100.4", "copex/module-product-feed": "^1.0", "copex/module-html-head-enricher": "^1.0", "copex/module-product-detail-seo-link": "^1.0", "copex/theme-hyva-green": "dev-main", "copex/module-snowdog-hyva-menu": "^1.1", "copex/theme-luma-green-checkout": "^1.0", "copex/module-cartdiscountpercentage": "^1.0", "copex/module-badges": "^1.0", "copex/module-hyva-swiper": ">=1.3", "copex/module-hyvavideo": "^0.1.0", "copex/module-inlinefontloader": "^1.0", "copex/module-lowestpricehistory": "^1.0", "copex/module-lof-reviews": "^1.0", "copex/module-hyva-add-to-cart-success-popup": "^1.0", "magento-hackathon/module-bestsellers-sorting": "^1.0", "copex/module-sorting-options": "^1.0", "copex/module-product-attachment": "^1.1", "copex/module-cms-breadcrumbs": "^1.1", "copex/module-exitintent": "^1.0", "copex/module-next-gen-images": "^1.0", "hyva/module-swissup-gdpr": "^1.0", "copex/module-yireogtm-order-address-on-success": "^1.0", "copex/module-page-builder-banner": "^1.0", "copex/module-page-builder-accordion": "^1.0", "copex/module-page-builder-reverse-on-mobile": "^1.1", "copex/module-voice-search": "^1.0", "copex/module-catalog-search-spam-filter": "^1.0", "copex/module-resource-hints": "^1.2", "copex/module-product-list-product-attributes-for-copex-green": "^1.0", "hyva-themes/magento2-firegento-magesetup": "^1.0", "copex/module-performance-tweaks": "^1.0", "copex/module-advanced-elasticsuite-catalog": "^1.0", "smile/elasticsuite": "^2.11.11", "copex/module-core": "^1.0", "incert/module-voucher": "^1.0", "magento/module-tax-sample-data": "100.4.*"}, "require-dev": {"yireo/magento2-whoops": "*", "mage2tv/magento-cache-clean": "*", "magepal/magento2-preview-checkout-success-page": "*", "lusitanian/oauth": "~0.8.10", "pdepend/pdepend": "2.*", "deployer/deployer": "^7.0", "phoenix-media/magento2-mediastoragesync": "^1.2", "msp/devtools": "^1.2", "magento/magento-coding-standard": "^31.0", "phpunit/phpunit": "^9.5", "allure-framework/allure-phpunit": "*"}, "replace": {"adobe-commerce/adobe-ims-metapackage": "*", "magento/module-adobe-ims": "*", "magento/module-adobe-ims-api": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-configurable-import-export": "*", "shopialfb/facebook-module": "*", "temando/module-shipping-m2": "*", "braintree/braintree_php": "*", "braintree/braintree": "*", "paypal/module-braintree": "*", "dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "klarna/m2-payments": "*", "klarna/module-core": "*", "klarna/module-kp": "*", "klarna/module-ordermanagement": "*", "temando/module-shipping": "*", "vertex/product-magento-module": "*", "vertex/module-tax": "*", "vertex/sdk": "*", "vertexinc/product-magento-module": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*", "magento/google-shopping-ads": "*", "magento/module-advanced-pricing-import-export": "*", "magento/module-amqp": "*", "magento/module-amqp-store": "*", "magento/module-authorizenet": "*", "magento/module-authorizenet-acceptjs": "*", "magento/module-authorizenet-cardinal": "*", "magento/module-bundle-import-export": "*", "magento/module-catalog-analytics": "*", "magento/module-cardinal-commerce": "*", "magento/module-customer-analytics": "*", "magento/module-customer-import-export": "*", "magento/module-cybersource": "*", "magento/module-dhl": "*", "magento/module-downloadable-import-export": "*", "magento/module-eway": "*", "magento/module-fedex": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-grouped-import-export": "*", "magento/module-marketplace": "*", "magento/module-multishipping": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-send-friend": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-re-captcha-send-friend": "*", "magento/module-signifyd": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-tax-import-export": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-version": "*", "magento/module-wishlist-analytics": "*", "magento/module-worldpay": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-admin-analytics": "*", "landofcoder/module-all": "*", "magento/module-two-factor-auth": "*", "magento/module-admin-adobe-ims": "*", "magefan/module-admin-user-guide": "*", "magento/module-persistent": "*", "mageplaza/module-core": "*", "magento/module-application-performance-monitor": "*", "magento/module-application-performance-monitor-new-relic": "*", "magento/module-payment-services-saas-export": "*"}, "config": {"use-include-path": true, "allow-plugins": {"laminas/laminas-dependency-plugin": true, "magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "magento/composer-root-update-plugin": true, "magento/composer-dependency-version-audit-plugin": false, "creativestyle/composer-plugin-patchset": true, "php-http/discovery": true}}, "autoload": {"psr-4": {"Magento\\": "app/code/Magento/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\Framework\\": "lib/internal/Magento/Framework/", "Zend\\Mvc\\Controller\\": "setup/src/Zend/Mvc/Controller/"}, "psr-0": {"": "app/code/"}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**", ".giti<PERSON>re"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/"}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "magento-deploy-ignore": {"*": ["/pub/.htaccess", "/.giti<PERSON>re"]}, "patchset": {"magento/framework": [{"description": "38214", "filename": "config/patches/magento/framework/38214.patch"}]}}, "scripts": {"post-install-cmd": ["([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/)"], "post-update-cmd": ["([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/)"]}}
<?php


namespace CopeX\Plc\Plugin\Magento\PageCache\Model\App\Response;

class HttpPlugin
{

    public function aroundBeforeSendResponse(
        \Magento\PageCache\Model\App\Response\HttpPlugin $subject,
        \Closure $proceed,
        \Magento\Framework\App\Response\Http $originSubject
    ) {
        /**
         * Fix because \Magento\Framework\App\Response\Http\FileFactory
         * does not implement \Magento\Framework\App\PageCache\NotCacheableInterface
         * in 2.3.3
         *
         */
        if (!$originSubject->headersSent()) {
            $proceed($originSubject);
        }
    }
}
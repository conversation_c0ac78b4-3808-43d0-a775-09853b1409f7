<?php

namespace Incert\Voucher\Plugin\Sales;

use Incert\Voucher\Helper\Voucher;
use Incert\Voucher\Model\ResourceModel\Purchase\CollectionFactory;
use Magento\Sales\Model\Order\Item;
use Magento\Sales\Model\Order\CreditmemoValidator as Validator;

class CreditmemoValidator
{

    private Voucher $voucher;

    private CollectionFactory $collectionFactory;

    private $memoizedResult = [];

    public function __construct(Voucher $voucher, CollectionFactory $collectionFactory)
    {
        $this->voucher = $voucher;
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * @param Validator  $subject
     * @param bool       $result
     * @param Item       $item
     * @param array|null $qtys
     * @param array|null $invoiceQtysRefundLimits
     * @return bool
     */
    public function afterCanRefundItem(
        Validator $subject,
        bool $result,
        Item $item,
        ?array $qtys = [],
        ?array $invoiceQtysRefundLimits = []
    ): bool {
        if ($item->getProductType() === \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE && $result) {
            $currentInvoiceItem = $this->getInvoiceItemByOrderItem($item);
            if ($currentInvoiceItem) {
                $qtyToRefund = $qtys[$item->getId()] ?? $item->getQtyToRefund();
                $boughtVoucherProducts = $this->getIncertProductsFromInvoice($currentInvoiceItem->getInvoice());
                $this->updateVoucherProductDataFromIncertApi($boughtVoucherProducts);
                $currentVoucherProducts = $boughtVoucherProducts->getItemsByColumnValue('invoice_item_id',
                    $currentInvoiceItem->getId());
                $item->setConnectedIncertProducts($currentVoucherProducts);

                $allowedToCancel = array_filter($currentVoucherProducts, function ($voucherProduct) {
                    return $voucherProduct->getIncertData()['cancel_allowed'] === true;
                });
                $allowedToCancelCount = count($allowedToCancel);
                if ($allowedToCancelCount < $qtyToRefund) {
                    $item->setAllowedQtyToRefund($allowedToCancelCount);
                    $item->setOriginQtyToRefund($qtyToRefund);
                }
            }
        }
        return $result; //If no invoices are booked, everything is refundable
    }

    /**
     * @param  $invoice
     * @return \Incert\Voucher\Model\ResourceModel\Purchase\Collection
     */
    private function getIncertProductsFromInvoice($invoice
    ): \Incert\Voucher\Model\ResourceModel\Purchase\Collection {
        /** @var \Incert\Voucher\Model\ResourceModel\Purchase\Collection $collection */
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('invoice_id', $invoice->getId());
        return $collection;
    }

    /**
     * @param Item $item
     * @return \Magento\Sales\Api\Data\InvoiceItemInterface|null
     */
    public function getInvoiceItemByOrderItem(
        Item $item
    ): ?\Magento\Sales\Api\Data\InvoiceItemInterface {
        $invoices = $item->getOrder()->getInvoiceCollection();
        foreach ($invoices as $invoice) {
            /** @var \Magento\Sales\Model\Order\Invoice $invoice */
            foreach ($invoice->getItems() as $invoiceItem) {
                if ($invoiceItem->getOrderItemId() === $item->getItemId()) {
                    return $invoiceItem;
                }
            }
        }
        return null;
    }

    /**
     * @param mixed $currentVoucherProduct
     * @return \Incert\Client\Model\Shop\Response\Order\CanCancel
     */
    public function getApiResultByIncertOrderId($incertOrderId): \Incert\Client\Model\Shop\Response\Order\CanCancel
    {
        if (!isset($this->memoizedResult[$incertOrderId])) {
            $this->memoizedResult[$incertOrderId] = $this->voucher->canCancelVoucherProduct($incertOrderId);
        }
        /** @var \Incert\Client\Model\Shop\Response\Order\CanCancel $apiResult */
        $apiResult = $this->memoizedResult[$incertOrderId];
        return $apiResult;
    }

    private function updateVoucherProductDataFromIncertApi($voucherProducts)
    {
        foreach ($voucherProducts as $voucherProduct) {
            $apiResult = $this->getApiResultByIncertOrderId($voucherProduct->getIncertOrderId());
            foreach ($apiResult->getProducts() as $products) {
                foreach ($products as $product) {
                    if ($product['code'] === $voucherProduct->getVoucherCode()) {
                        $voucherProduct->setIncertData($product);
                    }
                }
            }
        }
    }
}
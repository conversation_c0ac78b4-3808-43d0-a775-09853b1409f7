<?php

namespace CopeX\VatValidator\Plugin\Sales\Frontend;

use CopeX\VatValidator\Model\Validator;
use Magento\Customer\Helper\Address as CustomerAddress;
use Magento\Customer\Model\Address\AbstractAddress;
use Magento\Framework\Event\Observer;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Address;
use Magento\Sales\Observer\Frontend\AddVatRequestParamsOrderComment;

class PreventOriginalComment
{
    private CustomerAddress $customerAddressHelper;

    /**
     * @param CustomerAddress $customerAddressHelper
     */
    public function __construct(CustomerAddress $customerAddressHelper)
    {
        $this->customerAddressHelper = $customerAddressHelper;
    }


    /**
     * Retrieve sales address (order or quote) on which tax calculation must be based on
     *
     * @param Order $order
     * @param \Magento\Store\Model\Store|string|int|null $store
     * @return Address|null
     */
    protected function _getVatRequiredSalesAddress($order, $store = null)
    {
        $configAddressType = $this->customerAddressHelper->getTaxCalculationAddressType($store);
        return $configAddressType === AbstractAddress::TYPE_SHIPPING
            ? $order->getShippingAddress()
            : $order->getBillingAddress();
    }

    /**
     * @param AddVatRequestParamsOrderComment $subject
     * @param callable                        $proceed
     * @param Observer                        $observer
     * @return void
     */
    public function aroundExecute(AddVatRequestParamsOrderComment $subject, callable $proceed, Observer $observer): void
    {
        $orderInstance = $observer->getOrder();
        $orderAddress = $this->_getVatRequiredSalesAddress($orderInstance, $orderInstance->getStore()->getStoreCode());
        $vatRequestId = $orderAddress ? $orderAddress->getVatRequestId() : false;
        if (!$vatRequestId || strpos($vatRequestId, Validator::REQUEST_ID_PREFIX) === false) {
            $proceed($observer);
        }
    }
}
<?php

namespace Incert\Voucher\Plugin\Sales;

use Magento\Sales\Model\Order;

class OrderPlugin
{
    /**
     * @param Order $subject
     * @return array
     */
    public function beforeCanCreditmemo(Order $order): array
    {
        if ($order->getBaseTotalPaid() - $order->getBaseTotalRefunded() < PHP_FLOAT_EPSILON) {
            if (abs($order->getBaseVoucherInvoiced() - $order->getBaseVoucherRefunded()) > PHP_FLOAT_EPSILON) {
                $order->setData('forced_can_creditmemo', true);
            }
        }
        return [];
    }
}
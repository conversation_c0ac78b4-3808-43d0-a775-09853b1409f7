<?php

namespace Incert\Voucher\Plugin\Sales\Order;

use Magento\Sales\Model\Order\Item;

class OrderItem
{
    /**
     * @param Item  $subject
     * @param float $result
     * @return float
     */
    public function afterGetQtyToRefund(Item $subject, float $result): float
    {
        $allowed = $subject->getAllowedQtyToRefund();
        if ($allowed) {
            return min($allowed, $result);
        }
        return $result;
    }
}
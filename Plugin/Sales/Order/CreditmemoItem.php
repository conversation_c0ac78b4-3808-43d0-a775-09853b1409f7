<?php

namespace Incert\Voucher\Plugin\Sales\Order;

use Magento\Sales\Model\Order\Creditmemo\Item;

class CreditmemoItem
{
    /**
     * @param Item $subject
     * @param bool $result
     * @return bool
     */
    public function afterIsLast(Item $subject, bool $result): bool
    {
        if($result && $subject->getOrderItem()->getProductType() ===  \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE){
            $orderItem = $subject->getOrderItem();
            if($orderItem->getAllowedQtyToRefund() < $orderItem->getOriginQtyToRefund()){
                return false;
            }
        }
        return $result;
    }

    public function beforeCalcRowTotal(Item $subject)
    {
        if($subject->getOrderItem()->getProductType() ===  \Incert\Voucher\Model\Product\Type\Voucher::TYPE_CODE){
            $orderItem = $subject->getOrderItem();
            if($orderItem->getAllowedQtyToRefund() && $subject->getQty() > $orderItem->getAllowedQtyToRefund()){
                throw new \Magento\Framework\Exception\LocalizedException(
                    __('Some vouchers are already applied and can not be refunded (%1[%2] maximum %3)', $orderItem->getName(), $orderItem->getSku(), $orderItem->getAllowedQtyToRefund())
                );
            }
        }
        return [];
    }
}
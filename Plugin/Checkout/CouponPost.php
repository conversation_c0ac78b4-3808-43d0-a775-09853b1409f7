<?php

namespace Incert\Voucher\Plugin\Checkout;

use Incert\Voucher\Helper\Config;
use Incert\Voucher\Helper\CouponCodes;
use Magento\Checkout\Model\Session;
use Magento\Framework\Escaper;
use Magento\Framework\Message\ManagerInterface;
use Magento\Quote\Model\QuoteRepository;

class CouponPost
{

    private CouponCodes $couponCodes;
    private Config $config;
    private QuoteRepository $quoteRepository;
    private Session $session;
    private ManagerInterface $manager;
    private Escaper $escaper;

    public function __construct(
        CouponCodes $couponCodes,
        Config $config,
        QuoteRepository $quoteRepository,
        Session $session,
        ManagerInterface $manager,
        Escaper $escaper
    ) {
        $this->couponCodes = $couponCodes;
        $this->config = $config;
        $this->quoteRepository = $quoteRepository;
        $this->session = $session;
        $this->manager = $manager;
        $this->escaper = $escaper;
    }

    /**
     * @param CouponPost $subject
     * @return array
     */
    public function beforeExecute(\Magento\Checkout\Controller\Cart\CouponPost $subject): array
    {
        if ($this->config->isEnabled()) {
            $couponCode = $subject->getRequest()->getParam('remove') == 1
                ? ''
                : trim($subject->getRequest()->getParam('coupon_code', ''));
            if ($couponCode) {
                $this->couponCodes->splitCouponCodes($couponCode ?? "", $this->config->getPattern());
                $magentoCoupons = implode(",", $this->couponCodes->getMagentoCouponCodes());
                $this->session->getQuote()->setCouponCode($magentoCoupons)->collectTotals();
                $subject->getRequest()->setParam('coupon_code', $magentoCoupons);
            }
        }
        return [];
    }

    public function afterExecute(\Magento\Checkout\Controller\Cart\CouponPost $subject, $result)
    {
        if ($this->config->isEnabled()) {
            $quote = $this->session->getQuote();
            if(!$this->couponCodes->getMagentoCouponCodes() && $this->couponCodes->getIncertVoucherStatus()){
                $this->manager->getMessages(true); //Clear messages if no magento coupons are applied
            }
            $this->addMessages();
            $quote->setCouponCode(implode(",", array_merge($this->couponCodes->getMagentoCouponCodes(), $this->couponCodes->getIncertVouchers())));
            $this->quoteRepository->save($quote);
        }
        return $result;
    }

    /**
     * @param array $incertVouchers
     * @param array $notValid
     * @return void
     */
    private function addMessages(): void
    {
        $notValid = array_diff($this->couponCodes->getPossibleVouchers(), array_keys($this->couponCodes->getIncertVoucherStatus()));
        foreach ($this->couponCodes->getIncertVoucherStatus() as $voucherCode => $statusDetail) {
            if($statusDetail->hasError()){
                $this->manager->addErrorMessage(
                    __(
                        sprintf($statusDetail->getErrorMessage($this->config->getStoreLanguage()),"%1"),
                        $this->escaper->escapeHtml($voucherCode)
                    )
                );
            } else {
                $this->manager->addSuccessMessage(
                    __(
                        'You used coupon code "%1".',
                        $this->escaper->escapeHtml($voucherCode)
                    )
                );
            }
        }
        if (count($notValid)) {
            foreach ($notValid as $couponCode) {
                $this->manager->addErrorMessage(
                    __(
                        'The coupon code "%1" is not valid.',
                        $this->escaper->escapeHtml($couponCode)
                    )
                );
            }
        }
    }
}
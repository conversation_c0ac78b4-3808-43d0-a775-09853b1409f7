<?php

namespace Incert\Voucher\Plugin;

use Incert\Voucher\Model\Product\Type\Voucher;
use Magento\Backend\Model\View\Result\RedirectFactory;
use Magento\Backend\Model\View\Result\Page;
use Magento\Catalog\Controller\Adminhtml\Product\NewAction;

class NewProduct
{

    private RedirectFactory $redirectFactory;

    public function __construct(RedirectFactory $redirectFactory)
    {
        $this->redirectFactory = $redirectFactory;
    }

    /**
     * @param NewAction $subject
     * @param Page      $result
     * @return
     */
    public function afterExecute(NewAction $subject, $result)
    {
        if($result instanceof Page){
            if( $subject->getRequest()->getParam('type') == Voucher::TYPE_CODE){
                return $this->redirectFactory->create()->setPath('incert_voucher/product/create');
            }
        }
        return $result;
    }
}
<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\Core\Console\Command;

use CopeX\Core\Model\Registry;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class Renew extends Command
{

    private const NAME_ARGUMENT = "name";
    private Registry $registry;

    public function __construct(Registry $registry, ?string $name = null)
   {
       parent::__construct($name);
       $this->registry = $registry;
   }

    /**
     * @inheritdoc
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ): int {
        $name = $input->getArgument(self::NAME_ARGUMENT) ?? "";
        $modules = $this->registry->getModules();
        if ($name) {
            if(isset($modules[$name])){
                $modules = [$modules[$name]];
            }
            else {
                $output->writeln("$name not found!");
                return Command::FAILURE;
            }
        }
        foreach($modules as $module) {
            try{
                $module->renew();
                $output->writeln("{$module->getModule()} licence renewed!");
            }catch (\Exception $exception){
                $output->writeln($exception->getMessage());
            }
        }

        return Command::SUCCESS;
    }

    /**
     * @inheritdoc
     */
    protected function configure(): void
    {
        $this->setName("copex:core:renew");
        $this->setDescription("Renew licenses for modules");
        $this->setDefinition([
            new InputArgument(self::NAME_ARGUMENT, InputArgument::OPTIONAL, "Module Name"),
        ]);
        parent::configure();
    }
}

# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## 1.0.9 (2025-05-15)
### Fixed
- correct calculation of refunded creditmemo voucher amount

## 1.0.8 (2025-05-15)
### Fixed
- Voucher Code was displayed in shipping method

## 1.0.7 (2025-04-07)
### Improvement
- Better ACL Rules

## 1.0.6 (2024-11-18)
### Improvement
- Added default attribute set for voucher products

## 1.0.5 (2024-10-16)
### Improvement
- Possibility to creditmemo already bought voucher products, if they are not redeemed already

## 1.0.4 (2024-08-27)
### Improvement
- Fixes in configuration
- Added a block on how to apply the vouchers in frontend
- Add error message if currency is different
- Fix when buying vouchers with qty > 1

## 1.0.3 (2024-08-02)
### Fixed
- Bug when saving multiple voucher products
- Respect maximum product price in product type save

## 1.0.2 (2024-07-31)
### Fixed
- Download of images
- Multi language values are now stored in the correct store view

## 1.0.1 (2024-07-26)
### Improvement
- Show better error messages on coupon post
### Fixed
- Redemption of sponsoring vouchers
- Redemption of partly redeemable vouchers

## 1.0.0 (2024-07-03)
### Fixed
- First release of Incert Voucher Module for Magento 2
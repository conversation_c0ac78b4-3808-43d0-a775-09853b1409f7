<?php
namespace Incert\Voucher\Block\Sales\Order;

use Magento\Framework\View\Element\Template;
use Magento\Sales\Model\Order;

class Voucher extends Template
{
    /**
     * @var Order
     */
    protected $order;

    /**
     * @var \Magento\Framework\DataObject
     */
    protected $source;

    /**
     * Get data (totals) source model
     *
     * @return \Magento\Framework\DataObject
     */
    public function getSource()
    {
        return $this->source;
    }

    /**
     * Get order object
     *
     * @return \Magento\Framework\DataObject
     */
    public function getOrder()
    {
        return $this->order;
    }

    /**
     * Initialize all order totals relates with voucher
     *
     * @return \Incert\Voucher\Block\Sales\Order\Voucher
     */
    public function initTotals()
    {
        $parent = $this->getParentBlock();
        $this->order = $parent->getOrder();
        $this->source = $parent->getSource();

        if ($this->order->getVoucherAmount() != 0) {
            $voucher = new \Magento\Framework\DataObject(['code' => 'voucher', 'block_name' => $this->getNameInLayout()]);
            $this->getParentBlock()->addTotalBefore($voucher, 'grand_total');
        }
        return $this;
    }

    /**
     * @return array
     */
    public function getLabelProperties()
    {
        return $this->getParentBlock()->getLabelProperties();
    }

    /**
     * @return array
     */
    public function getValueProperties()
    {
        return $this->getParentBlock()->getValueProperties();
    }
}

<?php

namespace CopeX\Plc\Block\Adminhtml\Order\View;

use CopeX\Plc\Helper\Config;
use CopeX\Plc\Helper\Data;
use CopeX\Plc\Helper\Module;
use CopeX\Plc\Helper\Retouring;
use CopeX\Plc\Model\LabelFactory;
use CopeX\PLCUtils\Communicator;
use \Magento\Backend\Block\Template as Template;
use Magento\Framework\Locale\ResolverInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class Custom extends Template
{

    protected $_collection;
    protected $_scopeConfig;
    protected $_orderRepository;
    protected $_helper;
    protected $label;
    protected $returnHelper;
    /**
     * @var ResolverInterface
     */
    protected $localeResolver;
    /**
     * @var LabelFactory
     */
    protected $_labelFactory;
    private Config $config;
    private Module $moduleHelper;

    public function __construct(
        Template\Context $context,
        Data $helper,
        Retouring $returnHelper,
        LabelFactory $labelFactory,
        OrderRepositoryInterface $orderRepository,
        ResolverInterface $localeResolver,
        Config $config,
        Module $moduleHelper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->_labelFactory = $labelFactory;
        $this->_orderRepository = $orderRepository;
        $this->_helper = $helper;
        $this->returnHelper = $returnHelper;
        $this->localeResolver = $localeResolver;
        $this->config = $config;
        $this->moduleHelper = $moduleHelper;
    }

    protected function _toHtml()
    {
        if ($this->moduleHelper->isModuleEnabled()) {
            return parent::_toHtml();
        }
        return "";
    }


    /**
     * @return \CopeX\Plc\Model\ResourceModel\Label\Collection
     */
    public function getCollection()
    {
        return $this->_collection->load();
    }


    public function isAllowed()
    {
        return $this->_authorization->isAllowed('CopeX_Plc::create');
    }

    public function isReturnAllowed()
    {
        return $this->_authorization->isAllowed('CopeX_Plc::return');
    }

    public function isCancelAllowed()
    {
        return $this->_authorization->isAllowed('CopeX_Plc::cancel');
    }

    /**
     * @return bool
     */
    public function isSubmitEnabled($order)
    {
        $submitEnabled = false;
        $clientId = $this->config->getClientId($order->getStoreId());
        $orgUnitID = $this->config->getOrgUnitId($order->getStoreId());
        if ($clientId != null && $orgUnitID != null) {
            $submitEnabled = true;
        }
        return $submitEnabled;
    }

    /**
     * @param $order
     * @return mixed
     */
    public function getLabels($order)
    {
        if ($this->isSubmitEnabled($order)) {
            return $this->_helper->getLabelsByOrderId($order->getId(), \Magento\Framework\Api\SortOrder::SORT_ASC);
        }
        return false;
    }

    public function getLabel($orderId)
    {
        $this->label = $this->_helper->getLabelByOrderId($orderId);
        return $this->label;
    }

    /**
     * @return \Magento\Framework\App\Config\ScopeConfigInterface
     */
    public function getScopeConfig()
    {
        return $this->_scopeConfig;
    }

    public function getOrder($id)
    {
        return $this->_orderRepository->get($id);
    }

    public function getShippingCode($order)
    {
        $shippingMethod = $order->getShippingMethod();
        if($shippingMethod){
            return explode("_", $shippingMethod)[1];
        }
        return "";
    }

    public function getConfigMessage()
    {
        return Data::CONFIG_MESSAGE;
    }

    public function getHelper()
    {
        return $this->_helper;
    }

    /**
     * @return Config
     */
    public function getConfigHelper(){
        return $this->config;
    }

    public function getRedirectUrl()
    {
        $url = $this->_urlBuilder->getUrl();
        return $this->_urlBuilder->getRedirectUrl($url);
    }

    public function getPacketOptions($order)
    {
        $packetOptions =  $this->_helper->getPacketOptions();
        if ($this->getReturnHelper()->isReturnWithoutLabelAllowed($order->getStoreId())) {
            $packetOptions = array_replace($packetOptions, Communicator::getReturnOptions());
        }
        return $packetOptions;
    }

    /**
     * @return Retouring
     */
    public function getReturnHelper(): Retouring
    {
        return $this->returnHelper;
    }

    public function formatDate($date = null,
        $format = \IntlDateFormatter::SHORT,
        $showTime = true,
        $timezone = null){
        $date = $date instanceof \DateTimeInterface ? $date : new \DateTime($date);
        return $this->_localeDate->formatDateTime(
            $date,
            $format,
            $showTime ? $format : \IntlDateFormatter::NONE,
            $this->localeResolver->getLocale() ?? $this->localeResolver->getDefaultLocale()
        );
    }

    public function getLabelClasses($label){
        $classes = ['label'];
        if ($this->getReturnHelper()->isLabelReturn($label)) {
            $classes [] = "return";
        }
        $date = new \DateTime($label->getCreatedAt());
        $age = $date->diff($this->_localeDate->date());
        if ($age->days < 1 && $age->h < 2) {
            $classes [] = "new";
        }
        return implode(" ", $classes);
    }
}
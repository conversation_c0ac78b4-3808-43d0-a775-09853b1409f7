<?php

namespace Incert\Voucher\Block\Adminhtml\Sales\Order;

use Magento\Framework\View\Element\Template;

class Totals extends Template
{
    /**
     * Get totals source object
     *
     * @return \Magento\Sales\Model\Order
     */
    public function getSource()
    {
        return $this->getParentBlock()->getSource();
    }

    /**
     * Create the voucher totals summary
     *
     * @return $this
     */
    public function initTotals()
    {
        $voucherDiscountAmount = $this->getSource()->getVoucherAmount();
        $baseVoucherDiscountAmount = $this->getSource()->getBaseVoucherAmount();
        if ($voucherDiscountAmount == 0) {
            return $this;
        }

        $parentBlockName = $this->getParentBlock()->getNameInLayout();
        if ($parentBlockName == 'creditmemo_totals') {
            $label = __('Refunded to Voucher');
        } else {
            $label = __('Vouchers');
        }

        //@codingStandardsIgnoreLine
        $total = new \Magento\Framework\DataObject(
            [
                'code' => $this->getNameInLayout(),
                'label' => $label,
                'value' => $voucherDiscountAmount,
                'base_value' => $baseVoucherDiscountAmount
            ]
        );


        $this->getParentBlock()->addTotalBefore($total, 'grand_total');

        if ($parentBlockName == 'order_totals' && $this->getSource()->getVoucherRefunded() != 0) {
            //@codingStandardsIgnoreLine
            $totalRefunded = new \Magento\Framework\DataObject(
                [
                    'code' => $this->getNameInLayout().'_refunded',
                    'label' => __('Refunded to Voucher'),
                    'value' => $this->getSource()->getVoucherRefunded(),
                    'base_value' => $this->getSource()->getBaseVoucherRefunded()
                ]
            );

            $this->getParentBlock()->addTotalBefore($totalRefunded, 'grand_total');
        }
        return $this;
    }
}
